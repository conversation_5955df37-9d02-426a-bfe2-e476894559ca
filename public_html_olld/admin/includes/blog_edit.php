<?php
include '../includes/config.php';
session_start();

$id = $_GET['id'];
$sql = "SELECT * FROM blog_posts WHERE id = :id";
$stmt = $pdo->prepare($sql);
$stmt->execute([':id' => $id]);
$post = $stmt->fetch(PDO::FETCH_ASSOC);

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $title = $_POST['title'];
    $content = $_POST['content'];
    $short_description = $_POST['short_description'];  // short_description დამატება
    $category = $_POST['category'];
    $image = $_FILES['image']['name'] ? $_FILES['image']['name'] : $post['image'];

    if ($_FILES['image']['name']) {
        move_uploaded_file($_FILES['image']['tmp_name'], '../uploads/' . $image);
    }

    $sql = "UPDATE blog_posts SET title = :title, content = :content, short_description = :short_description, image = :image, category = :category WHERE id = :id";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([
        ':title' => $title, 
        ':content' => $content, 
        ':short_description' => $short_description,  // short_description მოთხოვნაში
        ':image' => $image, 
        ':category' => $category, 
        ':id' => $id
    ]);

    header("Location: blog.php");
    exit;
}
?>