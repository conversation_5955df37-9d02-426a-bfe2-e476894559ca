<?php
session_start();
include '../includes/config.php';
include 'connect.php';


$sql = "SELECT * FROM blog_posts";
$stmt = $pdo->prepare($sql);
$stmt->execute();
$posts = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Blog Posts</title>
    <link rel="stylesheet" href="css/styles.css">
</head>
<body>
    <!-- Sidebar -->
    <?php include 'includes/sidebar.php'; ?>
    
    <!-- Main Content -->
    <div class="main-content">
        <div class="navbar">
            <h2>Blog Posts</h2>
            <div class="menu-icon">
                <span>&#9776;</span>
            </div>
        </div>

        <div class="content">
    <div class="blog-header">
        <h3>Manage Blog Posts</h3>
        <!-- Add new blog post button -->
        <a href="blog_add.php" class="btn add-post-btn">Add New Post</a>
    </div>
    
    <!-- Blog Posts Table -->
    <table class="user-table">
        <thead>
            <tr>
                <th>Title</th>
                <th>Category</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($posts as $post): ?>
            <tr>
                <td><?php echo htmlspecialchars($post['title']); ?></td>
                <td><?php echo htmlspecialchars($post['category']); ?></td>
                <td>
                    <a href="blog_edit.php?id=<?php echo $post['id']; ?>" class="btn">Edit</a>
                    <a href="includes/blog_delete.php?id=<?php echo $post['id']; ?>" class="btn">Delete</a>
                </td>
            </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
</div>

    </div>

    <script src="js/script.js"></script>
</body>
</html>
