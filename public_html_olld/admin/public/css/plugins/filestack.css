/*!
 * froala_editor v4.5.0 (https://www.froala.com/wysiwyg-editor)
 * License https://froala.com/wysiwyg-editor/terms/
 * Copyright 2014-2025 Froala Labs
 */

.fr-popup .fr-command.fr-btn[data-cmd="filestackIcon"] svg path,
.fr-popup .fr-command.fr-btn[data-cmd="openFilePickerVideo"] svg path,
.fr-popup .fr-command.fr-btn[data-cmd="openFilePickerImage"] svg path,
.fr-toolbar .fr-command.fr-btn[data-cmd="openFilePicker"] svg path,
.fr-toolbar .fr-command.fr-btn[data-cmd="openFilePickerImage"] svg path,
.fr-popup .fr-tabs .fr-command.fr-btn[data-cmd="openFilePicker"] svg path,
.fr-popup .fr-tabs .fr-command.fr-btn[data-cmd="openFilePickerImage"] svg path,
.fr-popup .fr-tabs .fr-command.fr-btn[data-cmd="openFilePickerVideo"] svg path,
.fr-command.fr-btn[data-cmd="openFilePickerReplaceImageOnly"] svg path,
.fr-command.fr-btn[data-cmd="openFilePickerReplaceVideoOnly"] svg path,
.fr-toolbar .fr-command.fr-btn[data-cmd="openFilePickerVideo"] svg path,
.fr-toolbar .fr-command.fr-btn[data-cmd="openFilePickerImageOnly"] svg path:last-child,
.fr-toolbar .fr-command.fr-btn[data-cmd="openFilePickerVideoOnly"] svg path:last-child,
.fr-popup .fr-command.fr-btn[data-cmd="openFilePickerImage"] svg path,
.fr-popup .fr-command.fr-btn[data-cmd="openFilePickerVideo"] svg path,
.fr-popup .fr-command.fr-btn[data-cmd="filestackIcon"] svg path,
.fr-popup .fr-command.fr-btn[data-cmd="openFilePickerFile"] svg path {
  fill: #ef4a25; }

.fr-command.fr-filestack-active.fr-btn.fr-filestack-active svg path {
  fill: #0098F7; }

.fsp-picker-appear-active {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 9; }

body.fr-fullscreen .fs-transforms-container {
  position: absolute;
  z-index: 2247483660; }

.fsp-notifications__container {
  position: fixed !important; }
