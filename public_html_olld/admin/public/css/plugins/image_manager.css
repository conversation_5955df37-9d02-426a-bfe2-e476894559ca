/*!
 * froala_editor v4.5.0 (https://www.froala.com/wysiwyg-editor)
 * License https://froala.com/wysiwyg-editor/terms/
 * Copyright 2014-2025 Froala Labs
 */

.fr-clearfix::after {
  clear: both;
  display: block;
  content: "";
  height: 0; }

.fr-hide-by-clipping {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0; }

.fr-modal .fr-modal-head .fr-modal-head-line::after {
  clear: both;
  display: block;
  content: "";
  height: 0; }
.fr-modal .fr-modal-head .fr-modal-head-line .fr-modal-more {
  margin-top: 10px; }
  .fr-modal .fr-modal-head .fr-modal-head-line .fr-modal-more.fr-not-available {
    opacity: 0;
    width: 0;
    padding: 12px 0; }
.fr-modal .fr-modal-head .fr-modal-tags {
  padding: 0 20px;
  display: none;
  text-align: left; }
  .fr-modal .fr-modal-head .fr-modal-tags a {
    display: inline-block;
    opacity: 0;
    padding: 6px 12px;
    margin: 8px 0 8px 8px;
    text-decoration: none;
    border-radius: 32px;
    -moz-border-radius: 32px;
    -webkit-border-radius: 32px;
    -moz-background-clip: padding;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
    -webkit-transition: opacity 0.2s ease 0s, background 0.2s ease 0s;
    -moz-transition: opacity 0.2s ease 0s, background 0.2s ease 0s;
    -ms-transition: opacity 0.2s ease 0s, background 0.2s ease 0s;
    -o-transition: opacity 0.2s ease 0s, background 0.2s ease 0s;
    cursor: pointer;
    background-color: #f5f5f5; }
    .fr-modal .fr-modal-head .fr-modal-tags a:focus {
      outline: none;
      background-color: #ebebeb; }
    .fr-modal .fr-modal-head .fr-modal-tags a:hover {
      background-color: #ebebeb; }
    .fr-modal .fr-modal-head .fr-modal-tags a:active {
      background-color: #d6d6d6; }
    .fr-modal .fr-modal-head .fr-modal-tags a.fr-selected-tag {
      background-color: #EcF5Ff;
      color: #0098f7; }
      .fr-modal .fr-modal-head .fr-modal-tags a.fr-selected-tag:focus {
        outline: none;
        background-color: #ebebeb; }
      .fr-modal .fr-modal-head .fr-modal-tags a.fr-selected-tag:hover {
        background-color: #ebebeb; }
      .fr-modal .fr-modal-head .fr-modal-tags a.fr-selected-tag:active {
        background-color: #d6d6d6; }

.fr-show-tags .fr-modal-more svg path {
  fill: #0098f7; }

div.fr-modal-body {
  -webkit-transition: background 0.2s ease 0s;
  -moz-transition: background 0.2s ease 0s;
  -ms-transition: background 0.2s ease 0s;
  -o-transition: background 0.2s ease 0s; }
  div.fr-modal-body .fr-preloader {
    display: block;
    margin: 50px auto; }
  div.fr-modal-body div.fr-image-list {
    text-align: center;
    margin: 0 20px;
    padding: 0; }
    div.fr-modal-body div.fr-image-list .fr-list-column {
      float: left;
      width: calc((100% - 20px) / 2); }
      @media (min-width: 768px) and (max-width: 1199px) {
        div.fr-modal-body div.fr-image-list .fr-list-column {
          width: calc((100% - 40px) / 3); } }
      @media (min-width: 1200px) {
        div.fr-modal-body div.fr-image-list .fr-list-column {
          width: calc((100% - 60px) / 4); } }
      div.fr-modal-body div.fr-image-list .fr-list-column + .fr-list-column {
        margin-left: 20px; }
    div.fr-modal-body div.fr-image-list div.fr-image-container {
      position: relative;
      width: 100%;
      display: block;
      border-radius: 2px;
      -moz-border-radius: 2px;
      -webkit-border-radius: 2px;
      -moz-background-clip: padding;
      -webkit-background-clip: padding-box;
      background-clip: padding-box;
      overflow: hidden; }
      div.fr-modal-body div.fr-image-list div.fr-image-container:first-child {
        margin-top: 20px; }
      div.fr-modal-body div.fr-image-list div.fr-image-container + div {
        margin-top: 20px; }
      div.fr-modal-body div.fr-image-list div.fr-image-container.fr-image-deleting::after {
        position: absolute;
        -webkit-opacity: 0.5;
        -moz-opacity: 0.5;
        opacity: 0.5;
        -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
        -webkit-transition: opacity 0.2s ease 0s;
        -moz-transition: opacity 0.2s ease 0s;
        -ms-transition: opacity 0.2s ease 0s;
        -o-transition: opacity 0.2s ease 0s;
        background: #000;
        content: "";
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        z-index: 2; }
      div.fr-modal-body div.fr-image-list div.fr-image-container.fr-image-deleting::before {
        content: attr(data-deleting);
        color: #FFF;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        margin: auto;
        position: absolute;
        z-index: 3;
        font-size: 15px;
        height: 20px; }
      div.fr-modal-body div.fr-image-list div.fr-image-container.fr-empty {
        height: 95px;
        background: #CCCCCC;
        z-index: 1; }
        div.fr-modal-body div.fr-image-list div.fr-image-container.fr-empty::after {
          position: absolute;
          margin: auto;
          top: 0;
          bottom: 0;
          left: 0;
          right: 0;
          content: attr(data-loading);
          display: inline-block;
          height: 20px; }
      div.fr-modal-body div.fr-image-list div.fr-image-container img {
        width: 100%;
        vertical-align: middle;
        position: relative;
        z-index: 2;
        -webkit-opacity: 1;
        -moz-opacity: 1;
        opacity: 1;
        -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
        -webkit-transition: opacity 0.2s ease 0s, filter 0.2s ease 0s;
        -moz-transition: opacity 0.2s ease 0s, filter 0.2s ease 0s;
        -ms-transition: opacity 0.2s ease 0s, filter 0.2s ease 0s;
        -o-transition: opacity 0.2s ease 0s, filter 0.2s ease 0s;
        -webkit-transform: translateZ(0);
        -moz-transform: translateZ(0);
        -ms-transform: translateZ(0);
        -o-transform: translateZ(0); }
      div.fr-modal-body div.fr-image-list div.fr-image-container.fr-mobile-selected img {
        -webkit-opacity: 0.75;
        -moz-opacity: 0.75;
        opacity: 0.75;
        -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)"; }
      div.fr-modal-body div.fr-image-list div.fr-image-container.fr-mobile-selected .fr-delete-img, div.fr-modal-body div.fr-image-list div.fr-image-container.fr-mobile-selected .fr-insert-img {
        display: inline-block; }
      div.fr-modal-body div.fr-image-list div.fr-image-container .fr-delete-img, div.fr-modal-body div.fr-image-list div.fr-image-container .fr-insert-img {
        display: none;
        top: 50%;
        border-radius: 100%;
        -moz-border-radius: 100%;
        -webkit-border-radius: 100%;
        -moz-background-clip: padding;
        -webkit-background-clip: padding-box;
        background-clip: padding-box;
        -webkit-transition: background 0.2s ease 0s, color 0.2s ease 0s;
        -moz-transition: background 0.2s ease 0s, color 0.2s ease 0s;
        -ms-transition: background 0.2s ease 0s, color 0.2s ease 0s;
        -o-transition: background 0.2s ease 0s, color 0.2s ease 0s;
        -webkit-box-sizing: border-box;
        -moz-box-sizing: border-box;
        box-sizing: border-box;
        position: absolute;
        cursor: pointer;
        margin: 0;
        line-height: 40px;
        text-decoration: none;
        z-index: 3; }
      div.fr-modal-body div.fr-image-list div.fr-image-container .fr-delete-img {
        background: #B8312F;
        fill: #FFF;
        padding: 8px;
        left: 50%;
        -webkit-transform: translateY(-50%) translateX(25%);
        -moz-transform: translateY(-50%) translateX(25%);
        -ms-transform: translateY(-50%) translateX(25%);
        -o-transform: translateY(-50%) translateX(25%); }
      div.fr-modal-body div.fr-image-list div.fr-image-container .fr-insert-img {
        background: #FFF;
        fill: #0098f7;
        padding: 8px;
        left: 50%;
        -webkit-transform: translateY(-50%) translateX(-125%);
        -moz-transform: translateY(-50%) translateX(-125%);
        -ms-transform: translateY(-50%) translateX(-125%);
        -o-transform: translateY(-50%) translateX(-125%); }

.fr-desktop .fr-modal-wrapper div.fr-modal-body div.fr-image-list div.fr-image-container:hover img {
  -webkit-opacity: 0.75;
  -moz-opacity: 0.75;
  opacity: 0.75;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)"; }
.fr-desktop .fr-modal-wrapper div.fr-modal-body div.fr-image-list div.fr-image-container:hover .fr-delete-img, .fr-desktop .fr-modal-wrapper div.fr-modal-body div.fr-image-list div.fr-image-container:hover .fr-insert-img {
  display: inline-block;
  width: 40px;
  height: 40px; }
.fr-desktop .fr-modal-wrapper div.fr-modal-body div.fr-image-list div.fr-image-container .fr-delete-img:hover {
  background: #bf4644;
  color: #FFF; }
.fr-desktop .fr-modal-wrapper div.fr-modal-body div.fr-image-list div.fr-image-container .fr-insert-img:hover {
  background: #ebebeb; }
