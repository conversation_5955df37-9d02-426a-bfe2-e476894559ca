/*!
 * froala_editor v4.5.0 (https://www.froala.com/wysiwyg-editor)
 * License https://froala.com/wysiwyg-editor/terms/
 * Copyright 2014-2025 Froala Labs
 */

!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(require("froala-editor")):"function"==typeof define&&define.amd?define(["froala-editor"],t):t(e.FroalaEditor)}(this,function(r){"use strict";r=r&&r.hasOwnProperty("default")?r["default"]:r,Object.assign(r.DEFAULTS,{lineHeights:{Default:"",Single:"1",1.15:"1.15",1.5:"1.5",Double:"2"}}),r.PLUGINS.lineHeight=function(a){var o=a.$;return{_init:function e(){},apply:function i(e){a.selection.save(),a.html.wrap(!0,!0,!0,!0),a.selection.restore();var t=a.selection.blocks();t.length&&o(t[0]).parent().is("td")&&a.format.applyStyle("line-height",e.toString()),a.selection.save();for(var n=0;n<t.length;n++)o(t[n]).css("line-height",e),a.opts.enter!==r.ENTER_BR||t.length&&o(t[0]).parent().is("td")||(o(t[n]).hasClass("fr-temp-div")&&o(t[n]).removeClass("fr-temp-div"),""===o(t[n]).attr("class")&&o(t[n]).removeAttr("class")),""===o(t[n]).attr("style")&&o(t[n]).removeAttr("style");a.html.unwrap(),a.selection.restore()},refreshOnShow:function l(e,t){var n=a.selection.blocks();if(n.length){var r=o(n[0]);t.find(".fr-command").each(function(){var e=o(this).data("param1"),t=r.attr("style"),n=0<=(t||"").indexOf("line-height: "+e+";");if(t){var a=t.substring(t.indexOf("line-height")),i=a.substr(0,a.indexOf(";")),l=i&&i.split(":")[1];l&&l.length||"Default"!==r.text()||(n=!0)}t&&-1!==t.indexOf("line-height")||""!==e||(n=!0),o(this).toggleClass("fr-active",n).attr("aria-selected",n)})}}}},r.RegisterCommand("lineHeight",{type:"dropdown",html:function(){var e='<ul class="fr-dropdown-list" role="presentation">',t=this.opts.lineHeights;if(t instanceof Array)for(var n=0;n<t.length;n++)e+='<li role="presentation"><a class="fr-command '.concat(t[n].label,'" tabIndex="-1" role="option" data-cmd="lineHeight" data-param1="').concat(t[n].value,'" title="').concat(this.language.translate(t[n].label),'">').concat(this.language.translate(t[n].label),"</a></li>");else if(t instanceof Object)for(var a in t)t.hasOwnProperty(a)&&(e+='<li role="presentation"><a class="fr-command '.concat(a,'" tabIndex="-1" role="option" data-cmd="lineHeight" data-param1="').concat(t[a],'" title="').concat(this.language.translate(a),'">').concat(this.language.translate(a),"</a></li>"));return e+="</ul>"},title:"Line Height",callback:function(e,t){this.lineHeight.apply(t)},refreshOnShow:function(e,t){this.lineHeight.refreshOnShow(e,t)},plugin:"lineHeight"}),r.DefineIcon("lineHeight",{NAME:"arrows-v",FA5NAME:"arrows-alt-v",SVG_KEY:"lineHeight"})});