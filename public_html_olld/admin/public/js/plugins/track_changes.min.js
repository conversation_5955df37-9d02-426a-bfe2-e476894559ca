/*!
 * froala_editor v4.5.0 (https://www.froala.com/wysiwyg-editor)
 * License https://froala.com/wysiwyg-editor/terms/
 * Copyright 2014-2025 Froala Labs
 */

!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(require("froala-editor")):"function"==typeof define&&define.amd?define(["froala-editor"],t):t(e.FroalaEditor)}(this,function(j){"use strict";function i(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,n=Array(t);a<t;a++)n[a]=e[a];return n}function $(e){return function t(e){if(Array.isArray(e))return i(e)}(e)||function a(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function n(e,t){if(e){if("string"==typeof e)return i(e,t);var a={}.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?i(e,t):void 0}}(e)||function r(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}j=j&&j.hasOwnProperty("default")?j["default"]:j,Object.assign(j.DEFAULTS,{trackChangesEnabled:!1,showChangesEnabled:!1}),j.PLUGINS.track_changes=function(P){var O=P.$;O.fn.isAfter=function(e){var t=O(this),a=t.parents(),n=e.parents(),r=t;for(var i in a){var s=e;if(a.hasOwnProperty(i)){var l=O(a[i]);for(var o in n)if(n.hasOwnProperty(o)){var c=O(n[o]);if(l[0]===c[0]){var d=r.index();return s.index()<d}s=c}r=l}}return!1};var s,r,l=[],D="",V="",B="",H="",C=[],i=[],S=null,K=[],d=!1;function o(){return l.pop()}function N(e){var t=!(!e||8!=e.keyCode),a=e&&13==e.keyCode,n=!(!e||46!=e.keyCode);if(P.opts.trackChangesEnabled){P.selection.restore(),e&&e.composing&&(P.selection.save(),P.selection.ranges()[0].collapse(!1));var r=P.selection.ranges(0),i=r.startContainer,s=!1,l=t&&0===r.startOffset&&P.$el.get(0).firstChild===i;if(t&&0===r.startOffset&&P.$el.get(0).firstChild!==i&&i!==document){for(var o=U(i);o&&o.nodeType===Node.TEXT_NODE&&1===o.length&&8203===o.textContent.charCodeAt(0);)o=U(o);o&&(i=o,s=!0)}var c=O(i).parentsUntil(P.$el,"[data-tracking=true]");if(O(i).data("tracking")&&t){P.markers.insert();var d=P.$el.find(".fr-marker");return d[0].previousSibling&&"IMG"===d[0].previousSibling.tagName||P.selection.clear(),void O(d).remove()}if(!c.length||O(i).data("tracking-deleted")&&t||"TD"==i.tagName||a){if(P.selection.isCollapsed()){if(s){var g=i;if(g&&g.nodeType!==Node.TEXT_NODE&&0<g.childNodes.length){for(var h=null,f=g.childNodes.length-1;0<=f;f--)if(g.childNodes[f].nodeType===Node.TEXT_NODE){h=g.childNodes[f];break}null!==h&&(g=h)}var p=r.cloneRange();p.setStart(g,g.length),p.setEnd(g,g.length),P.selection.get().removeAllRanges(),P.selection.get().addRange(p)}!s&&l||P.markers.insert();var u=P.$el.find(".fr-marker");if(e&&e.composing&&(u=O(u[1])),!u.length)return;var k="pending-".concat(P.id,"-").concat(P.track_changes.getPendingChanges().length),m=P.opts.showChangesEnabled?'class="fr-highlight-change"':"";if(t||n){if(t){if(!u[0].previousSibling||u[0].previousSibling.nodeType!==Node.ELEMENT_NODE||!u[0].previousSibling.lastChild||u[0].previousSibling.lastChild.nodeType!==Node.ELEMENT_NODE||"IMG"!==u[0].previousSibling.lastChild.tagName){for(;O(u[0].previousSibling).data("tracking");)u.insertBefore(u.prev());O(u[0].previousSibling).find("[data-tracking=true]").length&&u.insertBefore(O(u[0].previousSibling).find("[data-tracking=true]").eq(0))}}else if(O(i).data("tracking")){if(!i.nextSibling)return void(u[0].nextSibling&&"IMG"===u[0].nextSibling.tagName&&u[0].nextSibling.remove());u.insertBefore(i.nextSibling),G(u)}else G(u);var v=u[0]&&u[0].previousSibling?u[0].previousSibling.nodeValue:"",b=v&&v.slice(0,-(e.selectionLength?e.selectionLength:1));n&&(u[0].nextSibling&&"SPAN"!==u[0].nextSibling.tagName&&null===u[0].nextSibling.nodeValue&&u[0].nextSibling.firstChild&&u[0].nextSibling.firstChild.before(u[0]),b=(v=u[0].nextSibling?u[0].nextSibling.nodeValue:" ")&&v.slice(1)),n&&u[0].nextSibling&&"isPasted"===u[0].nextSibling.id?b=(v=u[0].nextSibling?u[0].nextSibling.textContent:" ")&&v.slice(1):t&&u[0].previousSibling&&"isPasted"===u[0].previousSibling.id&&(b=(v=u[0].previousSibling?u[0].previousSibling.textContent:" ")&&v.slice(0,-(e.selectionLength?e.selectionLength:1)));var C,S=O('<span data-tracking="true" data-track-id="'.concat(k,'" ').concat(m,"></span>")),N=O('<span data-tracking-deleted="true" class="fr-tracking-deleted" '.concat(P.helpers.isIOS()?"":'contenteditable="false"',"></span>"));P.opts.showChangesEnabled||(P.helpers.isMobile()?N.addClass("fr-track-hide-mobile"):N.addClass("fr-track-hide"));var E=!1,y=!1;if(t)try{var x=u[0]&&u[0].previousSibling;if(u[0]&&u[0].previousElementSibling&&"I"===u[0].previousElementSibling.tagName&&!v&&(C=u[0].previousElementSibling),x&&3===x.nodeType&&"SPAN"!==x.parentNode.tagName&&x.previousSibling&&"SPAN"===x.previousSibling.tagName&&" "===x.nodeValue&&0<O(x.previousSibling).find(".fr-emoticon").length?C=x.previousSibling:x&&"SPAN"===x.parentNode.tagName&&0<O(x.parentNode).find(".fr-emoticon").length?(C=x.parentNode,E=!0):x&&("SPAN"===x.tagName&&"isPasted"!==x.id||"HR"===x.tagName)?C=x:O(u).parent().prev().is("hr")?(C=O(u).parent().prev().get(0),E=y=!0):x&&"IMG"===x.tagName&&(C=x,O(S).prepend("".concat(j.MARKERS)),O(S).append("".concat(j.MARKERS))),P.helpers.isMobile()&&u[0]&&!C&&!v&&!b)return;E?(O(u[0].parentNode).after(S),y?O(u).parent().remove():O(u).remove()):u[0]?e.composing?O(u).after(S):O(u).before(S):O(H).before(S)}catch(e){H&&O(H).before(S)}else if(n){var A=u[0].nextSibling;u[0].nextElementSibling&&"I"===u[0].nextElementSibling.tagName&&!u[0].nextSibling.nodeValue?C=u[0].nextElementSibling:A&&3===A.nodeType&&"SPAN"!==A.parentNode.tagName&&A.nextSibling&&"SPAN"===A.nextSibling.tagName&&" "===A.nodeValue&&0<O(A.nextSibling).find(".fr-emoticon").length?C=A.nextSibling:A&&"SPAN"===A.parentNode.tagName&&0<O(A.parentNode).find(".fr-emoticon").length?(C=A.parentNode,E=!0):A&&"SPAN"===A.tagName&&u[0].nextSibling&&"isPasted"!==u[0].nextSibling.id?C=A:A&&"IMG"===A.tagName&&(C=A,O(S).prepend("".concat(j.MARKERS)),O(S).append("".concat(j.MARKERS))),E?(O(u[0].parentNode).before(S),O(u).remove()):O(u).after(S)}if(e.composing?K.push({span:S,"delete":N}):(P.helpers.isIOS()&&K.push({"delete":N}),O(S).prepend(N)),!C&&(C=v&&v.replace(b,""),P.helpers.isMobile()&&D&&!e.composing)){var M=P.selection.get().focusNode,_=P.selection.get().focusOffset,T=1;V&&M&&V.isSameNode(M)&&(T=B-_),C=D.replace(/\u00a0/g," ").replace(v.replace(/\u00a0/g," "),"").slice(0,T)}if(C&&(E?(O(N).append(O(O(C).get(0).outerHTML)),P.markers.insert()):O(N).append(C)),C&&0==C.length)return;P.browser.msie&&t&&(O(S).prepend("".concat(j.MARKERS)),O(S).append("".concat(j.MARKERS))),t&&(b||""===b)&&u[0].previousSibling?u[0].previousSibling.nodeValue=b:n&&(O(S).append("".concat(j.MARKERS)),u[0].nextSibling.nodeValue=b)}else{var I=j.INVISIBLE_SPACE;if(e&&P.helpers.isMobile()&&((I=u[0]&&u[0].previousSibling&&u[0].previousSibling.nodeValue?u[0].previousSibling.nodeValue.slice(-1):j.INVISIBLE_SPACE)&&u[0]&&u[0].previousSibling&&u[0].previousSibling.nodeValue?u[0].previousSibling.nodeValue=u[0].previousSibling.nodeValue.slice(0,-1):!u[0]||u[0].previousSibling||u[0].nextSibling||I!==j.INVISIBLE_SPACE||(I=P.selection.get().focusNode.nodeValue.slice(B,P.selection.get().focusOffset),P.selection.get().focusNode.nodeValue=P.selection.get().focusNode.nodeValue.slice(P.selection.get().focusOffset))),a){if(u.parent().parent().is("li"))return;var w=O("<p></p>");return void(0<u.closest("p").length&&(u.closest("p").after(w),w.append(u),P.markers.remove(),setTimeout(function(){w.prev().text()!==String.fromCharCode(8203)&&""!==w.prev().text()||w.prev().remove(),P.selection.setAfter(w[0])},1)))}u[0].previousSibling&&"BR"===u[0].previousSibling.tagName&&u[0].previousSibling.remove(),u.replaceWith('<span data-tracking="true" data-track-id="'.concat(k,'" ').concat(m,">").concat(I+j.MARKERS,"</span>"))}P.selection.restore(),P.track_changes.pushChange(k)}else{P.selection.save();var L=P.$el.find('.fr-marker[data-type="true"]').length&&P.$el.find('.fr-marker[data-type="true"]').get(0),R=P.$el.find('.fr-marker[data-type="false"]').length&&P.$el.find('.fr-marker[data-type="false"]').get(0);if(O(L).parent().hasClass("fr-emoticon")&&O(L).parent().before(L),O(L).isAfter(O(R))){var $=L;L=R,R=$}F(L,R,e&&e.deletion),P.selection.restore()}P.helpers.isMobile()&&D&&P.markers.remove()}}}function G(e){if(O(e[0].nextSibling).data("tracking")){for(;O(e[0].nextSibling).data("tracking")&&(!(0<O(e[0].previousElementSibling).find("[data-tracking-deleted=true]").length&&0<O(e[0].nextElementSibling).find("[data-tracking-deleted=true]").length)||O(e[0].previousSibling).data("tracking"));)O(e).insertAfter(e.next());e.insertBefore(e[0].previousSibling)}}function U(e){var t=e.previousSibling;if(!t){var a=O(e).parent().get(0).previousSibling;null!==a&&(t=a.lastChild||a)}return t}function F(e,t){var a=2<arguments.length&&arguments[2]!==undefined&&arguments[2];if(e&&(!e.isSameNode(t)||!O(e).hasClass("fr-marker"))){for(;e.nodeType===Node.COMMENT_NODE;)e=e.nextSibling;if(O(e).parent().is("table")&&0!==O(e).parent().next().text().length&&F(O(e).parent().next()[0],t,a),"TD"!==e.tagName||1!==e.childNodes.length||"BR"!==e.childNodes[0].tagName){if(P.node.isBlock(e)&&"HR"!==e.tagName)return P.node.hasClass(e.firstChild,"fr-marker")?F(e.firstChild.nextSibling,t,a):F(e.firstChild,t,a),!1;if("BR"===e.tagName&&"TD"===e.parentElement.tagName){var n=e.parentElement.nextElementSibling;O(e).remove(),F(n,t)}var r="pending-".concat(P.id,"-").concat(P.track_changes.getPendingChanges().length),i=P.opts.showChangesEnabled?'class="fr-highlight-change"':"",s=O('<span data-tracking="true" data-track-id="'.concat(r,'" ').concat(i,"></span>")),l=O('<span data-tracking-deleted="true" class="fr-tracking-deleted" '.concat(P.helpers.isIOS()?"":'contenteditable="false"',"></span>"));P.opts.showChangesEnabled||(P.helpers.isMobile()?l.addClass("fr-track-hide-mobile"):l.addClass("fr-track-hide"));var o=e;for(O(e).before(s),P.track_changes.pushChange(r);o&&O(o).hasClass("fr-marker");)o=o.nextSibling;for(var c=!1;o&&!c&&!O(o).hasClass("fr-marker")&&!O(o).is("[data-track-id]")&&0===O(o).find("fr-marker").length&&"UL"!==o.tagName&&"OL"!==o.tagName;){var d=o;if("IMG"===o.tagName&&O(o).data("tracking-img","true"),P.node.isBlock(o)&&"HR"!==e.tagName)return F(o.firstChild,t,a),!1;if(o)for(var g=o.childNodes,h=0;h<g.length;h++)if(g[h].className&&"fr-marker"===g[h].className){c=!0,F(o.firstChild,t,a);break}c||(o=o.nextSibling,O(l).append(d))}if(!o||"UL"!==o.tagName&&"OL"!==o.tagName||F(o,t,a),a?K.push({span:s,"delete":l}):(P.helpers.isIOS()&&K.push({"delete":l}),O(s).prepend(l)),P.browser.msie?O(s).prepend("".concat(j.INVISIBLE_SPACE+j.MARKERS)):O(s).prepend("".concat(j.INVISIBLE_SPACE)),O(s).append("".concat(j.INVISIBLE_SPACE+j.MARKERS)),!o&&!e.isSameNode(t)){for(var f=s.get(0).parentNode;f&&!f.nextSibling&&!P.node.isElement(f);)f=f.parentNode;if(f){var p=f.nextSibling;p&&(P.node.isBlock(p)?"HR"===p.tagName?F(p.nextSibling,t,a):(P.browser.mozilla&&"LI"===f.tagName&&"SPAN"===f.lastChild.tagName&&O(f.lastChild).hasClass("fr-marker")&&f.lastChild.remove(),F(p.firstChild,t,a)):F(p,t,a))}}}}}function E(e){if(P.opts.trackChangesEnabled){P.selection.restore();var t=P.selection.ranges(0).startContainer,a=O(t).parentsUntil(P.$el,"[data-tracking=true]");if(O(t).data("tracking"))return;if(!a.length&&!P.selection.isCollapsed()&&"quote"!=e){P.selection.save(),f(P.$el.find('.fr-marker[data-type="true"]').length&&P.$el.find('.fr-marker[data-type="true"]').get(0),P.$el.find('.fr-marker[data-type="false"]').length&&P.$el.find('.fr-marker[data-type="false"]').get(0));var n=P.$el.find('.fr-marker[data-type="true"]').length&&P.$el.find('.fr-marker[data-type="true"]').get(0),r=n.parentNode;if(r&&"A"===r.tagName&&r.firstChild&&"SPAN"===r.firstChild.tagName&&2<r.firstChild.childNodes.length&&"STRONG"===r.firstChild.childNodes[3].tagName)O(r.firstChild.childNodes[3].firstChild).before(n);else{for(var i=n.previousSibling.firstChild;i&&"SPAN"!==i.tagName;)i=i.nextSibling;O(i).after(n)}P.selection.restore()}}}function f(e,t,a){if(e&&!("<br>"===O(e).html()&&O(e).parent().is("td")||(O(e).parent().is("table")&&0!==O(e).parent().next().text().length&&f(O(e).parent().next()[0],t,a),O(e).is("br")&&O(e).parent().length&&O(e).parent().is("td")&&0===O(e).parent().text().length))){for(a||(a="pending-".concat(P.id,"-").concat(P.track_changes.getPendingChanges().length),P.track_changes.pushChange(a));e.nodeType===Node.COMMENT_NODE;)e=e.nextSibling;if(P.node.isBlock(e)&&"HR"!==e.tagName)return P.node.hasClass(e.firstChild,"fr-marker")?f(e.firstChild.nextSibling,t,a):f(e.firstChild,t,a),!1;var n=P.opts.showChangesEnabled?'class="fr-highlight-change"':"",r=O('<span data-tracking="true" data-track-id="'.concat(a,'" ').concat(n,"></span>")),i=O('<span data-tracking-deleted="true" class="fr-tracking-deleted" contenteditable="false" style="display:none"></span>'),s=e;for(O(e).before(r);s&&O(s).hasClass("fr-marker");)s=s.nextSibling;for(var l=!1;s&&!l&&!O(s).hasClass("fr-marker")&&0===O(s).find("fr-marker").length&&"UL"!==s.tagName&&"OL"!==s.tagName;){var o=s;if(P.node.isBlock(s)&&"HR"!==e.tagName)return f(s.firstChild,t,a),!1;if(s)for(var c=s.childNodes,d=0;d<c.length;d++)if(c[d].className&&"fr-marker"===c[d].className){l=!0,f(s.firstChild,t,a);break}l||(s=s.nextSibling,O(i).append(o.cloneNode(!0)),O(r).append(o))}if(!s||"UL"!==s.tagName&&"OL"!==s.tagName||f(s,t,a),O(s).hasClass("fr-marker")&&O(r).append(s),O(r).prepend(i),O(r).prepend("".concat(j.INVISIBLE_SPACE)),O(r).append("".concat(j.INVISIBLE_SPACE)),!s&&!e.isSameNode(t)){for(var g=r.get(0).parentNode;g&&!g.nextSibling&&!P.node.isElement(g);)g=g.parentNode;if(g){var h=g.nextSibling;h&&(P.node.isBlock(h)?"HR"===h.tagName?f(h.nextSibling,t,a):f(h.firstChild,t,a):f(h,t,a))}}}}function c(e,t,a){var n=P.opts.showChangesEnabled?'class="fr-highlight-change"':"",r=O('<span data-tracking="true" data-track-id="'.concat(a,'" ').concat(n,"></span>")),i=O('<span data-tracking-deleted="true" class="fr-tracking-deleted" contenteditable="false"></span>');P.opts.showChangesEnabled||i.addClass("fr-track-hide"),i.append(t.get(0).innerHTML),r.append(i);var s=e.get(0).innerHTML;e.html(""),e.append(r),r.append(s)}function g(){if(P.events.trigger("image.hideResizer"),P.events.trigger("video.hideResizer"),P.html.cleanEmptyTags(!0),P.helpers.isMobile())for(var e=P.$el.find("img[data-tracking-img]"),t=0;t<e.length;t++){var a=O(e.get(t));a.parent()&&a.parent().data("tracking")||a.removeData("tracking-img")}}function e(){P.$el.find("li").each(function(){0===this.innerText.replace(/\u200B/g,"").length&&this.remove()})}function t(){var e=o();if(e){var t,a=P.$el.find("[data-track-id=".concat(e,"]"));if(a.length){var n=a.parent("a");if(n.length){var r=O(n.get(0).outerHTML).text();1===[].filter.call(r,function(e){return 8203!==e.charCodeAt(0)}).length&&(t=n)}for(var i=0;i<a.length;i++){var s=a.get(i),l=O(a).data("tracking-deleted")?O(a):O(s).find("[data-tracking-deleted=true]");l.length&&(l.get(0).remove?l.get(0).remove():l.get(0).parentNode.removeChild(l.get(0))),s.hasAttribute("style")?(O(s).removeData("tracking"),O(s).removeData("track-id"),O(s).removeClass("fr-highlight-change")):(P.selection.save(),0<O(s).find("IFRAME").length?(O(s).removeData("tracking"),O(s).removeData("track-id"),O(s).removeClass("fr-highlight-change")):s.outerHTML=s.innerHTML,P.selection.restore())}}t&&t.remove()}g()}function a(){var e=o();if(e){var t=P.$el.find("[data-track-id=".concat(e,"]"));if(t.length){P.selection.save();for(var a=t.length-1;0<=a;a--){var n=O(t).data("tracking-deleted")?O(t):O(t.get(a)).find("[data-tracking-deleted=true]");if(n.length)if(0<O(t).find("IFRAME").length)O(t).removeData("tracking"),O(t).removeData("track-id"),O(t).removeClass("fr-highlight-change"),O(t).removeClass("fr-tracking-deleted"),O(t).removeData("tracking-deleted"),O(t).removeAttr("contenteditable");else n.parent("[data-tracking=true]").data("track-id")==e?(O(t.get(a)).find("[data-tracking-deleted=true]").remove(),t.get(a).outerHTML=n.get(0).innerHTML):t.get(a).remove?t.get(a).remove():t.get(a).parentNode.removeChild(t.get(a));else t.get(a).remove?t.get(a).remove():t.get(a).parentNode.removeChild(t.get(a))}P.selection.restore()}}g()}return P.events.on("keydown",function(e){var t;if(!P.opts.trackChangesEnabled)return!0;if(s=null===(t=P.selection.get())||void 0===t||null===(t=t.focusNode)||void 0===t?void 0:t.nodeValue,P.helpers.isAndroid()){var a,n,r=P.selection.get();D=null==r||null===(a=r.focusNode)||void 0===a?void 0:a.nodeValue,V=null==r?void 0:r.focusNode,H=null===(n=V)||void 0===n?void 0:n.nextSibling,B=null==r?void 0:r.focusOffset}else if(function i(e){return(!P.keys.ctrlKey(e)||e.which!==j.KEYCODE.Z&&e.which!==j.KEYCODE.C)&&(48<=e.keyCode&&e.keyCode<=57||65<=e.keyCode&&e.keyCode<=90||97<=e.keyCode&&e.keyCode<=122||186<=e.keyCode&&e.keyCode<=222||13===e.keyCode||8===e.keyCode||32===e.keyCode||46===e.keyCode||96<=e.keyCode&&e.keyCode<=111)}(e))if(!P.keys.ctrlKey(e)||"b"!==e.key&&"i"!==e.key&&"u"!==e.key){if(P.keys.ctrlKey(e)&&"a"===e.key||P.keys.ctrlKey(e)&&"s"===e.key||P.keys.ctrlKey(e)&&"x"===e.key)return;N(e)}else E()},!0),P.events.on("cut",function(e){if(!P.opts.trackChangesEnabled)return!0;for(var t=P.selection.get().getRangeAt(0).cloneContents(),a="",n="",r=0;r<t.childNodes.length;r++){var i=t.childNodes[r],s=i.outerHTML,l=i.textContent;i.nodeType===Node.TEXT_NODE&&(l=s=i.textContent.replace(/\u200B/g,"")),a+=s,n+=l}P.paste.saveCopiedText(a,n,!0),N(e)},!0),P.events.on("keyup",function(e){if(!P.opts.trackChangesEnabled)return!0;var t;if(P.helpers.isMobile())if(r=null===(t=P.selection.get())||void 0===t||null===(t=t.focusNode)||void 0===t?void 0:t.nodeValue,d){for(var a=0;a<K.length;a++)P.opts.showChangesEnabled&&O(K[a].span).empty(),O(K[a].span).prepend(K[a]["delete"]);d=!(K=[])}else if(P.helpers.isIOS()){for(var n=0;n<K.length;n++)r&&1===r.length&&s&&1==s.length?(O(K[n]["delete"]).attr("contenteditable","true"),O(K[n]["delete"]).addClass("fr-ios-mobile-disable-select")):O(K[n]["delete"]).attr("contenteditable","false");K=[]}else{if(46===e.keyCode)return!0;N(e)}},!0),P.events.on("beforeinput",function(e){var t=e.originalEvent,a=!1;if(!P.opts.trackChangesEnabled)return!0;if(P.helpers.isAndroid()){var n=P.selection.get(),r=P.selection.ranges(0),i=r.startContainer,s=r.endContainer,l=O(i).parentsUntil(P.$el,"[data-tracking=true]"),o=O(s).parentsUntil(P.$el,"[data-tracking=true]");if("insertCompositionText"===t.inputType)if(""===t.data)a=!0;else if(n.toString().length>t.data.length){if(0<l.length||0<o.length)return d=!0,e.preventDefault(),!1;a=!0,e.composing=!0}if("insertText"!==t.inputType||P.selection.isCollapsed()||(a=!0),a||"deleteContentBackward"===t.inputType){var c;if(d=!0,i.lastChild&&!n.toString()&&null!==(c=i.lastChild.children)&&void 0!==c&&null!==(c=c.item(0))&&void 0!==c&&c.hasAttribute("contenteditable"))return;e.keyCode=8,e.deletion=!0,N(e)}}},!0),P.events.on("paste.before",function(e){P.opts.trackChangesEnabled&&P.helpers.isMobile()&&N(e)},!0),P.events.on("paste.after",function(e){if(P.opts.trackChangesEnabled){P.markers.insert();for(var t=P.$el.find(".fr-marker"),a=t.next();a.is("br");){var n=a.next();a.remove(),a=n}if(a.is("ul")||a.is("ol")){var r=t.closest("ul, ol"),i=a.children("li");i.find("[data-tracking=true]").add(i.closest("[data-tracking=true]")).addClass("fr-highlight-change");var s=0<a.closest("li").next().length?a.closest("li").next():function o(e){return e.append('<li data-temp-li="true"></li>').find("li").last()}(r);i.each(function(){var e=O(this),t="pending-".concat(P.id,"-").concat(P.track_changes.getPendingChanges().length),a=P.opts.showChangesEnabled?'class="fr-highlight-change"':"";O('<li><span data-tracking="true" data-track-id="'.concat(t,'" ').concat(a,">").concat(e.html(),"</span></li>")).insertBefore(s),P.track_changes.pushChange(t)});var l=r.find("li[data-temp-li=true");0<l.length&&l.remove(),a.remove()}P.markers.remove()}},!0),P.events.on("image.inserted",function(e){P.helpers.isMobile()&&P.opts.trackChangesEnabled&&e.data("tracking-img",!0)}),P.events.on("image.removed",function(e){if(P.helpers.isMobile()&&P.opts.trackChangesEnabled&&!e.data("tracking-img")&&!e.parent().data("tracking")){var t=P.selection.ranges(0).startContainer,a=O(t).parentsUntil(P.$el,"[data-tracking=true]");P.markers.insert();var n=P.$el.find(".fr-marker"),r="pending-".concat(P.id,"-").concat(P.track_changes.getPendingChanges().length),i=P.opts.showChangesEnabled?'class="fr-highlight-change"':"",s=O('<span data-tracking="true" data-track-id="'.concat(r,'" ').concat(i,"></span>")),l=O('<span data-tracking-deleted="true" class="fr-tracking-deleted" contenteditable="false"></span>');P.opts.showChangesEnabled||l.addClass("fr-track-hide"),e.data("tracking-img",!0),O(t).data("tracking")||a.length?(O(n).replaceWith(l),O(l).append(e),P.markers.remove()):(O(n).replaceWith(s),O(s).prepend(l),O(l).append(e),P.markers.remove(),P.track_changes.pushChange(r))}}),P.events.on("commands.before",function(t){if(!P.opts.trackChangesEnabled)return!0;var e=["applytextColor","bold","italic","underline","strikeThrough","subscript","superscript","fontFamily","fontSize","textColor","applybackgroundColor","inlineClass","inlineStyle","alignLeft","alignCenter","formatOLSimple","alignRight","alignJustify","formatOL","formatUL","paragraphFormat","paragraphStyle","lineHeight","outdent","indent","quote"];if(["change","applyAll","applyLast","removeLast","showChanges","trackChanges","moreTrackChanges","undo","redo","fullscreen","print","getPDF","spellChecker","selectAll","html","help"].filter(function(e){return e===t}).length||e.filter(function(e){return e===t}).length){if(e.filter(function(e){return e===t}).length&&"paragraphFormat"!==t&&"paragraphStyle"!==t&&"lineHeight"!==t)E(t);else if("paragraphFormat"===t||"paragraphStyle"===t||"lineHeight"===t){P.selection.save();for(var a=P.$el.find('.fr-marker[data-type="true"]').length&&P.$el.find('.fr-marker[data-type="true"]').get(0),n=P.opts.showChangesEnabled?'class="fr-highlight-change"':"",r="pending-".concat(P.id,"-").concat(P.track_changes.getPendingChanges().length),i=O('<span data-tracking="true" data-track-id="'.concat(r,'" ').concat(n,"></span>")),s=O('<span data-tracking-deleted="true" class="fr-tracking-deleted" contenteditable="false" style="display:none"></span>'),l=a.parentNode.cloneNode(!0),o=l.childNodes,c=0;c<o.length;)o[c].className&&"fr-marker"===o[c].className?o[c].remove():c++;O(s).append(l),O(i).append(s),O(a.parentNode).after(O("<p></p>")),O(a.parentNode.nextSibling).append(i),i=O('<span data-tracking="true" data-track-id="'.concat(r,'" ').concat(n,"></span>")),O(a.parentNode.nextSibling).after(O("<p></p>")),O(a.parentNode.nextSibling.nextSibling).append(i),i.append(a.parentNode),P.track_changes.pushChange(r),P.selection.restore()}}else{if(j.COMMANDS[t]&&(j.COMMANDS[t].hasOwnProperty("type")&&"dropdown"===j.COMMANDS[t].type||j.COMMANDS[t].more_btn||j.COMMANDS[t].popup)){if("insertImage"!=t&&"insertFile"!=t)return}else if("clearFormatting"===t){if(P.opts.trackChangesEnabled){S=P.selection.blocks();for(var d=[],g=0,h=0;h<S.length;h++){g=h<g?g:h;var f=O(S[h]);if(f.is("li"))f.siblings("li").each(function(e,t){O(t).find("li").length&&(d.push({index:g,item:t}),g+=1)});C[h]=O("<span>".concat(f.get(0).innerHTML,"</span>"))}for(var p=0,u=d;p<u.length;p++){var k=u[p],m=k.index,v=k.item,b=O(O(v).get(0).outerHTML);b.find("ul").remove(),b.find("ol").remove(),C=[].concat($(C.slice(0,m+1)),[O("<span>".concat(b.get(0).innerHTML,"</span>"))],$(C.slice(m+1))),S=[].concat($(S.slice(0,m+1)),[v],$(S.slice(m+1)))}}return}N()}},!0),P.events.on("commands.after",function(e){if(!P.opts.trackChangesEnabled)return!0;if(0<=["undo","redo"].indexOf(e))!function r(){var e=P.$el.find("[data-tracking=true]");l.splice(0,l.length);for(var t=0;t<e.length;t++){var a=O(e.get(t)).data("track-id");l[a.slice(a.lastIndexOf("-")+1)]=a}}();else if("clearFormatting"==e&&P.opts.trackChangesEnabled){var t="pending-".concat(P.id,"-").concat(P.track_changes.getPendingChanges().length);for(var a in S){var n=O(S[a]);C[a]&&(c(n,C[a],t),C[a]=null,i[a]=null)}P.track_changes.pushChange(t),C=[],S=null}},!0),P.events.on("quickInsert.commands.before",function(e){P.opts.trackChangesEnabled&&N(e)}),{toggleTracking:function n(){P.opts.trackChangesEnabled&&-1<P.html.get(!0,!0).indexOf("data-tracking")||P.opts.trackChangesEnabled&&0<P.$el.find("[data-tracking=true]").length?alert("Your editor has pending changes. Please resolve them before turning off Track Changes in the toolbar."):(P.commands.moreTrackChanges(),P.opts.trackChangesEnabled=!P.opts.trackChangesEnabled,P.opts.trackChangesEnabled||(P.opts.showChangesEnabled=!1))},pushChange:function h(e){l.push(e)},insertChangeAt:function p(e,t){l=[].concat($(l.slice(0,e)),[t],$(l.slice(e)))},popChange:o,getPendingChanges:function u(){return l},showChanges:function k(){if(P.opts.showChangesEnabled=!P.opts.showChangesEnabled,function r(e,t){for(var a=0;a<e.length;a++)t?(O(e.get(a)).removeClass("fr-track-hide"),O(e.get(a)).removeClass("fr-track-hide-mobile")):P.helpers.isMobile()?O(e.get(a)).addClass("fr-track-hide-mobile"):O(e.get(a)).addClass("fr-track-hide")}(P.$el.find(".fr-tracking-deleted"),P.opts.showChangesEnabled),P.opts.showChangesEnabled)for(var e=P.$el.find("[data-tracking=true]").not(".fr-highlight-change"),t=0;t<e.length;t++)O(e.get(t)).addClass("fr-highlight-change");else for(var a=P.$el.find(".fr-highlight-change"),n=0;n<a.length;n++)O(a.get(n)).removeClass("fr-highlight-change")},acceptAllChanges:function m(){for(;0!=P.track_changes.getPendingChanges().length;)t();l=[],e(),P.$el.find("li").each(function(){!O(this).children()[0]||"OL"!==O(this).children()[0].tagName&&"UL"!==O(this).children()[0].tagName||e()})},rejectAllChanges:function v(){for(;0!=P.track_changes.getPendingChanges().length;)a();l=[]},acceptSingleChange:t,rejectSingleChange:a,refresh:function b(e){var t,a,n=P.$el.find("[data-tracking=true]");e.toggleClass("fr-disabled",!l.length),e.toggleClass("fr-active",l.length).attr("aria-pressed",l.length),0<!n.length&&(e.removeClass("fr-active"),e.addClass("fr-disabled"));var r=null===(t=P.$tb)||void 0===t?void 0:t.find('.fr-command[data-cmd="markdown"]');r&&P.opts.trackChangesEnabled?r.addClass("fr-disabled"):r&&!O(".fr-active.fr-popup").length&&r.removeClass("fr-disabled");var i=null===(a=P.$tb)||void 0===a?void 0:a.find('.fr-command[data-cmd="showChanges"]');i&&P.opts.trackChangesEnabled&&i.removeClass("fr-disabled")},replaceSpecialItem:function y(e){P.selection.setBefore(e.get(0)),P.selection.setAfter(e.get(0)),f(P.$el.find('.fr-marker[data-type="true"]').length&&P.$el.find('.fr-marker[data-type="true"]').get(0),P.$el.find('.fr-marker[data-type="false"]').length&&P.$el.find('.fr-marker[data-type="false"]').get(0))},removeSpecialItem:function x(e){P.selection.setBefore(e.get(0)),P.selection.setAfter(e.get(0)),F(P.$el.find('.fr-marker[data-type="true"]').length&&P.$el.find('.fr-marker[data-type="true"]').get(0),P.$el.find('.fr-marker[data-type="false"]').length&&P.$el.find('.fr-marker[data-type="false"]').get(0))},removedTable:function A(e){var t=!1;if(e.find("[data-tracking=true]").each(function(){0<this.innerText.replace(/\u200B/g,"").length&&(t=!0)}),t)alert("Your table has pending changes. Please resolve them before remove table.");else{var r="pending-".concat(P.id,"-").concat(P.track_changes.getPendingChanges().length),i=P.opts.showChangesEnabled?'class="fr-highlight-change"':"",a=O('<span data-tracking="true" data-track-id="'.concat(r,'" ').concat(i,"></span>")),n=O('<span data-tracking-deleted="true" class="fr-tracking-deleted" contenteditable="false"></span>');e.before(a),e.find("tr").find("> td").each(function(){var e=O('<span data-tracking="true" data-track-id="'.concat(r,'" ').concat(i,"></span>")),t=O('<span data-tracking-deleted="true" class="fr-tracking-deleted" contenteditable="false"></span>'),a=O(this),n=a[0].innerText;O(t)[0].innerText=n,a[0].innerText="",e.prepend(t),a.prepend(e)}),n.append(e),a.append(n),P.track_changes.pushChange(r)}},addQuote:function M(e){var t="pending-".concat(P.id,"-").concat(P.track_changes.getPendingChanges().length),a=P.opts.showChangesEnabled?'class="fr-highlight-change"':"",n=O('<div data-tracking="true" data-track-id="'.concat(t,'" ').concat(a,"></div>")),r=O('<div data-tracking="true" data-track-id="'.concat(t,'" ').concat(a,"></div>")),i=O('<span data-tracking-deleted="true" class="fr-tracking-deleted" contenteditable="false"></span>');P.opts.showChangesEnabled||i.addClass("fr-track-hide"),e.before(r),e.before(n);var s=e.get(0).innerHTML;i.append(s),n.append(i),r.append(e),r.find('[data-tracking="true"]').each(function(e,t){O(t).children().eq(0).insertBefore(O(t)),O(t).remove()}),P.track_changes.pushChange(t)},removeQuote:function _(e,t){if(!(0<t)){var a="pending-".concat(P.id,"-").concat(P.track_changes.getPendingChanges().length),n=P.opts.showChangesEnabled?'class="fr-highlight-change"':"",r=O('<div data-tracking="true" data-track-id="'.concat(a,'" ').concat(n,"></div>")),i=O('<div data-tracking="true" data-track-id="'.concat(a,'" ').concat(n,"></div>")),s=O('<span data-tracking-deleted="true" class="fr-tracking-deleted" contenteditable="false"></span>');P.opts.showChangesEnabled||s.addClass("fr-track-hide"),e.before(i),e.before(r);var l=e.find("blockQuote").eq(0).html();l||(l=e.html()),s.append(e),r.append(s),i.append(l),i.find('[data-tracking="true"]').each(function(e,t){O(t).children().eq(0).insertBefore(O(t)),O(t).remove()}),P.track_changes.pushChange(a)}},wrapInTracking:function T(e,t){var a="pending-".concat(P.id,"-").concat(P.track_changes.getPendingChanges().length),n=P.opts.showChangesEnabled?'class="fr-highlight-change"':"",r=O('<span data-tracking="true" data-track-id="'.concat(a,'" ').concat(n,"></span>"));t&&r.addClass(t);var i=O('<span data-tracking-deleted="true" class="fr-tracking-deleted" contenteditable="false"></span>');return P.opts.showChangesEnabled||i.addClass("fr-track-hide"),e.before(r),r.append(e),P.track_changes.pushChange(a),r},wrapInDelete:function I(e){var t=O('<span data-tracking-deleted="true" class="fr-tracking-deleted" contenteditable="false"></span>');return P.opts.showChangesEnabled||(P.helpers.isMobile()?t.addClass("fr-track-hide-mobile"):t.addClass("fr-track-hide")),t.append(e),t},wrapLinkInTracking:function w(e,t){var a="pending-".concat(P.id,"-").concat(P.track_changes.getPendingChanges().length),n=P.opts.showChangesEnabled?'class="fr-highlight-change"':"",r=O('<span data-tracking="true" data-track-id="'.concat(a,'" ').concat(n,"></span>"));return e.before(r),r.append(e),P.track_changes.insertChangeAt(t-1,a),r},pasteInEmptyEdior:function L(e){var t=P.$el.find(".fr-highlight-change");O(t)[0].style.display="block",O(t)[0].innerHTML=e},pasteInEdior:function R(e){var t=P.$el.find(".fr-marker");O(t)[0].outerHTML=e,P.markers.remove()}}},j.DefineIcon("trackChanges",{NAME:"enable-tracking",SVG_KEY:"trackChanges"}),j.DefineIcon("showChanges",{NAME:"show-changes",SVG_KEY:"showTrackChanges"}),j.DefineIcon("applyAll",{NAME:"apply-all",SVG_KEY:"acceptAllChanges"}),j.DefineIcon("removeAll",{NAME:"remove-all",SVG_KEY:"rejectAllChanges"}),j.DefineIcon("applyLast",{NAME:"apply-last",SVG_KEY:"acceptSingleChange"}),j.DefineIcon("removeLast",{NAME:"remove-last",SVG_KEY:"rejectSingleChange"}),j.RegisterCommand("trackChanges",{type:"button",title:"Enable Track Changes",plugin:"track_changes",showOnMobile:!0,callback:function(){this.track_changes.toggleTracking()},refreshAfterCallback:!0,forcedRefresh:!0,refresh:function(e){if(e&&this.$tb){var t=this.$tb.find('.fr-more-toolbar[data-name="'.concat(e.attr("id"),'"]')),a=0!==t.length&&t.hasClass("fr-expanded");(this.opts.trackChangesEnabled&&this.opts.toolbarContainer&&!a||!this.opts.trackChangesEnabled&&this.opts.toolbarContainer&&a)&&(this.$tb.find('.fr-more-toolbar[data-name="'.concat(e.attr("id"),'"]')).toggleClass("fr-expanded"),this.$box.toggleClass("fr-toolbar-open"),this.$tb.toggleClass("fr-toolbar-open"),e.toggleClass("fr-open")),e.toggleClass("fr-active",this.opts.trackChangesEnabled).attr("aria-pressed",this.opts.trackChangesEnabled)}}}),j.RegisterCommand("showChanges",{type:"button",icon:"showChanges",title:"Show Changes",plugin:"track_changes",undo:!1,focus:!1,accessibilityFocus:!0,forcedRefresh:!0,refreshAfterCallback:!0,toggle:!0,callback:function(){this.track_changes.showChanges()},refresh:function(e){e.toggleClass("fr-active",this.opts.showChangesEnabled).attr("aria-pressed",this.opts.showChangesEnabled)}}),j.RegisterCommand("applyAll",{type:"button",icon:"applyAll",title:"Accept All Changes",plugin:"track_changes",toggle:!0,refreshAfterCallback:!0,forcedRefresh:!0,callback:function(){0<this.track_changes.getPendingChanges().length&&this.track_changes.acceptAllChanges()},refresh:function(e){this.opts.trackChangesEnabled&&this.track_changes.refresh(e)}}),j.RegisterCommand("removeAll",{type:"button",icon:"removeAll",title:"Reject All Changes",plugin:"track_changes",toggle:!0,refreshAfterCallback:!0,forcedRefresh:!0,callback:function(){0<this.track_changes.getPendingChanges().length&&this.track_changes.rejectAllChanges()},refresh:function(e){this.opts.trackChangesEnabled&&this.track_changes.refresh(e)}}),j.RegisterCommand("applyLast",{type:"button",icon:"applyLast",title:"Accept Single Change",plugin:"track_changes",toggle:!0,refreshAfterCallback:!0,forcedRefresh:!0,callback:function(){0<this.track_changes.getPendingChanges().length&&this.track_changes.acceptSingleChange()},refresh:function(e){this.opts.trackChangesEnabled&&this.track_changes.refresh(e)}}),j.RegisterCommand("removeLast",{type:"button",icon:"removeLast",title:"Reject Single Change",plugin:"track_changes",toggle:!0,refreshAfterCallback:!0,forcedRefresh:!0,callback:function(){0<this.track_changes.getPendingChanges().length&&this.track_changes.rejectSingleChange()},refresh:function(e){this.opts.trackChangesEnabled&&this.track_changes.refresh(e)}})});