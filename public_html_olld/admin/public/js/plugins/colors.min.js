/*!
 * froala_editor v4.5.0 (https://www.froala.com/wysiwyg-editor)
 * License https://froala.com/wysiwyg-editor/terms/
 * Copyright 2014-2025 Froala Labs
 */

!function(o,t){"object"==typeof exports&&"undefined"!=typeof module?t(require("froala-editor")):"function"==typeof define&&define.amd?define(["froala-editor"],t):t(o.FroalaEditor)}(this,function(k){"use strict";k=k&&k.hasOwnProperty("default")?k["default"]:k,Object.assign(k.POPUP_TEMPLATES,{"textColor.picker":"[_BUTTONS_][_TEXT_COLORS_][_CUSTOM_COLOR_]","backgroundColor.picker":"[_BUTTONS_][_BACKGROUND_COLORS_][_CUSTOM_COLOR_]"}),Object.assign(k.DEFAULTS,{colorsText:["#61BD6D","#1ABC9C","#54ACD2","#2C82C9","#9365B8","#475577","#CCCCCC","#41A85F","#00A885","#3D8EB9","#2969B0","#553982","#28324E","#000000","#F7DA64","#FBA026","#EB6B56","#E25041","#A38F84","#EFEFEF","#FFFFFF","#FAC51C","#F37934","#D14841","#B8312F","#7C706B","#D1D5D8","REMOVE"],colorsBackground:["#61BD6D","#1ABC9C","#54ACD2","#2C82C9","#9365B8","#475577","#CCCCCC","#41A85F","#00A885","#3D8EB9","#2969B0","#553982","#28324E","#000000","#F7DA64","#FBA026","#EB6B56","#E25041","#A38F84","#EFEFEF","#FFFFFF","#FAC51C","#F37934","#D14841","#B8312F","#7C706B","#D1D5D8","REMOVE"],colorsStep:7,colorsHEXInput:!0,colorsButtons:["colorsBack","|","-"]}),k.PLUGINS.colors=function(g){var E=g.$,l='<div class="fr-color-hex-layer fr-active fr-layer" id="fr-color-hex-layer- \n  '.concat(g.id,'"><div class="fr-input-line"><input maxlength="7" id="[ID]"\n  type="text" placeholder="').concat(g.language.translate("HEX Color"),'" \n  tabIndex="1" aria-required="true"></div><div class="fr-action-buttons"><button \n  type="button" class="fr-command fr-submit" data-cmd="[COMMAND]" tabIndex="2" role="button">\n  ').concat(g.language.translate("OK"),"</button></div></div>");function s(o){for(var t="text"===o?g.opts.colorsText:g.opts.colorsBackground,r='<div class="fr-color-set fr-'.concat(o,'-color fr-selected-set">'),e=0;e<t.length;e++)0!==e&&e%g.opts.colorsStep==0&&(r+="<br>"),"REMOVE"!==t[e]?r+='<span class="fr-command fr-select-color" style="background:'.concat(t[e],';" \n        tabIndex="-1" aria-selected="false" role="button" data-cmd="apply').concat(o,'Color" \n        data-param1="').concat(t[e],'"><span class="fr-sr-only"> ').concat(g.language.translate("Color")).concat(t[e]," \n        &nbsp;&nbsp;&nbsp;</span></span>"):r+='<span class="fr-command fr-select-color" data-cmd="apply'.concat(o,'Color"\n         tabIndex="-1" role="button" data-param1="REMOVE" \n         title="').concat(g.language.translate("Clear Formatting"),'">').concat(g.icon.create("remove"),' \n        <span class="fr-sr-only"> ').concat(g.language.translate("Clear Formatting")," </span></span>");return"".concat(r,"</div>")}function i(o){var t,r=g.popups.get("".concat(o,"Color.picker")),e=E(g.selection.element());t="background"===o?"background-color":"color";var c=r.find(".fr-".concat(o,"-color .fr-select-color"));for(c.find(".fr-selected-color").remove(),c.removeClass("fr-active-item"),c.not('[data-param1="REMOVE"]').attr("aria-selected",!1);e.get(0)!==g.el;){if("transparent"!==e.css(t)&&"rgba(0, 0, 0, 0)"!==e.css(t)){var a=r.find(".fr-".concat(o,'-color .fr-select-color[data-param1="').concat(g.helpers.RGBToHex(e.css(t)),'"]'));a.append('<span class="fr-selected-color" aria-hidden="true">\uf00c</span>'),a.addClass("fr-active-item").attr("aria-selected",!0);break}e=e.parent()}!function n(o){var t=g.popups.get("".concat(o,"Color.picker")),r=t.find(".fr-".concat(o,"-color .fr-active-item")).attr("data-param1"),e=t.find(".fr-color-hex-layer input");r||(r="");e.length&&E(e.val(r).input).trigger("change")}(o)}function e(o){"REMOVE"!==o?g.format.applyStyle("background-color",g.helpers.HEXtoRGB(o)):g.format.removeStyle("background-color"),g.popups.hide("backgroundColor.picker")}function c(o){"REMOVE"!==o?g.format.applyStyle("color",g.helpers.HEXtoRGB(o)):g.format.removeStyle("color"),g.popups.hide("textColor.picker")}return{showColorsPopup:function p(o){var t=g.$tb.find('.fr-command[data-cmd="'.concat(o,'"]')),r=g.popups.get("".concat(o,".picker"));if(r||(r=function n(o){var t="";g.opts.toolbarInline&&0<g.opts.colorsButtons.length&&(t+='<div class="fr-buttons fr-colors-buttons fr-tabs">\n        '.concat(g.button.buildList(g.opts.colorsButtons),"\n        </div>"));var r,e="";r="textColor"===o?(g.opts.colorsHEXInput&&(e=l.replace(/\[ID\]/g,"fr-color-hex-layer-text-".concat(g.id)).replace(/\[COMMAND\]/g,"customTextColor")),{buttons:t,text_colors:s("text"),custom_color:e}):(g.opts.colorsHEXInput&&(e=l.replace(/\[ID\]/g,"fr-color-hex-layer-background-".concat(g.id)).replace(/\[COMMAND\]/g,"customBackgroundColor")),{buttons:t,background_colors:s("background"),custom_color:e});var c=g.popups.create("".concat(o,".picker"),r);return function a(C,b){g.events.on("popup.tab",function(o){var t=E(o.currentTarget);if(!g.popups.isVisible(b)||!t.is("span"))return!0;var r=o.which,e=!0;if(k.KEYCODE.TAB===r){var c=C.find(".fr-buttons");e=!g.accessibility.focusToolbar(c,!!o.shiftKey)}else if(k.KEYCODE.ARROW_UP===r||k.KEYCODE.ARROW_DOWN===r||k.KEYCODE.ARROW_LEFT===r||k.KEYCODE.ARROW_RIGHT===r){if(t.is("span.fr-select-color")){var a=t.parent().find("span.fr-select-color"),n=a.index(t),l=g.opts.colorsStep,s=Math.floor(a.length/l),i=n%l,p=Math.floor(n/l),u=p*l+i,d=s*l;k.KEYCODE.ARROW_UP===r?u=((u-l)%d+d)%d:k.KEYCODE.ARROW_DOWN===r?u=(u+l)%d:k.KEYCODE.ARROW_LEFT===r?u=((u-1)%d+d)%d:k.KEYCODE.ARROW_RIGHT===r&&(u=(u+1)%d);var f=E(a.get(u));g.events.disableBlur(),f.focus(),e=!1}}else k.KEYCODE.ENTER===r&&(g.button.exec(t),e=!1);return!1===e&&(o.preventDefault(),o.stopPropagation()),e},!0)}(c,"".concat(o,".picker")),c}(o)),!r.hasClass("fr-active"))if(g.popups.setContainer("".concat(o,".picker"),g.$tb),i("textColor"===o?"text":"background"),t.isVisible()){var e=g.button.getPosition(t),c=e.left,a=e.top;g.popups.show("".concat(o,".picker"),c,a,t.outerHeight())}else g.position.forSelection(r),g.popups.show("".concat(o,".picker"))},background:e,customColor:function a(o){var t=g.popups.get("".concat(o,"Color.picker")).find(".fr-color-hex-layer input");if(t.length){var r=t.val();"background"===o?e(r):c(r)}},text:c,back:function o(){g.popups.hide("textColor.picker"),g.popups.hide("backgroundColor.picker"),g.toolbar.showInline()}}},k.DefineIcon("textColor",{NAME:"tint",SVG_KEY:"textColor"}),k.RegisterCommand("textColor",{title:"Text Color",undo:!1,focus:!0,refreshOnCallback:!1,popup:!0,callback:function(){this.popups.isVisible("textColor.picker")?(this.$el.find(".fr-marker").length&&(this.events.disableBlur(),this.selection.restore()),this.popups.hide("textColor.picker")):this.colors.showColorsPopup("textColor")}}),k.RegisterCommand("applytextColor",{undo:!0,callback:function(o,t){this.colors.text(t)}}),k.RegisterCommand("customTextColor",{title:"OK",undo:!0,callback:function(){this.colors.customColor("text")}}),k.DefineIcon("backgroundColor",{NAME:"paint-brush",SVG_KEY:"backgroundColor"}),k.RegisterCommand("backgroundColor",{title:"Background Color",undo:!1,focus:!0,refreshOnCallback:!1,popup:!0,callback:function(){this.popups.isVisible("backgroundColor.picker")?(this.$el.find(".fr-marker").length&&(this.events.disableBlur(),this.selection.restore()),this.popups.hide("backgroundColor.picker")):this.colors.showColorsPopup("backgroundColor")}}),k.RegisterCommand("applybackgroundColor",{undo:!0,callback:function(o,t){this.colors.background(t)}}),k.RegisterCommand("customBackgroundColor",{title:"OK",undo:!0,callback:function(){this.colors.customColor("background")}}),k.DefineIcon("colorsBack",{NAME:"arrow-left",SVG_KEY:"back"}),k.RegisterCommand("colorsBack",{title:"Back",undo:!1,focus:!1,back:!0,refreshAfterCallback:!1,callback:function(){this.colors.back()}}),k.DefineIcon("remove",{NAME:"eraser",SVG_KEY:"remove"})});