/*!
 * froala_editor v4.5.0 (https://www.froala.com/wysiwyg-editor)
 * License https://froala.com/wysiwyg-editor/terms/
 * Copyright 2014-2025 Froala Labs
 */

!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(require("froala-editor")):"function"==typeof define&&define.amd?define(["froala-editor"],e):e(t.FroalaEditor)}(this,function(i){"use strict";i=i&&i.hasOwnProperty("default")?i["default"]:i,Object.assign(i.DEFAULTS,{charCounterMax:-1,charCounterCount:!0}),i.PLUGINS.charCounter=function(r){var n,o=r.$,a=function a(){return(r.opts.iframe&&r.markdown&&r.markdown.isEnabled()?r.$el.text()||"":r.el.textContent||"").replace(/\u200B/g,"").length};function t(t){if(r.opts.charCounterMax<0)return!0;if(a()<r.opts.charCounterMax)return!0;var e=t.which;return!(!r.keys.ctrlKey(t)&&r.keys.isCharacter(e)||e===i.KEYCODE.IME)||(t.preventDefault(),t.stopPropagation(),r.events.trigger("charCounter.exceeded"),!1)}function e(t){return r.opts.charCounterMax<0?t:o("<div>").html(t).text().length+a()<=r.opts.charCounterMax?t:(r.events.trigger("charCounter.exceeded"),"")}function c(t,e,n){return r.opts.charCounterMax<0||(t.includes('<span class="fr-emoticon')||t.includes('<i class="fa ')?!(a()>=r.opts.charCounterMax)||(r.events.trigger("charCounter.exceeded"),!1):o("<div>").html(t).text().length+a()<=r.opts.charCounterMax||(r.events.trigger("charCounter.exceeded"),!1))}function s(){if(r.opts.charCounterCount){var t=a()+(0<r.opts.charCounterMax?"/"+r.opts.charCounterMax:"");n.text("".concat(r.language.translate("Characters")," : ").concat(t)),r.opts.toolbarBottom&&n.css("margin-bottom",r.$tb.outerHeight(!0));var e=r.$wp.get(0).offsetWidth-r.$wp.get(0).clientWidth;0<=e&&("rtl"==r.opts.direction?n.css("margin-left",e):n.css("margin-right",e))}}return{_init:function u(){return!!r.$wp&&!!r.opts.charCounterCount&&((n=o(document.createElement("span")).attr("class","fr-counter")).css("bottom",r.$wp.css("border-bottom-width")),r.$second_tb?r.$second_tb.append(n):r.$wp.append(n),r.events.on("keydown",t,!0),r.events.on("paste.afterCleanup",e),r.events.on("keyup contentChanged input",function(){r.events.trigger("charCounter.update")}),r.events.on("html.beforeInsert",c),r.events.on("charCounter.update",s),r.events.trigger("charCounter.update"),void r.events.on("destroy",function(){o(r.o_win).off("resize.char".concat(r.id)),n.removeData().remove(),n=null}))},count:a}}});