/*!
 * froala_editor v4.5.0 (https://www.froala.com/wysiwyg-editor)
 * License https://froala.com/wysiwyg-editor/terms/
 * Copyright 2014-2025 Froala Labs
 */

!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(require("froala-editor")):"function"==typeof define&&define.amd?define(["froala-editor"],e):e(t.FroalaEditor)}(this,function(H){"use strict";function l(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,i=Array(e);r<e;r++)i[r]=t[r];return i}function o(t){return function e(t){if(Array.isArray(t))return l(t)}(t)||function r(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function i(t,e){if(t){if("string"==typeof t)return l(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?l(t,e):void 0}}(t)||function n(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}H=H&&H.hasOwnProperty("default")?H["default"]:H,Object.assign(H.DEFAULTS,{wordDeniedTags:[],wordDeniedAttrs:[],wordAllowedStyleProps:["font-family","font-size","background","color","width","text-align","vertical-align","background-color","padding","margin","height","margin-top","margin-left","margin-right","margin-bottom","text-decoration","font-weight","font-style","text-indent","border","border-.*","line-height","list-style-type"],wordPasteModal:!0,wordPasteKeepFormatting:!0}),H.PLUGINS.wordPaste=function(A){var a,n,C,D=A.$,s="word_paste",p={},E=!0;function e(t){var e=A.opts.wordAllowedStyleProps;t||(A.opts.wordAllowedStyleProps=[]),0===n.indexOf("<colgroup>")&&(n="<table>"+n+"</table>"),n=l(n=n.replace(/<span[\n\r ]*style='mso-spacerun:yes'>([\r\n\u00a0 ]*)<\/span>/g,function(t,e){for(var r="",i=0;i++<e.length;)r+="&nbsp;";return r}),A.paste.getRtfClipboard());var r=A.doc.createElement("DIV");r.innerHTML=n,A.html.cleanBlankSpaces(r),n=r.innerHTML,n=(n=A.paste.cleanEmptyTagsAndDivs(n)).replace(/\u200b/g,""),function i(){A.modals.hide(s)}(),A.helpers.isMobile()&&A.events.focus(),A.paste.clean(n,!0,!0),A.opts.wordAllowedStyleProps=e}function S(t){t.parentNode&&t.parentNode.removeChild(t)}function m(t,e){if(e(t))for(var r=t.firstChild;r;){var i=r,n=r.previousSibling;r=r.nextSibling,m(i,e),i.previousSibling||i.nextSibling||i.parentNode||!r||n===r.previousSibling||!r.parentNode?i.previousSibling||i.nextSibling||i.parentNode||!r||r.previousSibling||r.nextSibling||r.parentNode||(n?r=n.nextSibling?n.nextSibling.nextSibling:null:t.firstChild&&(r=t.firstChild.nextSibling)):r=n?n.nextSibling:t.firstChild}}function _(t){return D(t).hasClass("ListContainerWrapper")&&0<D(t).find("ol").length}function F(t){return D(t).hasClass("ListContainerWrapper")&&0<D(t).find("ul").length}function $(t){if(_(t)||F(t))return!0;if(!t.getAttribute("style")||!/mso-list:[\s]*l/gi.test(t.getAttribute("style").replace(/\n/gi,"")))return!1;try{if(!t.querySelector('[style="mso-list:Ignore"]'))return!!(t.outerHTML&&0<=t.outerHTML.indexOf("\x3c!--[if !supportLists]--\x3e"))}catch(e){return!1}return!0}var h=null,v={};function k(t){var e;if(_(t)||F(t))e=D(t).find("li").attr("data-aria-level");else{var r,i,n=(null===(r=t.getAttribute("style"))||void 0===r?void 0:r.replace(/\n/g,""))||"",l=h&&(null===(i=h.getAttribute("style"))||void 0===i?void 0:i.replace(/\n/g,""))||"",a=n.match(/mso-list:(\s*l([0-9]+)\s*level([0-9]+)\s*)lfo([0-9]+)/i),s=l.match(/mso-list:(\s*l([0-9]+)\s*level([0-9]+)\s*)lfo([0-9]+)/i);if(a){if(v[a[1]])return h=t,v[a[1]].toString();var o=s&&s[2],d=a[2],f=l&&v[s[1]]||0;e=o&&o!==d&&parseFloat(h.style.marginLeft)<parseFloat(t.style.marginLeft)?(f+parseInt(a[3])).toString():a[3],v[a[1]]=parseInt(e)}else e=n.replace(/.*level([0-9]+?).*/gi,"$1")}return h=t,e}function W(t,i){var e=t.cloneNode(!0);if(-1!==["H1","H2","H3","H4","H5","H6"].indexOf(t.tagName)){var r=document.createElement(t.tagName.toLowerCase());r.setAttribute("style",t.getAttribute("style")),r.innerHTML=e.innerHTML,e.innerHTML=r.outerHTML}m(e,function(t){if(t.nodeType==Node.COMMENT_NODE&&(A.browser.msie||A.browser.safari||A.browser.edge))try{if("[if !supportLists]"===t.data){for(t=t.nextSibling;t&&t.nodeType!==Node.COMMENT_NODE;){var e=t.nextSibling;t.parentNode.removeChild(t),t=e}t&&t.nodeType==Node.COMMENT_NODE&&t.parentNode.removeChild(t)}}catch(r){return!1}return t.nodeType===Node.ELEMENT_NODE&&("mso-list:\nIgnore"===t.getAttribute("style")&&t.setAttribute("style","mso-list:Ignore"),"mso-list:Ignore"===t.getAttribute("style")&&t.parentNode.removeChild(t),t.setAttribute("style",b(t)),M(t,i)),!0});var n=_(t)?D(t).find("li").get(0).innerHTML:e.innerHTML;if(_(t)||F(t)){n="";for(var l=D(t).find("li"),a=0;a<l.length;a++){var s=D(l.get(a).innerHTML);s.find("span").each(function(){"&nbsp;"===D(this).html()&&(0<D(this).prev().text().trim().length?D(this).remove():D(this).html("&ZeroWidthSpace;"))});var o=l[a].style.marginLeft;o?(o=parseFloat(parseFloat(o).toFixed(2))+o.match(/[a-zA-Z]+/g)[0],n+='<li style = "margin-left: '.concat(o,';">').concat(s.get(0).innerHTML,"</li>")):n+="<li >".concat(s.get(0).innerHTML,"</li>")}}else{var d,f=null===(d=D(t).find("p").get(0))||void 0===d?void 0:d.style.marginLeft;n=f?(f=parseFloat(parseFloat(f).toFixed(2))+f.match(/[a-zA-Z]+/g)[0],'<li style = "margin-left: '.concat(f,';">').concat(n,"</li>")):"<li>".concat(n,"</li>")}return n=n.replace(/<!--[\s\S]*?-->/gi,"")}function b(t){var e=1<arguments.length&&arguments[1]!==undefined?arguments[1]:null,r="",i=["line-height","font-family","font-size","color","background"].concat(o(e||[])),n=t.getAttribute("style");return n&&i.forEach(function(e){var t=n.split(";").find(function(t){return t.trim().includes(e+":")});t&&(t.includes("mso")&&(t=t.match(new RegExp(e+":.*(;|)"))[0]),t.trim().startsWith(e)&&(r+=t+";"))}),r}function y(t,e){for(var r=document.createElement(e),i=0;i<t.attributes.length;i++){var n=t.attributes[i].name;"FONT"===t.tagName&&(A.browser.msie&&["id","class","style"].indexOf(n)<0||!A.browser.msie&&CSS.supports(n,t.getAttribute(n)))?D(r).css(n,t.getAttribute(n)):r.setAttribute(n,t.getAttribute(n))}return r.innerHTML=t.innerHTML,t.parentNode.replaceChild(r,t),r}function N(t,e){A.node.clearAttributes(t);for(var r=t.firstElementChild,i=0,n=!1,l=null;r;){if(r.firstElementChild&&-1!==r.firstElementChild.tagName.indexOf("W:"))for(var a=0;a<r.children.length;a++)"<('[^']*'|\"[^\"]*\"|[^\"'>])*>"==r.children[a]&&(r.innerHTML+=r.children[a]);(l=r.getAttribute("width"))||(l=r.style.width),l||n||(n=!0),i+=parseInt(l,10),(!r.firstChild||r.firstChild&&r.firstChild.data===H.UNICODE_NBSP)&&(r.firstChild&&S(r.firstChild),r.innerHTML="<br>");for(var s=r.firstElementChild,o=1===r.children.length;s;)"P"!==s.tagName||$(s)||o&&T(s),s=s.nextElementSibling;if(e){var d=r.getAttribute("class");if(d){var f=(d=L(d)).match(/xl[0-9]+/gi);if(f){var u="."+f[0];e[u]&&w(r,e[u])}}e.td&&w(r,e.td)}var g=r.getAttribute("style");g&&(g=L(g))&&";"!==g.slice(-1)&&(g+=";");var c=r.getAttribute("valign");if(!c&&g){var p=g.match(/vertical-align:.+?[; "]{1,1}/gi);p&&(c=p[p.length-1].replace(/vertical-align:(.+?)[; "]{1,1}/gi,"$1"))}var m=null;if(g){var h=g.match(/text-align:.+?[; "]{1,1}/gi);h&&(m=h[h.length-1].replace(/text-align:(.+?)[; "]{1,1}/gi,"$1")),"general"===m&&(m=null)}var v=null;if(g){var b=g.match(/background:.+?[; "]{1,1}/gi);b&&(v=b[b.length-1].replace(/background:(.+?)[; "]{1,1}/gi,"$1"))}var y=r.getAttribute("colspan"),N=r.getAttribute("rowspan");y&&r.setAttribute("colspan",y),N&&r.setAttribute("rowspan",N),c&&(r.style["vertical-align"]=c),m&&(r.style["text-align"]=m),v&&(r.style["background-color"]=v),l&&r.setAttribute("width",l),r=r.nextElementSibling}for(r=t.firstElementChild;r;)l=r.getAttribute("width"),n&&r.removeAttribute("width"),r=r.nextElementSibling;t.previousSibling?C!=i&&(E=!1):C=i,t.nextElementSibling||function x(t){var e=t.closest("table"),r=0,n=function n(){var i=0;Array.from(t.children).forEach(function(t){var e=parseInt(t.getAttribute("width"),10);isNaN(e)||(i+=e)}),r<i&&(r=i),E?Array.from(t.children).forEach(function(t){var e=parseInt(t.getAttribute("width"),10);if(!isNaN(e)&&0<i){var r=100*e/i;t.setAttribute("width","".concat(r,"%"))}}):Array.from(t.children).forEach(function(t){var e=parseInt(t.getAttribute("width"),10);isNaN(e)||t.setAttribute("width","".concat(e,"px"))}),t=t.previousElementSibling};for(;t;)n();if(e&&0<r){var i=parseInt(e.style.width,10);(isNaN(i)||0===i)&&(e.style.width="".concat(r,"px"))}}(t)}function T(t){var e=t.getAttribute("align");e&&(t.style["text-align"]=e,t.removeAttribute("align"))}function L(t){return t.replace(/\n|\r|\n\r|&quot;/g,"")}function w(t,e,r){if(e){var i=t.getAttribute("style");i&&";"!==i.slice(-1)&&(i+=";"),e&&";"!==e.slice(-1)&&(e+=";"),e=e.replace(/\n/gi,"");var n=null;n=r?(i||"")+e:e+(i||""),t.setAttribute("style",n)}}var c=null;function d(t,e,r){for(var i=t.split(r),n=1;n<i.length;n++){var l=i[n];if(1<(l=l.split("shplid")).length){l=l[1];for(var a="",s=0;s<l.length&&"\\"!==l[s]&&"{"!==l[s]&&" "!==l[s]&&"\r"!==l[s]&&"\n"!==l[s];)a+=l[s],s++;var o=l.split("bliptag");if(o&&o.length<2)continue;var d=null;if(-1!==o[0].indexOf("pngblip")?d="image/png":-1!==o[0].indexOf("jpegblip")&&(d="image/jpeg"),!d)continue;var f=o[1].split("}");if(f&&f.length<2)continue;var u=void 0;if(2<f.length&&-1!==f[0].indexOf("blipuid"))u=f[1].split(" ");else{if((u=f[0].split(" "))&&u.length<2)continue;u.shift()}var g=u.join("");c[e+a]={image_hex:g,image_type:d}}}}function x(t,e){if(e){var r;if("IMG"===t.tagName){var i=t.getAttribute("src");if(!i||-1===i.indexOf("file://"))return;if(0===i.indexOf("file://")&&A.helpers.isURL(t.getAttribute("alt")))return void t.setAttribute("src",t.getAttribute("alt"));(r=p[t.getAttribute("v:shapes")])||(r=t.getAttribute("v:shapes"),t.parentNode&&t.parentNode.parentNode&&0<=t.parentNode.parentNode.innerHTML.indexOf("msEquation")&&(r=null))}else r=t.parentNode.getAttribute("o:spid");if(t.removeAttribute("height"),r){!function s(t){c={},d(t,"i","\\shppict"),d(t,"s","\\shp{")}(e);var n=c[r.substring(7)];if(n){var l=function o(t){for(var e=t.match(/[0-9a-f]{2}/gi),r=[],i=0;i<e.length;i++)r.push(String.fromCharCode(parseInt(e[i],16)));var n=r.join("");return btoa(n)}(n.image_hex),a="data:"+n.image_type+";base64,"+l;"IMG"===t.tagName?(t.src=a,t.setAttribute("data-fr-image-pasted",!0)):D(t).parent().before('<img data-fr-image-pasted="true" src="'.concat(a,'" style="').concat(t.parentNode.getAttribute("style"),'">')).remove()}}}}function M(t,e){var r=t.tagName,i=r.toLowerCase();t.firstElementChild&&("I"===t.firstElementChild.tagName?y(t.firstElementChild,"em"):"B"===t.firstElementChild.tagName&&y(t.firstElementChild,"strong"));if(-1!==["SCRIPT","APPLET","EMBED","NOFRAMES","NOSCRIPT"].indexOf(r))return S(t),!1;for(var n=["META","LINK","XML","ST1:","O:","W:","FONT"],l=0;l<n.length;l++)if(-1!==r.indexOf(n[l]))return t.innerHTML&&(t.outerHTML=t.innerHTML),S(t),!1;if("TD"!==r){var a=t.getAttribute("class")||"MsoNormal";if(e&&a){for(var s=(a=L(a)).split(" "),o=0;o<s.length;o++){var d=[],f="."+s[o];d.push(f),f=i+f,d.push(f);for(var u=0;u<d.length;u++)e[d[u]]&&w(t,e[d[u]])}t.removeAttribute("class")}e&&e[i]&&w(t,e[i])}if(-1!==["P","H1","H2","H3","H4","H5","H6","PRE"].indexOf(r)){var g=t.getAttribute("class");if(g&&(e&&e[r.toLowerCase()+"."+g]&&w(t,e[r.toLowerCase()+"."+g]),-1!==g.toLowerCase().indexOf("mso"))){var c=L(g);(c=c.replace(/[0-9a-z-_]*mso[0-9a-z-_]*/gi,""))?t.setAttribute("class",c):t.removeAttribute("class")}var p=t.getAttribute("style");if(p){var m=p.match(/text-align:.+?[; "]{1,1}/gi);m&&m[m.length-1].replace(/(text-align:.+?[; "]{1,1})/gi,"$1")}T(t)}if("TR"===r&&N(t,e),"A"!==r||t.attributes.getNamedItem("href")||t.attributes.getNamedItem("name")||!t.innerHTML||(t.outerHTML=t.innerHTML),"A"==r&&t.getAttribute("href")&&t.querySelector("img"))for(var h=t.querySelectorAll("span"),v=0;v<h.length;v++)h[v].innerText||(h[v].outerHTML=h[v].innerHTML);if("TD"!==r&&"TH"!==r||t.innerHTML||(t.innerHTML="<br>"),t.getAttribute("lang")&&t.removeAttribute("lang"),t.getAttribute("style")&&-1!==t.getAttribute("style").toLowerCase().indexOf("mso")){var b=L(t.getAttribute("style"));(b=b.replace(/[0-9a-z-_]*mso[0-9a-z-_]*:.+?(;{1,1}|$)/gi,"").replace("line-height:107%",""))?t.setAttribute("style",b):t.removeAttribute("style")}return!0}function l(t,e){0<=t.indexOf("<html")&&(t=t.replace(/[.\s\S\w\W<>]*(<html[^>]*>[.\s\S\w\W<>]*<\/html>)[.\s\S\w\W<>]*/i,"$1"));var o=t.match(/(MSFontService|class="?Mso|class='?Mso|class="?Xl|class='?Xl|class=Xl|style="[^"]*\bmso-|style='[^']*\bmso-|w:WordDocument|LibreOffice)/gi);!function u(t){for(var e=t.split("v:shape"),r=1;r<e.length;r++){var i=e[r],n=i.split(' id="')[1];if(n&&1<n.length){n=n.split('"')[0];var l=i.split(' o:spid="')[1];l&&1<l.length&&(l=l.split('"')[0],p[n]=l)}}}(t);var r=(new DOMParser).parseFromString(t,"text/html"),i=r.head,n=r.body,d=function g(t){var e={},r=t.getElementsByTagName("style");if(r.length){var i=r[0].innerHTML.match(/[\S ]+\s+{[\s\S]+?}/gi);if(i)for(var n=0;n<i.length;n++){var l=i[n],a=l.replace(/([\S ]+\s+){[\s\S]+?}/gi,"$1"),s=l.replace(/[\S ]+\s+{([\s\S]+?)}/gi,"$1");a=a.replace(/^[\s]|[\s]$/gm,""),s=s.replace(/^[\s]|[\s]$/gm,""),a=a.replace(/\n|\r|\n\r/g,""),s=s.replace(/\n|\r|\n\r/g,"");for(var o=a.split(", "),d=0;d<o.length;d++)e[o[d]]=s}}return e}(i);m(n,function(t){if(t.nodeType===Node.ELEMENT_NODE&&t.hasAttribute("style")){var e=t.getAttribute("style");t.setAttribute("style",e),t.style.fontFamily||t.setAttribute("style",function r(t){return t.replace(/mso-(bidi|fareast|fareast-theme)-font-family/g,"font-family")}(e))}return!0}),m(n,function(t){if(t.nodeType===Node.TEXT_NODE&&/\n|\u00a0|\r/.test(t.data)){if(!/\S| /.test(t.data)&&!/[\u00a0]+/.test(t.data))return t.data===H.UNICODE_NBSP?(t.data="\u200b",!0):1===t.data.length&&10===t.data.charCodeAt(0)?(t.data=" ",!0):(S(t),!1);t.data=t.data.replace(/\n|\r/gi," ")}return!0}),m(n,function(t){return t.nodeType!==Node.ELEMENT_NODE||"V:IMAGEDATA"!==t.tagName&&"IMG"!==t.tagName||x(t,e),!0});for(var l=n.querySelectorAll("ul > ul, ul > ol, ol > ul, ol > ol"),a=l.length-1;0<=a;a--)l[a].previousElementSibling&&"LI"===l[a].previousElementSibling.tagName&&l[a].previousElementSibling.appendChild(l[a]);m(n,function(t){if(t.nodeType===Node.TEXT_NODE)return t.data=t.data.replace(/<br>(\n|\r)/gi,"<br>"),!1;if(t.nodeType===Node.ELEMENT_NODE){if($(t)){var e=t.parentNode,r=t.previousSibling,i=function P(t,e,r,i){var n,l,a,s,o,d,f,u=navigator.userAgent.toLowerCase();-1!=u.indexOf("safari")&&(u=-1<u.indexOf("chrome")?1:"safari"),t.innerHTML.includes("mso-list:\nIgnore")&&(t.innerHTML=t.innerHTML.replace(/mso-list:\nIgnore/gi,"mso-list:Ignore"));var g,c,p,m,h=/[0-9a-zA-Z]./gi,v="",b=_(t),y=null===(n=D(t).find("ol"))||void 0===n||null===(n=n.attr("style"))||void 0===n||null===(n=n.split(";"))||void 0===n?void 0:n.find(function(t){return t.trim().startsWith("list-style-type")});y==undefined&&F(t)&&(y=null===(c=D(t).find("ul"))||void 0===c||null===(c=c.attr("style"))||void 0===c||null===(c=c.split(";"))||void 0===c?void 0:c.find(function(t){return t.trim().startsWith("list-style-type")}));if(b)d="ol",f=y?"list-style-type: "+y.replace("list-style-type:","").trim()+";":"";else if(F(t))d="ul",f=y?"list-style-type: "+y.replace("list-style-type:","").trim()+";":"";else{var N=t.querySelector('span[style="mso-list:Ignore"]');null==N&&"safari"==u&&(N=t.querySelector('span[lang="PT-BR"]'));var x=!1;N&&(x=x||h.test(N.textContent)),null!==N&&(g=N.textContent.trim().split(".")[0]),d=1==x?(g=(g=N.textContent.trim().split(".")[0]).replace(/\(([^)]+)\)|[)]$/g,"$1"),isNaN(parseInt(g))?/^(m{0,4}(cm|cd|d?c{0,3})(xc|xl|l?x{0,3})(ix|iv|v?i{0,3}))$/.test(g)?(f="lower-roman;",t.nextElementSibling&&1===t.nextElementSibling.textContent.trim().split(".")[0].length&&(f="lower-alpha;")):/^(M{0,4}(CM|CD|D?C{0,3})(XC|XL|L?X{0,3})(IX|IV|V?I{0,3}))$/.test(g)?(f="upper-roman;",t.nextElementSibling&&1===t.nextElementSibling.textContent.trim().split(".")[0].length&&(f="upper-alpha;")):"o"==g?f="circle;":g.match(/^v$/)||(g.match(/^[a-z]$/)||g.match(/^[a-z]\)$/)?f="lower-alpha;":(g.match(/^[A-Z]$/)||g.match(/^[A-Z]\)$/))&&(f="upper-alpha;")):f="decimal;",f=f?"list-style-type: "+f:"","ol"):(null!=N&&(g=N.textContent.trim().split(".")[0]),"\xa7"==g?f="square;":"\xb7"==g&&(f="disc;"),f=f?"list-style-type: "+f:"","ul"),N==undefined||N.textContent==undefined||isNaN(parseInt(N.textContent.trim().split(".")[1],10))||(v=' class="decimal_type" ')}var A=1,C="",E=(k(t),t.style.marginLeft),S="";E.includes("in")?(S="in",E=parseFloat(E)-.5):E.includes("pt")&&(S="px",E=parseFloat(E)-10),E&&(m=" margin-left: "+E+S+";"),A="list-style-type: upper-alpha;"==f?b?parseInt(D(t).find("ol").attr("start")):g.charCodeAt(0)-64:"list-style-type: lower-alpha;"==f?b?parseInt(D(t).find("ol").attr("start")):g.charCodeAt(0)-96:"list-style-type: upper-roman;"==f||"list-style-type: lower-roman;"==f?b?parseInt(D(t).find("ol").attr("start")):function I(t){for(var e={I:1,V:5,X:10,L:50,C:100,D:500,M:1e3,i:1,v:5,x:10,l:50,c:100,d:500,m:1e3},r=0,i=0,n=t.length-1;0<=n;n--){var l=e[t[n]];i<=l?r+=l:r-=l,i=l}return r}(g):b?parseInt(D(t).find("ol").attr("start")):parseInt(g),C+=f||"",C+=m||"",p="<"+d,"none"===t.style.display&&(p+=' class="fr-mso-hidden"'),p+=v||"",p+="ol"!=d||1==A||isNaN(A)?"":' start = "'+A+'"',p+=C?' style = "'+C+'"':"",p+=">";for(var T=!1;t;){if(!$(t)){if(t.outerHTML&&0<t.outerHTML.indexOf("mso-bookmark")&&0==(t.textContent||"").length){t=t.nextElementSibling;continue}break}var L=k(t);if((r=r||L)<L)p+=(o=P(t,e,L,t.style.marginLeft)).el.outerHTML,t=o.currentNode;else{if(L<r)break;t.firstElementChild&&t.firstElementChild.firstElementChild&&t.firstElementChild.firstElementChild.firstChild&&(h.lastIndex=0),a&&a.firstElementChild&&a.firstElementChild.firstElementChild&&a.firstElementChild.firstElementChild.firstChild&&(h.lastIndex=0,l=h.test(a.firstElementChild.firstElementChild.firstChild.data||a.firstElementChild.firstElementChild.firstChild.firstChild&&a.firstElementChild.firstElementChild.firstChild.firstChild.data||""));var w=!1,M=a&&k(a);(!i&&!t.style.marginLeft||i&&t.style.marginLeft&&i===t.style.marginLeft||M&&M===L)&&(w=!0),i=t.style.marginLeft,w||l===undefined?(s=W(t,e),t.nextSibling&&t.nextSibling.innerText!=undefined&&t.nextSibling.innerText!=undefined&&!p.includes('class="decimal_type"')&&(isNaN(parseInt(t.nextSibling.innerText.trim().split(".")[1],10))||(p=p.substring(3,0)+' class="decimal_type"'+p.substring(3,p.length))),p+=s):(1==L&&(p+="</"+d+">",T=!0,a=null),p+=(o=P(t,e,L,t.style.marginLeft)).el.outerHTML,t=o.currentNode);var H=t&&t.nextElementSibling;if(H&&(a=H.previousElementSibling),t&&!$(t)){if(t.outerHTML&&0<t.outerHTML.indexOf("mso-bookmark")&&0==(t.textContent||"").length){t=t.nextElementSibling;continue}break}t&&t.parentNode&&t.parentNode.removeChild(t),t=H}}T||(p+="</"+d+">");var O=document.createElement("div");return O.innerHTML=p,{el:O,currentNode:t}}(t,d).el,n=null;return(n=r?r.nextSibling:e.firstChild)?e.insertBefore(i,n):e.appendChild(i),!1}return"FONT"===t.tagName&&(o&&"LibreOffice"===o[0]||d["."+t.getAttribute("class")])?t=y(t,"span"):o&&"\x3c!--StartFragment--\x3e"===o[0]&&"UL"===t.tagName&&(D(t).find("li span").each(function(){"&nbsp;"===D(this).html()&&(0<D(this).prev().text().trim().length?D(this).remove():D(this).html("&ZeroWidthSpace;"))}),D(t).is("ul")&&t.setAttribute("style",b(t,["margin","margin-left","list-style-type"])),D(t).find("ul").each(function(){this.setAttribute("style",b(this,["margin","margin-left","list-style-type"]))})),"img"!==t.tagName.toLowerCase()&&function s(e){Array.from(e.attributes).map(function(t){return t.name}).filter(function(t){return t.toLowerCase().startsWith("data-")||t.toLowerCase().startsWith("xml:")}).forEach(function(t){D(e).removeAttr(t)})}(t),M(t,d)}if(t.nodeType!==Node.COMMENT_NODE)return!0;if(-1<t.data.indexOf("[if !supportLineBreakNewLine]"))for(var l=t.nextSibling;l;)(l=t.nextSibling)&&S(l),l.data&&-1<l.data.indexOf("[endif]")&&(l=null);if(-1<t.data.indexOf("[if supportFields]")&&-1<t.data.indexOf("FORMCHECKBOX")){var a=document.createElement("input");a.type="checkbox",t.parentNode.insertBefore(a,t.nextSibling)}return S(t),!1}),h=null,v={},n.innerHTML=function c(t){var e=D(document.createElement("div")).attr("id","top-level");e.html(t);for(var r=e[0].querySelectorAll("ol, ul"),i=0;i<r.length;i++)if(D(r[i]).parentsUntil("#top-level","ol, ul").length){r[i].style.removeProperty("margin-left");for(var n=r[i].children,l=0;l<n.length;l++)"LI"==n[l].tagName&&n[l].style.removeProperty("margin-left")}return e[0].innerHTML}(n.innerHTML),m(n,function(t){if(t.nodeType===Node.ELEMENT_NODE){var e=t.tagName;if(!t.innerHTML&&-1===["BR","IMG","INPUT"].indexOf(e)){for(var r=t.parentNode;r&&(S(t),!(t=r).innerHTML)&&"TD"!==t.parentNode.tagName;)r=t.parentNode;return!1}!function u(t){var e=t.getAttribute("style");if(e){(e=L(e))&&";"!==e.slice(-1)&&(e+=";");var r=e.match(/(^|\S+?):.+?;{1,1}/gi);if(r){for(var i={},n=0;n<r.length;n++){var l=r[n].split(":");2===l.length&&("text-align"===l[0]&&"SPAN"===t.tagName||(i[l[0]]=l[1]))}var a="";for(var s in i)if(i.hasOwnProperty(s)){if("font-size"===s&&"pt;"===i[s].slice(-3)){var o=null;try{o=parseFloat(i[s].slice(0,-3),10)}catch(f){o=null}if(o){var d=A.opts.fontSizeUnit;"px"===d?(o=Math.round(1.33*o),i[s]=o+"px;"):"pt"===d&&(o=Math.round(o),i[s]=o+"pt;")}}a+=s+":"+i[s]}a&&t.setAttribute("style",a)}}}(t)}return!0}),m(n,function(t){if(t&&"A"===t.nodeName){if(t.hasAttribute("name")&&t.getAttribute("name").startsWith("_Toc")&&t.parentNode&&(t.parentNode.id=t.getAttribute("name")),""===t.href){for(var e=document.createDocumentFragment();t.firstChild;)e.appendChild(t.firstChild);t.parentNode.replaceChild(e,t)}t.hasAttribute("href")&&t.getAttribute("href").startsWith("#_Toc")&&(t.removeChild(t.lastChild),t.removeChild(t.lastChild))}return!0}),m(n,function(t){return t&&"P"===t.tagName&&"DIV"===t.parentNode.tagName&&1==t.parentNode.children.length&&D(t).unwrap(),!0}),m(n,function(t){if(t&&"SPAN"===t.tagName)for(;t.nextSibling&&t.nextSibling.nodeType===Node.ELEMENT_NODE&&"SPAN"===t.nextSibling.tagName&&t.getAttribute("style")===t.nextSibling.getAttribute("style");)D(t).append(t.nextSibling.innerHTML),D(t.nextSibling).remove();return!0});var s=n.outerHTML,f=A.opts.htmlAllowedStyleProps;return A.opts.htmlAllowedStyleProps=A.opts.wordAllowedStyleProps,s=A.clean.html(s,A.opts.wordDeniedTags,A.opts.wordDeniedAttrs,!1),A.opts.htmlAllowedStyleProps=f,s}return{_init:function t(){A.events.on("paste.wordPaste",function(t){return n=t,A.opts.wordPasteModal?function l(){if(!a){var t='<h4><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 74.95 73.23" style="height: 25px; vertical-align: text-bottom; margin-right: 5px; display: inline-block"><defs><style>.a{fill:#2a5699;}.b{fill:#fff;}</style></defs><path class="a" d="M615.15,827.22h5.09V834c9.11.05,18.21-.09,27.32.05a2.93,2.93,0,0,1,3.29,3.25c.14,16.77,0,33.56.09,50.33-.09,1.72.17,3.63-.83,5.15-1.24.89-2.85.78-4.3.84-8.52,0-17,0-25.56,0v6.81h-5.32c-13-2.37-26-4.54-38.94-6.81q0-29.8,0-59.59c13.05-2.28,26.11-4.5,39.17-6.83Z" transform="translate(-575.97 -827.22)"/><path class="b" d="M620.24,836.59h28.1v54.49h-28.1v-6.81h22.14v-3.41H620.24v-4.26h22.14V873.2H620.24v-4.26h22.14v-3.41H620.24v-4.26h22.14v-3.41H620.24v-4.26h22.14v-3.41H620.24V846h22.14v-3.41H620.24Zm-26.67,15c1.62-.09,3.24-.16,4.85-.25,1.13,5.75,2.29,11.49,3.52,17.21,1-5.91,2-11.8,3.06-17.7,1.7-.06,3.41-.15,5.1-.26-1.92,8.25-3.61,16.57-5.71,24.77-1.42.74-3.55,0-5.24.09-1.13-5.64-2.45-11.24-3.47-16.9-1,5.5-2.29,10.95-3.43,16.42q-2.45-.13-4.92-.3c-1.41-7.49-3.07-14.93-4.39-22.44l4.38-.18c.88,5.42,1.87,10.82,2.64,16.25,1.2-5.57,2.43-11.14,3.62-16.71Z" transform="translate(-575.97 -827.22)"/></svg> '+A.language.translate("Word Paste Detected")+"</h4>",e=function n(){var t='<div class="fr-word-paste-modal" style="padding: 20px 20px 10px 20px;">';return t+='<p style="text-align: left;">'+A.language.translate("The pasted content is coming from a Microsoft Word document. Do you want to keep the format or clean it up?")+"</p>",t+='<div style="text-align: right; margin-top: 50px;"><button class="fr-remove-word fr-command">'+A.language.translate("Clean")+'</button> <button class="fr-keep-word fr-command">'+A.language.translate("Keep")+"</button></div>",t+="</div>"}(),r=A.modals.create(s,t,e),i=r.$body;a=r.$modal,r.$modal.addClass("fr-middle"),A.events.bindClick(i,"button.fr-remove-word",function(){var t=a.data("instance")||A;t.wordPaste.clean()}),A.events.bindClick(i,"button.fr-keep-word",function(){var t=a.data("instance")||A;t.wordPaste.clean(!0)}),A.events.$on(D(A.o_win),"resize",function(){A.modals.resize(s)})}A.modals.show(s),A.modals.resize(s)}():e(A.opts.wordPasteKeepFormatting),!1})},clean:e,_wordClean:l}}});