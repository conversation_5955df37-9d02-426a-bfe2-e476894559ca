/*!
 * froala_editor v4.5.0 (https://www.froala.com/wysiwyg-editor)
 * License https://froala.com/wysiwyg-editor/terms/
 * Copyright 2014-2025 Froala Labs
 */

!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(require("froala-editor")):"function"==typeof define&&define.amd?define(["froala-editor"],t):t(e.FroalaEditor)}(this,function(o){"use strict";o=o&&o.hasOwnProperty("default")?o["default"]:o,Object.assign(o.DEFAULTS,{listAdvancedTypes:!0}),o.PLUGINS.lists=function(g){var u=g.$;function h(e){return'<span class="fr-open-'.concat(e.toLowerCase(),'"></span>')}function N(e){return'<span class="fr-close-'.concat(e.toLowerCase(),'"></span>')}function c(e,t){!function c(e,t){for(var a=[],n=0;n<e.length;n++){var r=e[n].parentNode;"LI"==e[n].tagName&&r.tagName!=t&&a.indexOf(r)<0&&a.push(r)}for(var o=a.length-1;0<=o;o--){var l=u(a[o]);l.replaceWith("<".concat(t.toLowerCase()," ").concat(g.node.attributes(l.get(0)),">").concat(l.html(),"</").concat(t.toLowerCase(),">"))}}(e,t);var a,n=g.html.defaultTag(),r=null;e.length&&(a="rtl"==g.opts.direction||"rtl"==u(e[0]).css("direction")?"margin-right":"margin-left");for(var o=0;o<e.length;o++)if("TD"!=e[o].tagName&&"TH"!=e[o].tagName&&"LI"!=e[o].tagName){var l=g.helpers.getPX(u(e[o]).css(a))||0;(e[o].style.marginLeft=null)===r&&(r=l);var i=0<r?"<".concat(t,' style="').concat(a,": ").concat(r,'px ">'):"<".concat(t,">"),s="</".concat(t,">");for(l-=r;0<l/g.opts.indentMargin;)i+="</".concat(t,">"),s+=s,l-=g.opts.indentMargin;n&&e[o].tagName.toLowerCase()==n?u(e[o]).replaceWith("".concat(i,"<li").concat(g.node.attributes(e[o]),">").concat(u(e[o]).html(),"</li>").concat(s)):u(e[o]).wrap("".concat(i,"<li></li>").concat(s))}g.clean.lists()}function d(e){var t,a,n=[];for(t=e.length-1;0<=t;t--)for(a=t-1;0<=a;a--){if(u(e[a]).find(e[t]).length||e[a]==e[t]){u(e[t]).addClass("fr-removed-block-".concat(t)),n.push(t),e.splice(t,1);break}if(u(e[a]).contains(e[t])){u(e[a]).addClass("fr-removed-block-".concat(a)),n.push(a),e.splice(a,1);break}}var r=[],o=!1;for(t=0;t<e.length;t++){var l=u(e[t]),i=e[t].parentNode,s=l.attr("class");if(l.is(":first-child")&&(o=!0,u(i.parentNode).removeAttr("start")),l.before(N(i.tagName)),"LI"==i.parentNode.tagName){if(l.is(":last-child")){var c=l.find("> ul, > ol").last();c.length&&c.wrap("<".concat(i.tagName,">"))}l.before(N("LI")),l.after(h("LI"))}else if("OL"==i.parentNode.tagName)o||(l.before(N("OL")),l.after(h("OL")));else if("UL"==i.parentNode.tagName)o||(l.before(N("UL")),l.after(h("UL")));else{var d="";s&&(d+=' class="'.concat(s,'"'));var p="rtl"==g.opts.direction||"rtl"==l.css("direction")?"margin-right":"margin-left";g.helpers.getPX(u(i).css(p))&&0<=(u(i).attr("style")||"").indexOf("".concat(p,":"))&&(d+=' style="'.concat(p,":").concat(g.helpers.getPX(u(i).css(p)),'px;"')),g.html.defaultTag()&&0===l.find(g.html.blockTagsQuery()).length&&l.wrapInner(g.html.defaultTag()+d),g.node.isEmpty(l.get(0),!0)||0!==l.find(g.html.blockTagsQuery()).length||("DIV"==i.parentNode.tagName&&g.html.defaultTag()?(l.prepend(h(g.html.defaultTag())),l.append(N(g.html.defaultTag()))):l.append("<br>")),l.append(h("LI")),l.prepend(N("LI"))}l.after(h(i.tagName)),"LI"==i.parentNode.tagName&&(i=i.parentNode.parentNode),r.indexOf(i)<0&&r.push(i)}for(t=0;t<r.length;t++){var f=u(r[t]),m=f.html();m=(m=m.replace(/<span class="fr-close-([a-z]*)"><\/span>/g,"</$1>")).replace(/<span class="fr-open-([a-z]*)"><\/span>/g,"<$1>"),f.replaceWith(g.node.openTagString(f.get(0))+m+g.node.closeTagString(f.get(0)))}return g.$el.find("li:empty").remove(),g.$el.find("ul:empty, ol:empty").remove(),g.clean.lists(),g.$el.find("ul:empty, ol:empty").remove(),g.html.wrap(),n}function m(e,t){var a=u(document.createElement("li"));if(e.childNodes.length)for(var n=e.childNodes[0].attributes,r=0;r<n.length;r++){var o=n[r];"style"===o.name&&(a[0].style.cssText+=o.value)}u(e).prepend(a),u(e).removeAttr("start");for(var l=g.node.contents(t)[0];l&&!g.node.isList(l);){var i=l.nextSibling;a.append(l),l=i}}function e(e){if("indent"==e||"outdent"==e){var t=!1,a=g.selection.blocks(!1,!0),n=[],r=a[0].previousSibling||a[0].parentElement;if("outdent"==e){if("UL"!=a[0].parentNode.tagName&&"UL"!=r.parentNode.tagName&&"OL"!=r.parentNode.tagName&&"LI"!=r.parentNode.tagName)return;if(!a[0].previousSibling&&"none"==r.parentNode.style.listStyleType)return void function l(e){for(g.selection.save();0<e.childNodes.length;)e.parentNode.parentNode.append(e.childNodes[0]);g.clean.lists(),g.selection.restore()}(r)}else{if("UL"!=a[0].parentElement.tagName&&"OL"!=a[0].parentElement.tagName&&"LI"!=a[0].parentElement.tagName)return;if(g.node.isBlock(a[0])&&"LI"!==a[0].tagName&&0<u(a[0]).parentsUntil(g.$el,"LI").length&&(a[0]=a[0].parentElement,r=a[0].parentElement),!a[0].previousSibling||"LI"!=a[0].previousSibling.tagName){if(0==u(a[0]).parents("li").length)return void function i(e){g.selection.save();var t="OL"==e.tagName?document.createElement("ol"):document.createElement("ul");for(u(t).css("list-style-type",u(e).css("list-style-type"));0<e.childNodes.length;)t.append(e.childNodes[0]);var a=document.createElement("li");t.append(a),e.append(t),g.clean.lists(),g.selection.restore()}(r);t=!0}}for(var o=0;o<a.length;o++)"LI"==a[o].tagName?(t=!0,n.push(a[o])):"LI"==a[o].parentNode.tagName&&n.indexOf(a[o].parentNode)<0&&(t=!0,n.push(a[o].parentNode));t&&("indent"==e?function f(e){g.selection.save();for(var t=!1,a=0;a<e.length;a++){var n=e[a].previousSibling;if(n&&"LI"==n.tagName&&!t){var r=u(e[a]).find("> ul, > ol").last().get(0);if(r)m(r,e[a]),u(n).append(u(r)),u(e[a]).remove();else{var o=u(n).find("> ul, > ol").last().get(0);if(o)u(o).append(u(e[a]));else{var l=u("<".concat(e[a].parentNode.tagName,">"));u(n).append(l),l.append(u(e[a]))}}}else if(!n||"OL"!=n.tagName&&"UL"!=n.tagName){var i=e[a].parentNode;if(t=!0,"UL"==i.tagName||"OL"==i.tagName){var s=u(e[a]).find("> ol, > ul"),c=null==e[a].previousSibling;if(s.length){var d=s.first().get(0);m(d,e[a]),c?u(i).prepend(u(d)):u(i).append(u(d)),u(e[a]).remove()}else u(e[a]).wrap("<".concat(i.tagName,">"));c&&0<u(i).find("> li").length&&u(i).attr("start","2")}}else{var p=u(e[a]).find("> ol, > ul").first().get(0);p?(m(p,e[a]),u(n).append(u(p).children()),u(e[a]).remove(),u(p).remove()):u(n).append(u(e[a]))}}g.clean.lists(),g.selection.restore()}(n):function s(e){g.selection.save();var t=[];do{t=d(e),e=[];for(var a=0;a<t.length;a++){var n=g.$el.find(".fr-removed-block-".concat(t[a])).first().get(0);u(n).removeClass("fr-removed-block-".concat(t[a])),""==u(n).attr("class")&&u(n).removeAttr("class"),e.push(n)}}while(0<t.length);g.selection.restore()}(n))}}return{_init:function t(){g.events.on("commands.after",e),g.events.on("keydown",function(e){if(e.which==o.KEYCODE.TAB){for(var t=g.selection.blocks(),a=[],n=0;n<t.length;n++)"LI"==t[n].tagName?a.push(t[n]):"LI"==t[n].parentNode.tagName&&a.push(t[n].parentNode);if(1<=a.length||a.length&&(g.selection.info(a[0]).atStart||g.node.isEmpty(a[0])))return e.preventDefault(),e.stopPropagation(),e.shiftKey?g.commands.outdent():g.commands.indent(),!1}},!0)},format:function p(e,t){var a,n,r=1<(n=g.selection.blocks(!0)).length&&n[n.length-1],o=g.selection.ranges();if(r&&o.length&&g.node.isEmpty(r)&&!o[0].endOffset&&o[0].setEndAfter(r.firstChild,1),g.html.syncInputs(),g.selection.save(),g.browser.safari&&g.helpers.getSafariVersion()<17){var l=g.selection.ranges();if(o&&o[0].endContainer&&l&&l[0].endContainer&&!o[0].endContainer.isSameNode(l[0].endContainer)&&o[0].endContainer.previousSibling&&o[0].endContainer.previousSibling.isSameNode(l[0].endContainer)){var i=u(l[0].endContainer).find('.fr-marker[data-type="false"]');u(o[0].endContainer).append(i[0])}}for(g.html.wrap(!0,!0,!0,!0),g.selection.restore(),n=g.selection.blocks(!0),a=0;a<n.length;a++)"LI"!=n[a].tagName&&"LI"==n[a].parentNode.tagName&&(n[a]=n[a].parentNode);if(g.selection.save(),function s(e,t){for(var a=!0,n=0;n<e.length;n++){if("LI"!=e[n].tagName)return!1;e[n].parentNode.tagName!=t&&(a=!1)}return a}(n,e)?t||d(n):c(n,e),g.html.unwrap(),g.selection.restore(),t=t||"default"){for(n=g.selection.blocks(),a=0;a<n.length;a++)"LI"!=n[a].tagName&&"LI"==n[a].parentNode.tagName&&(n[a]=n[a].parentNode);for(a=0;a<n.length;a++)"LI"==n[a].tagName&&(u(n[a].parentNode).css("list-style-type","default"===t?"":t),0===(u(n[a].parentNode).attr("style")||"").length&&u(n[a].parentNode).removeAttr("style"))}},refresh:function r(e,t){var a=u(g.selection.element());if(a.get(0)!=g.el){var n=a.get(0);(n="LI"!=n.tagName&&n.firstElementChild&&"LI"!=n.firstElementChild.tagName?a.parents("li").get(0):"LI"==n.tagName||n.firstElementChild?n.firstElementChild&&"LI"==n.firstElementChild.tagName?a.get(0).firstChild:a.get(0):a.parents("li").get(0))&&n.parentNode.tagName==t&&g.el.contains(n.parentNode)&&e.addClass("fr-active")}}}},o.DefineIcon("formatOLSimple",{NAME:"list-ol",SVG_KEY:"orderedList"}),o.RegisterCommand("formatOLSimple",{title:"Ordered List",type:"button",options:{"default":"Default",circle:"Circle",disc:"Disc",square:"Square"},refresh:function(e){this.lists.refresh(e,"OL")},callback:function(e,t){this.lists.format("OL",t)},plugin:"lists"}),o.RegisterCommand("formatUL",{title:"Unordered List",type:"button",hasOptions:function(){return this.opts.listAdvancedTypes},options:{"default":"Default",circle:"Circle",disc:"Disc",square:"Square"},refresh:function(e){this.lists.refresh(e,"UL")},callback:function(e,t){this.lists.format("UL",t)},plugin:"lists"}),o.RegisterCommand("formatOL",{title:"Ordered List",hasOptions:function(){return this.opts.listAdvancedTypes},options:{"default":"Default","lower-alpha":"Lower Alpha","lower-greek":"Lower Greek","lower-roman":"Lower Roman","upper-alpha":"Upper Alpha","upper-roman":"Upper Roman"},refresh:function(e){this.lists.refresh(e,"OL")},callback:function(e,t){this.lists.format("OL",t)},plugin:"lists"}),o.DefineIcon("formatUL",{NAME:"list-ul",SVG_KEY:"unorderedList"}),o.DefineIcon("formatOL",{NAME:"list-ol",SVG_KEY:"orderedList"})});