/*!
 * froala_editor v4.5.0 (https://www.froala.com/wysiwyg-editor)
 * License https://froala.com/wysiwyg-editor/terms/
 * Copyright 2014-2025 Froala Labs
 */

!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(require("froala-editor")):"function"==typeof define&&define.amd?define(["froala-editor"],e):e(t.FroalaEditor)}(this,function(t){"use strict";t=t&&t.hasOwnProperty("default")?t["default"]:t,Object.assign(t.DEFAULTS,{editInPopup:!1}),t.MODULES.editInPopup=function(i){function t(){i.events.$on(i.$el,i._mouseup,function(){setTimeout(function(){!function n(){var t,e=i.popups.get("text.edit");t="INPUT"===i.el.tagName?i.$el.attr("placeholder"):i.$el.text(),e.find("input").val(t).trigger("change"),i.popups.setContainer("text.edit",i.$sc),i.popups.show("text.edit",i.$el.offset().left+i.$el.outerWidth()/2,i.$el.offset().top+i.$el.outerHeight(),i.$el.outerHeight())}()},10)})}return{_init:function n(){i.opts.editInPopup&&(!function e(){var t={edit:'<div id="fr-text-edit-'.concat(i.id,'" class="fr-layer fr-text-edit-layer"><div class="fr-input-line"><input type="text" placeholder="').concat(i.language.translate("Text"),'" tabIndex="1"></div><div class="fr-action-buttons"><button type="button" class="fr-command fr-submit" data-cmd="updateText" tabIndex="2">').concat(i.language.translate("Update"),"</button></div></div>")};i.popups.create("text.edit",t)}(),t())},update:function e(){var t=i.popups.get("text.edit").find("input").val();0===t.length&&(t=i.opts.placeholderText),"INPUT"===i.el.tagName?i.$el.attr("placeholder",t):i.$el.text(t),i.events.trigger("contentChanged"),i.popups.hide("text.edit")}}},t.RegisterCommand("updateText",{focus:!1,undo:!1,callback:function(){this.editInPopup.update()}})});