/*!
 * froala_editor v4.5.0 (https://www.froala.com/wysiwyg-editor)
 * License https://froala.com/wysiwyg-editor/terms/
 * Copyright 2014-2025 Froala Labs
 */

!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(require("froala-editor")):"function"==typeof define&&define.amd?define(["froala-editor"],t):t(e.FroalaEditor)}(this,function(R){"use strict";function c(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,n=Array(t);i<t;i++)n[i]=e[i];return n}function L(e,t){var i="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!i){if(Array.isArray(e)||(i=function r(e,t){if(e){if("string"==typeof e)return c(e,t);var i={}.toString.call(e).slice(8,-1);return"Object"===i&&e.constructor&&(i=e.constructor.name),"Map"===i||"Set"===i?Array.from(e):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?c(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){i&&(e=i);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,a=!0,l=!1;return{s:function(){i=i.call(e)},n:function(){var e=i.next();return a=e.done,e},e:function(e){l=!0,s=e},f:function(){try{a||null==i["return"]||i["return"]()}finally{if(l)throw s}}}}var U,D;R=R&&R.hasOwnProperty("default")?R["default"]:R,Object.assign(R.POPUP_TEMPLATES,{"filestackPlugin.error":"[_UPLOAD_LAYER_]"}),R.FILESTACK_PICKER_OPTIONS_MEDIA_MAX_SUPPORTED=[".pdf",".docx",".ppt",".pptx",".doc",".xlsx",".xls",".csv",".txt","image/jpeg","image/png","image/jpg","image/gif","image/webp","video/*","audio/*","mp3"],R.REQUIRED_PLUGINS=["image","video","file","filesManager"],R.FILESTACK_PICKER_OPTIONS_IMAGE_ONLY_SUPPORTED=["image/jpeg","image/png","image/jpg","image/gif","image/webp"],R.FILESTACK_DEFAULT_ACCEPT_FILE_TYPES=[".pdf",".docx",".ppt",".pptx",".doc",".xlsx",".xls",".csv",".txt"],R.FILESTACK_PICKER_OPTIONS_VIDEO_ONLY_SUPPORTED=["video/*"],R.FILESTACK_PICKER_OPTIONS_FROMSOURCES_MAX_SUPPORTED=["local_file_system","imagesearch","url","googledrive","facebook","instagram","dropbox"],R.FILESTACK_PICKER_DEFAULT_OPTIONS={displayMode:"overlay",uploadInBackground:!1,stage:{cname:"stage.filestackapi.com"}},Object.assign(R.DEFAULTS,{filestackOptions:{}}),R.FILE_STACK_URL_PART="filestack",R.PLUGINS.filestack=function(u){var o,f,t=["pdf","msword","vnd.openxmlformats-officedocument.wordprocessingml.document","vnd.ms-excel","vnd.openxmlformats-officedocument.spreadsheetml.sheet","vnd.ms-powerpoint","vnd.openxmlformats-officedocument.presentationml.presentation","csv","plain","ogg"],d=1,a=4,m=5,g=6,l=7,r={};function k(e,t,i){v(i?u.language.translate(r[e])+"\nResponse: "+JSON.stringify(i):u.language.translate(r[e]),t)}function v(e,t,i,n){var o;if((o=u.popups.get("filestackPlugin.error"))||(o=p(e)),i&&(o.find(".fr-image-progress-bar-layer").addClass("fr-active"),o.find(".fr-error-message").addClass("fr-layer"),o.find(".fr-buttons").hide()),!!u.$tb){var s;"openFilePicker"===t?s="openFilePicker":"openFilePickerReplaceImageOnly"===t||"openFilePickerImage"===t?s="insertImage":"openFilePickerReplaceVideoOnly"===t||"openFilePickerVideo"===t?s="insertVideo":"openFilePickerVideoOnly"===t?s="openFilePickerVideoOnly":"openFilePickerImageOnly"===t?s="openFilePickerImageOnly":"openFilePickerFile"===t&&(s="openFilePickerFile");var a=u.$tb.find(".fr-command[data-cmd='".concat(s,"']"));if(i)u.popups.show("filestackPlugin.error",n.x,n.y);else if(!o.hasClass("fr-active")&&(u.popups.refresh("filestackPlugin.error"),u.popups.setContainer("filestackPlugin.error",u.$tb),a.isVisible()||u.opts.toolbarInline)){var l=u.opts.toolbarInline&&["openFilePickerImage","openFilePickerVideo"].includes(t)?u.$(".fr-popup")[0].style:u.button.getPosition(a),r=l.left,c=l.top;u.popups.show("filestackPlugin.error",r,c,a.outerHeight())}}}function P(){var e="",t=function i(){var t=[];return R.REQUIRED_PLUGINS.forEach(function(e){u.opts.pluginsEnabled.indexOf(e)<0&&t.push(e.charAt(0).toUpperCase()+e.slice(1))}),t}();return e=t.join(", "),1<t.length?e+=" plugin are":e+=" plugin is",e}function p(e){var t=e.includes("Do you want to enable"),i=t?"<div style='text-align:right;' class='fr-buttons'>\n          <button class='fr-trim-button fr-plugins-enable'>".concat(u.language.translate("Enable"),"</button>               \n          <button class='fr-trim-button fr-plugins-cancel'>").concat(u.language.translate("Cancel"),"</button>\n        </div>"):"<div style='text-align:right;' class='fr-buttons'>\n          <button class='fr-trim-button fr-plugins-ok'>".concat(u.language.translate("OK"),"</button>               \n        </div>"),n={upload_layer:"".concat('<div class="fr-image-progress-bar-layer fr-layer"><h3 tabIndex="-1" class="fr-message">Uploading</h3><div class="fr-loader fr-indeterminate"><span class="fr-progress"></span></div><div class="fr-action-buttons"><button type="button" class="fr-command fr-dismiss" data-cmd="imageDismissError" tabIndex="2" role="button">OK</button></div></div>',"\n      <div style= 'padding:10px'>\n      <div class = 'fr-message fr-error-message'><h3 style ='font-size: 16px; margin: 10px 10px;font-weight: normal;'>").concat(u.language.translate(e),"</h3></div>\n      ").concat(i,"\n    ")},o=u.popups.create("filestackPlugin.error",n);if(t){var s=o.find(".fr-plugins-enable")[0],a=o.find(".fr-plugins-cancel")[0];s.addEventListener("click",function(e){!function t(){R.REQUIRED_PLUGINS.forEach(function(e){u.opts.pluginsEnabled.indexOf(e)<0&&u.opts.pluginsEnabled.push(e)})}(),function i(e){for(var t in e)if(!u[t]){if(R.PLUGINS[t]&&u.opts.pluginsEnabled.indexOf(t)<0)continue;u[t]=new e[t](u),u[t]._init&&u[t]._init()}}(R.PLUGINS),_(),u.events.trigger("filestack.pluginsEnabled"),u.popups.hide("filestackPlugin.error"),O(),e.preventDefault()}),a.addEventListener("click",function(e){u.popups.hide("filestackPlugin.error"),O(),e.preventDefault()})}else{var l=o.find(".fr-plugins-ok")[0];l&&l.addEventListener("click",function(e){u.popups.hide("filestackPlugin.error"),e.preventDefault()})}return o}function F(){var t=!0;return R.REQUIRED_PLUGINS.forEach(function(e){u.opts.pluginsEnabled.indexOf(e)<0&&(t=!1)}),t}r[d]="Filestack api is missing",r[2]="The Filestack options are missing. Please check filestackOptions",r[3]="The Filestack picker is missing. Please check filestackOption",r[a]="Image upload to filestack failed while processing the request",r[m]="Filestack plugin is currently not supported on this browser",r[g]="The selected media types are not supported",r[l]="The selected source types are not supported by Filestack",r[8]="Error opening Filestack picker due to invalid picker parameters",r[9]="This image is not supported for image transformation",r[10]="The Filestack client is not created yet";var h=u.opts.filestackOptions.filestackAPI;if(h)try{f=u.opts.filestackOptions.staging?filestack.init(h,R.FILESTACK_PICKER_DEFAULT_OPTIONS.stage):filestack.init(h,R.FILESTACK_PICKER_DEFAULT_OPTIONS)}catch(A){0===!Object.keys(u.opts.filestackOptions).length&&k(10)}else;function I(){var e=navigator.userAgent,t=u||"",i=t.helpers,n=t.browser;if(i.isMac()&&n.mozilla)return!1;var o=-1!=e.indexOf("Windows NT 10.")||-1!=e.indexOf("Windows NT 7."),s=e.match(/(chrome|safari|firefox|trident(?=\/))\/?\s*(\d+)/i)[2]||[],a=null;return 0<=e.indexOf("Android")&&(a=parseFloat(e.slice(e.indexOf("Android")+8))),!!((i.isMac()||i.isIOS())&&12<i.getSafariVersion())||(!!(o&&n.chrome&&70<s||-1!=e.indexOf("Edg")&&40<s)||!!(i.isAndroid()&&6<a&&n.chrome&&70<s))}function c(e){return!1===u.events.trigger("filestack.uploadedToFilestack",[e],!0)?(u.edit.on(),!1):e}function O(e){var t=u.button.getButtons('button[data-cmd="openFilePicker"]'),i=u.button.getButtons('button[data-cmd="openFilePickerImageOnly"]'),n=u.button.getButtons('button[data-cmd="openFilePickerVideoOnly"]'),o=u.button.getButtons('button[data-cmd="openFilePickerFile"]');t.removeClass("fr-filestack-active"),i.removeClass("fr-filestack-active"),n.removeClass("fr-filestack-active"),o.removeClass("fr-filestack-active"),!1===u.events.trigger("filestack.filestackPickerClosed",[e],!0)&&u.edit.on()}function E(){D=U=null,u.selection.restore()}function y(e){k(a,e),!1===u.events.trigger("filestack.uploadFailedToFilestack",[e],!0)&&u.edit.on()}function b(e){return t.includes(e.split("/")[1])}function T(e){var t=!1;u.opts.imageInsertButtons=u.opts.imageInsertButtons.map(function(e){return"openFilePickerImage"===e?(t=!0,"openFilePickerReplaceImageOnly"):e}),t&&u.events.trigger("filestack-init"),u.popups.hide("image.edit");var i=u.doc.querySelector(".fr-popup");i&&i.remove(),u.opts.pluginsEnabled.includes("filestack")&&function n(e){u.opts.imageEditButtons.includes("filestackIcon")&&(o=u.opts.imageEditButtons.indexOf("filestackIcon"));!u.opts.imageEditButtons.includes("filestackIcon")&&e&&u.opts.pluginsEnabled.includes("filestack")?(o||0===o)&&u.opts.imageEditButtons.splice(o,0,"filestackIcon"):e||(u.opts.imageEditButtons=u.opts.imageEditButtons.filter(function(e){return"filestackIcon"!=e}));u.opts.filestackOptions.uploadToFilestackOnly&&e?u.opts.imageEditButtons=u.opts.imageEditButtons.filter(function(e){return"imageTUI"!=e}):u.opts.pluginsEnabled.includes("imageTUI")&&!u.opts.imageEditButtons.includes("imageTUI")&&u.opts.imageEditButtons.push("imageTUI")}(e),u.events.trigger("filestack-init-edit")}function S(){var t=!1;u.opts.videoInsertButtons=u.opts.videoInsertButtons.map(function(e){return"openFilePickerVideo"===e?(t=!0,"openFilePickerReplaceVideoOnly"):e}),t&&u.events.trigger("filestack-init-video")}function _(){R.DEFAULTS.imageInsertButtons&&!R.DEFAULTS.imageInsertButtons.includes("openFilePickerImage")&&R.DEFAULTS.imageInsertButtons.push("openFilePickerImage"),R.DEFAULTS.imageEditButtons&&!R.DEFAULTS.imageEditButtons.includes("filestackIcon")&&R.DEFAULTS.imageEditButtons.push("filestackIcon"),R.DEFAULTS.videoInsertButtons&&!R.DEFAULTS.videoInsertButtons.includes("openFilePickerVideo")&&R.DEFAULTS.videoInsertButtons.push("openFilePickerVideo"),R.DEFAULTS.filesInsertButtons&&!R.DEFAULTS.filesInsertButtons.includes("openFilePicker")&&R.DEFAULTS.filesInsertButtons.push("openFilePicker"),R.DEFAULTS.fileInsertButtons&&!R.DEFAULTS.fileInsertButtons.includes("openFilePickerFile")&&R.DEFAULTS.fileInsertButtons.push("openFilePickerFile"),u.popups.onHide("image.insert",function(){var e=u.button.getButtons('button[data-cmd="openFilePickerImage"]'),t=u.button.getButtons('button[data-cmd="openFilePickerReplaceImageOnly"]');e.removeClass("fr-filestack-active"),t.removeClass("fr-filestack-active")}),u.popups.onHide("video.insert",function(){var e=u.button.getButtons('button[data-cmd="openFilePickerVideo"]'),t=u.button.getButtons('button[data-cmd="openFilePickerReplaceVideoOnly"]');e.removeClass("fr-filestack-active"),t.removeClass("fr-filestack-active")}),u.popups.onHide("filesManager.insert",function(){u.button.getButtons('button[data-cmd="openFilePicker"]').removeClass("fr-filestack-active")});var e=u.opts.filestackOptions.pickerOptions?Object.assign({},u.opts.filestackOptions.pickerOptions,{accept:u.opts.filestackOptions.pickerOptions.accept?u.opts.filestackOptions.pickerOptions.accept:[],fromSources:u.opts.filestackOptions.pickerOptions.fromSources?u.opts.filestackOptions.pickerOptions.fromSources:[]}):{accept:[],fromSources:[]};e.fromSources=function a(t,e){if(!e||0===e.length)return t;var i=[];return e.forEach(function(e){-1<t.indexOf(e)&&i.push(e)}),0===i.length&&k(l),i}(R.FILESTACK_PICKER_OPTIONS_FROMSOURCES_MAX_SUPPORTED,e.fromSources),e.onOpen=function(e){u.events.on("window.mouseup",function(e){if(document.getElementById("__filestack-picker")&&u.helpers.isMobile())return e.preventDefault(),e.stopPropagation(),!1},!0),!1===u.events.trigger("filestack.filestackPickerOpened",[e],!0)&&u.edit.on();var t=u.selection.get();t&&(t.anchorNode,t.anchorOffset)},e.onUploadDone=function(e){return function a(e){if("undefined"!=typeof e.filesUploaded){var n,t=L(e.filesUploaded);try{var i,o=function o(){var t=n.value;if(b(t.mimetype)||t.mimetype.includes("image/jpeg")||t.mimetype.includes("image/png")||t.mimetype.includes("image/")||t.mimetype.includes("video/")||t.mimetype.includes("audio/mpeg")){t.mimetype.includes("image/")&&T(!0),t.mimetype.includes("video/")&&S();var i=function i(e){t.mimetype.includes("image")?e.next().is("br")&&e.next().remove():(e.next()&&e.next().is("br")&&e.next().remove(),e.prev()&&e.prev().is("br")&&e.prev().remove()),u.selection.setAfter(e[0])};if(!c(e)||U||D||(t.mimetype.includes("ogg")?u.file.insert(t.url,t.filename,null):t.mimetype.includes("image")?(u.image.insert(t.url,!1,null,null),u.events.on("image.loaded",i)):t.mimetype.includes("video")?(u.events.on("video.inserted",i),u.video.insertHtmlVideo(t.url,null,null,null,null)):t.mimetype.includes("audio/mpeg")?(u.events.on("video.inserted",i),u.video.insertHtmlVideo(t.url,null,null,null,null,"audio")):b(t.mimetype)&&u.file.insert(t.url,t.filename,null)),U)return u.image.insert(t.url,!1,null,U),U=null,{v:void 0};if(D)return u.video.insertHtmlVideo(t.url,!1,null,D),D=null,{v:void 0}}};for(t.s();!(n=t.n()).done;)if(i=o())return i.v}catch(s){t.e(s)}finally{t.f()}}}(e)},e.onFileUploadFailed=y,e.onClose=O,e.onCancel=E,u.opts.filestackOptions.pickerOptions=e;var o=function o(e){var t="IMG"===e.target.tagName,i="VIDEO"===e.target.tagName,n=e.target.src&&e.target.src.includes(R.FILE_STACK_URL_PART)&&!e.target.src.includes("blob:");t?T(n):i&&S()},t=u.helpers.isMobile()&&!u.helpers.isWindowsPhone();if(u.events.on(t?"touchend":"click",o,t),u.events.on("drop",function(){T(!1),S()},!0),u.opts.filestackOptions.uploadToFilestackOnly&&!u.opts.iframe&&!u.opts.fullPage){if(f&&I()&&F()){var i=filestackDnD.init(f,u.el);i&&i.on("uploadFileFinish",function(e){if(e.files[0].mimetype.includes("image/")&&T(!0),e.files[0].mimetype.includes("video/")&&S(),b(e.files[0].mimetype)||e.files[0].mimetype.includes("pdf")||e.files[0].mimetype.includes("image/jpeg")||e.files[0].mimetype.includes("image/png")||e.files[0].mimetype.includes("image/")||e.files[0].mimetype.includes("video/")||e.files[0].name&&e.files[0].name.includes(".ogg")){var t=function t(e){u.popups.hide("filestackPlugin.error"),u.selection.setAfter(e[0])};if(e.files[0].mimetype.includes("image"))c(e)&&(u.image.insert(e.data.url,!1),u.events.on("image.inserted",t));else e.files[0].mimetype.includes("video")?(u.video.insertHtmlVideo(e.data.url),u.events.on("video.inserted",t)):(e.files[0].mimetype.includes("pdf")||b(e.files[0].mimetype)||e.files[0].name&&e.files[0].name.includes(".ogg"))&&u.file.insert(e.data.url,e.data._file.name,null);u.popups.hide("filestackPlugin.error")}})}u.events.on("drop",function(e){return f?I()?F()?(u.markers.remove(),u.markers.insertAtPoint(e.originalEvent),u.$el.find(".fr-marker").replaceWith(R.MARKERS),0===u.$el.find(".fr-marker").length&&u.selection.setAtEnd(u.el),v("Image loading","",!0,{x:e.originalEvent.x,y:e.originalEvent.y})):v(P()+" not enabled. Do you want to enable?","openFilePicker"):(p(r[m]),u.popups.show("filestackPlugin.error",e.originalEvent.x,e.originalEvent.y)):(p(r[d]),u.popups.show("filestackPlugin.error",e.originalEvent.x,e.originalEvent.y)),e.preventDefault(),e.stopPropagation(),!1},!0)}var n=document.getElementById("insertImage-".concat(u.id));n&&n.addEventListener("mouseup",function(){var t=!1;u.opts.imageInsertButtons=u.opts.imageInsertButtons.map(function(e){return"openFilePickerReplaceImageOnly"===e?(t=!0,"openFilePickerImage"):e}),t&&u.events.trigger("filestack-init")});var s=document.getElementById("insertVideo-".concat(u.id));s&&s.addEventListener("mouseup",function(){var t=!1;u.opts.videoInsertButtons=u.opts.videoInsertButtons.map(function(e){return"openFilePickerReplaceVideoOnly"===e?(t=!0,"openFilePickerVideo"):e}),t&&u.events.trigger("filestack-init-video")}),u.events.on("image.loaded",function(e){(!e[0].src.includes(R.FILE_STACK_URL_PART)||e[0].src.includes(R.FILE_STACK_URL_PART)&&e[0].src.includes("blob:"))&&setTimeout(function(){var e=document.querySelector("#filestackIcon-".concat(u.id));e&&e.parentNode.removeChild(e)},10)})}return u.events.on("filestack.imageTransformation.click",function(){!function s(e){U=e;var i=u.image.get()[0].currentSrc||"",t=filestack.init(h),n=new FilestackTransform(t);n.setConfigKey("output.blob",!0),n.open(i).then(function(e){t.upload(e).then(function(e){var t=u.selection.save();void 0!==i&&(u.image.insert(e.url,!1,null,U),U=null,u.selection.restore(t))}),t.on("upload.error",function(){k(a)})}),u.events.on("keydown",function(){var e=document.getElementsByClassName("fs-transforms-container");u.helpers.isMobile()&&e&&e[0].childNodes&&e[0].childNodes.length&&(U=null)});var o=u.button.getButtons('button[data-cmd="filestackIcon"]');n.on("canvas:save",function(){o.removeClass("fr-filestack-active"),v("Image loading","",!0,{x:U.offset().left,y:U.offset().top+U.height()})}),n.on("canvas:cancel",function(){o.removeClass("fr-filestack-active")})}(u.image.get())}),u.events.on("filestack.imageTransformation.error",function(){k(9)}),{_init:_,showFilestackPopup:function C(e,t,i){if(!e)return!1;var n;if("openFilePicker"===e||"openFilePickerFile"===e?n=R.FILESTACK_PICKER_OPTIONS_MEDIA_MAX_SUPPORTED:"openFilePickerImageOnly"===e||"openFilePickerReplaceImageOnly"===e||"openFilePickerImage"===e?n=R.FILESTACK_PICKER_OPTIONS_IMAGE_ONLY_SUPPORTED:"openFilePickerVideoOnly"!==e&&"openFilePickerReplaceVideoOnly"!==e&&"openFilePickerVideo"!==e||(n=R.FILESTACK_PICKER_OPTIONS_VIDEO_ONLY_SUPPORTED),!h)return k(d,e),!1;if(!I())return k(m,e),!1;if(!F())return v(P()+" not enabled. Do you want to enable?",e),!1;if(!f&&0===!Object.keys(u.opts.filestackOptions).length)return k(10,e),!1;t&&(U=t),i&&(D=i);var o,s=u.opts.filestackOptions.pickerOptions?Object.assign({},u.opts.filestackOptions.pickerOptions,{accept:u.opts.filestackOptions.pickerOptions.accept?u.opts.filestackOptions.pickerOptions.accept:[],fromSources:u.opts.filestackOptions.pickerOptions.fromSources?u.opts.filestackOptions.pickerOptions.fromSources:[]}):{accept:[],fromSources:[]};o=u.opts.fileAllowedTypes&&1===u.opts.fileAllowedTypes.length&&"*"===u.opts.fileAllowedTypes[0]?R.FILESTACK_PICKER_OPTIONS_MEDIA_MAX_SUPPORTED:u.opts.fileAllowedTypes,0===s.accept.length&&(s.accept=R.FILESTACK_PICKER_OPTIONS_MEDIA_MAX_SUPPORTED);var a=R.FILESTACK_PICKER_OPTIONS_VIDEO_ONLY_SUPPORTED;u.opts.videoAllowedTypes&&(a=u.opts.videoAllowedTypes.map(function(e){return"mp3"===e?"audio/".concat(e):"video/".concat(e)})),"openFilePickerImageOnly"===e||"openFilePickerReplaceImageOnly"===e||"openFilePickerImage"===e?n=u.opts.imageAllowedTypes.map(function(e){return"image/".concat(e)}):"openFilePickerVideoOnly"===e||"openFilePickerReplaceVideoOnly"===e||"openFilePickerVideo"===e?n=a:"openFilePickerFile"===e&&(n=o);var l=s.maxFiles;(t||i)&&(l=1);var r="openFilePicker"===e?function p(t,e){if(!e||0===e.length)return t;var i=[];return e.forEach(function(e){-1<t.indexOf(e)&&i.push(e)}),0===i.length&&k(g),i}(n,s.accept):n,c=Object.assign({},s,{accept:r,maxFiles:l});try{f.picker(c).open(),u.popups.hideAll(),u.selection.save()}catch(A){0===Object.keys(u.opts.filestackOptions).length&&k(8,e)}},isBrowserSupported:I}},R.DefineIcon("filestackIcon",{ALT:"Open Filestack file picker",NAME:"filestack",SVG_KEY:"filestackIcon"}),R.DefineIcon("filestackIconAdd",{ALT:"Open Filestack file picker",NAME:"filestackAdd",SVG_KEY:"filestackIconAdd"}),R.DefineIcon("imageTransformations",{ALT:"Open Filestack image transformations",NAME:"imageTransformations",SVG_KEY:"imageTransformations"}),R.DefineIcon("imageFilestackOnly",{template:"svgMultiplePath",PATHS:'<path d="M11.1 15.8h7.7V5.7H5.1v4.2H3.4V5.7c0-.9.8-1.7 1.7-1.7h13.7c.9 0 1.7.8 1.7 1.7v10.1c0 .9-.8 1.7-1.7 1.7h-7.7v-1.7Zm0-2.5 2.6-3.4h.2l3.3 4.2h-6.1v-.8ZM8.9 6.9c.7 0 1.3.6 1.3 1.3s-.6 1.3-1.3 1.3-1.3-.6-1.3-1.3.6-1.3 1.3-1.3Z"/><path d="M11.1 17.5 8.7 20v-2.5h2.4Zm-4.9-1.6v.9h-.9v-.9h.9Zm4.9.9h-.9v-6H3.5V19h4.6v.9H2.5V9.8h8.6v7Zm-3.4-1.6H5.3v-.9h2.4v.9Zm.6-2.5v.9h-3v-.9h3Z"/>'}),R.DefineIcon("videoFilestackOnly",{template:"svgMultiplePath",PATHS:'<path d="m11 11.3 1 2.6h3.6V6.8h-9v3.6H4.8V6c0-.2.1-.5.3-.6s.4-.3.6-.3h10.8c.2 0 .5.1.6.3s.3.4.3.6v3.1l2.1-2c.3-.3.6-.3 1-.2.3 0 .6.5.6.8v5.5c0 .4-.2.7-.6.8-.3.1-.7 0-1-.2l-2.1-2v3.1c0 .2 0 .5-.3.6-.2.2-.4.3-.6.3H12V14l-1-2.7Z" /><path d="M11.1 17.5 8.7 20v-2.5h2.4Zm-4.9-1.6v.9h-.9v-.9h.9Zm4.9.9h-.9v-6H3.5V19h4.6v.9H2.5V9.8h8.6v7Zm-3.4-1.6H5.3v-.9h2.4v.9Zm.6-2.5v.9h-3v-.9h3Z"/>'}),R.RegisterCommand("openFilePicker",{type:"button",icon:"filestackIcon",title:"Open Filestack File Picker",callback:function(){this.button.getButtons('button[data-cmd="openFilePicker"]').addClass("fr-filestack-active"),this.filestack.showFilestackPopup("openFilePicker")},plugin:"filestack"}),R.RegisterCommand("openFilePickerImageOnly",{type:"button",icon:"imageFilestackOnly",title:"Open Filestack Image Picker",callback:function(){this.button.getButtons('button[data-cmd="openFilePickerImageOnly"]').addClass("fr-filestack-active"),this.filestack.showFilestackPopup("openFilePickerImageOnly",this.image&&this.image.get())},plugin:"filestack"}),R.RegisterCommand("openFilePickerImage",{type:"button",icon:"filestackIcon",title:"Open Filestack Image Picker",callback:function(){this.button.getButtons('button[data-cmd="openFilePickerImage"]').addClass("fr-filestack-active"),this.filestack.showFilestackPopup("openFilePickerImage",this.image&&this.image.get())},plugin:"filestack"}),R.RegisterCommand("openFilePickerReplaceImageOnly",{type:"button",icon:"filestackIconAdd",title:"Open Filestack File Picker",callback:function(){this.button.getButtons('button[data-cmd="openFilePickerReplaceImageOnly"]').addClass("fr-filestack-active"),this.filestack.showFilestackPopup("openFilePickerReplaceImageOnly",this.image&&this.image.get())},plugin:"filestack"}),R.RegisterCommand("openFilePickerVideoOnly",{type:"button",icon:"videoFilestackOnly",title:"Open Filestack Video Picker",callback:function(){this.button.getButtons('button[data-cmd="openFilePickerVideoOnly"]').addClass("fr-filestack-active"),this.filestack.showFilestackPopup("openFilePickerVideoOnly",null,this.video&&this.video.get())},plugin:"filestack"}),R.RegisterCommand("openFilePickerVideo",{type:"button",icon:"filestackIcon",title:"Open Filestack Video Picker",callback:function(){this.button.getButtons('button[data-cmd="openFilePickerVideo"]').addClass("fr-filestack-active"),this.filestack.showFilestackPopup("openFilePickerVideo",null,this.video&&this.video.get())},plugin:"filestack"}),R.RegisterCommand("openFilePickerReplaceVideoOnly",{type:"button",icon:"filestackIconAdd",title:"Open Filestack File Picker",callback:function(){this.button.getButtons('button[data-cmd="openFilePickerReplaceVideoOnly"]').addClass("fr-filestack-active"),this.filestack.showFilestackPopup("openFilePickerReplaceVideoOnly",null,this.video&&this.video.get())},plugin:"filestack"}),R.RegisterCommand("filestackIcon",{type:"button",icon:"imageTransformations",title:"Image Transformations",plugin:"filestack",callback:function(){var e=!1,t=this.image.get(0)[0];(t&&t.currentSrc&&(e=t.currentSrc.includes(R.FILE_STACK_URL_PART)&&!t.currentSrc.includes("blob:")),e)?(this.button.getButtons('button[data-cmd="filestackIcon"]').addClass("fr-filestack-active"),this.events.trigger("filestack.imageTransformation.click")):this.events.trigger("filestack.imageTransformation.error")}}),R.RegisterCommand("openFilePickerFile",{type:"button",icon:"filestackIcon",title:"Open Filestack Upload File",callback:function(){this.button.getButtons('button[data-cmd="openFilePickerFile"]').addClass("fr-filestack-active"),this.filestack.showFilestackPopup("openFilePickerFile")},plugin:"filestack"})});