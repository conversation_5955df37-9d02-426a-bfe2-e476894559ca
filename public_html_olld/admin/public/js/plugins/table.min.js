/*!
 * froala_editor v4.5.0 (https://www.froala.com/wysiwyg-editor)
 * License https://froala.com/wysiwyg-editor/terms/
 * Copyright 2014-2025 Froala Labs
 */

!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(require("froala-editor")):"function"==typeof define&&define.amd?define(["froala-editor"],t):t(e.FroalaEditor)}(this,function(Ae){"use strict";function r(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,l=Array(t);a<t;a++)l[a]=e[a];return l}function $e(e){return function t(e){if(Array.isArray(e))return r(e)}(e)||function a(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function l(e,t){if(e){if("string"==typeof e)return r(e,t);var a={}.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?r(e,t):void 0}}(e)||function n(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}Ae=Ae&&Ae.hasOwnProperty("default")?Ae["default"]:Ae,Object.assign(Ae.POPUP_TEMPLATES,{"table.insert":"[_BUTTONS_][_ROWS_COLUMNS_]","table.edit":"[_BUTTONS_]","table.colors":"[_BUTTONS_][_COLORS_][_CUSTOM_COLOR_]"}),Object.assign(Ae.DEFAULTS,{tableInsertMaxSize:10,tableEditButtons:["tableHeader","tableFooter","tableRemove","tableRows","tableColumns","tableStyle","-","tableCells","tableCellBackground","tableCellVerticalAlign","tableCellHorizontalAlign","tableCellStyle"],tableInsertButtons:["tableBack","|"],tableResizer:!0,tableDefaultWidth:"100%",tableResizerOffset:5,tableResizingLimit:30,tableColorsButtons:["tableBack","|"],tableColors:["#61BD6D","#1ABC9C","#54ACD2","#2C82C9","#9365B8","#475577","#CCCCCC","#41A85F","#00A885","#3D8EB9","#2969B0","#553982","#28324E","#000000","#F7DA64","#FBA026","#EB6B56","#E25041","#A38F84","#EFEFEF","#FFFFFF","#FAC51C","#F37934","#D14841","#B8312F","#7C706B","#D1D5D8","REMOVE"],tableColorsStep:7,tableCellStyles:{"fr-highlighted":"Highlighted","fr-thick":"Thick"},tableStyles:{"fr-dashed-borders":"Dashed Borders","fr-alternate-rows":"Alternate Rows"},tableCellMultipleStyles:!0,tableMultipleStyles:!0,tableInsertHelper:!0,keepTextFormatOnTable:!1,tableInsertHelperOffset:15}),Ae.PLUGINS.table=function(R){var O,f,n,r,l,s,y,x=R.$,i={};function u(){var e=$();if(e){var t=R.popups.get("table.edit");if(t||(t=h()),t){R.popups.setContainer("table.edit",R.$sc);var a=k(e),l=a.left+(a.right-a.left)/2,n=a.bottom;R.popups.show("table.edit",l,n,a.bottom-a.top,!0),R.edit.isDisabled()&&(R.$el.removeClass("fr-no-selection"),R.edit.on(),R.button.bulkRefresh(),R.selection.setAtEnd(R.$el.find(".fr-selected-cell").last().get(0)),R.selection.restore())}}}function c(){var e=$();if(e){var t=R.popups.get("table.colors");t||(t=function o(){var e="";0<R.opts.tableColorsButtons.length&&(e='<div class="fr-buttons fr-tabs">'.concat(R.button.buildList(R.opts.tableColorsButtons),"</div>"));var t="";R.opts.colorsHEXInput&&(t='<div class="fr-color-hex-layer fr-table-colors-hex-layer fr-active fr-layer" id="fr-table-colors-hex-layer-'.concat(R.id,'"><div class="fr-input-line"><input maxlength="7" id="fr-table-colors-hex-layer-text-').concat(R.id,'" type="text" placeholder="').concat(R.language.translate("HEX Color"),'" tabIndex="1" aria-required="true"></div><div class="fr-action-buttons"><button type="button" class="fr-command fr-submit" data-cmd="tableCellBackgroundCustomColor" tabIndex="2" role="button">').concat(R.language.translate("OK"),"</button></div></div>"));var a={buttons:e,colors:function n(){for(var e='<div class="fr-color-set fr-table-colors">',t=0;t<R.opts.tableColors.length;t++)0!==t&&t%R.opts.tableColorsStep==0&&(e+="<br>"),"REMOVE"!=R.opts.tableColors[t]?e+='<span class="fr-command" style="background: '.concat(R.opts.tableColors[t],';" tabIndex="-1" role="button" data-cmd="tableCellBackgroundColor" data-param1="').concat(R.opts.tableColors[t],'"><span class="fr-sr-only">').concat(R.language.translate("Color")," ").concat(R.opts.tableColors[t],"&nbsp;&nbsp;&nbsp;</span></span>"):e+='<span class="fr-command" data-cmd="tableCellBackgroundColor" tabIndex="-1" role="button" data-param1="REMOVE" title="'.concat(R.language.translate("Clear Formatting"),'">').concat(R.icon.create("tableColorRemove"),'<span class="fr-sr-only">').concat(R.language.translate("Clear Formatting"),"</span></span>");return e+="</div>"}(),custom_color:t},l=R.popups.create("table.colors",a);return R.events.$on(R.$wp,"scroll.table-colors",function(){R.popups.isVisible("table.colors")&&c()}),function r(g){R.events.on("popup.tab",function(e){var t=x(e.currentTarget);if(!R.popups.isVisible("table.colors")||!t.is("span"))return!0;var a=e.which,l=!0;if(Ae.KEYCODE.TAB==a){var n=g.find(".fr-buttons");l=!R.accessibility.focusToolbar(n,!!e.shiftKey)}else if(Ae.KEYCODE.ARROW_UP==a||Ae.KEYCODE.ARROW_DOWN==a||Ae.KEYCODE.ARROW_LEFT==a||Ae.KEYCODE.ARROW_RIGHT==a){var r=t.parent().find("span.fr-command"),o=r.index(t),s=R.opts.colorsStep,i=Math.floor(r.length/s),f=o%s,c=Math.floor(o/s),d=c*s+f,p=i*s;Ae.KEYCODE.ARROW_UP==a?d=((d-s)%p+p)%p:Ae.KEYCODE.ARROW_DOWN==a?d=(d+s)%p:Ae.KEYCODE.ARROW_LEFT==a?d=((d-1)%p+p)%p:Ae.KEYCODE.ARROW_RIGHT==a&&(d=(d+1)%p);var h=x(r.get(d));R.events.disableBlur(),h.focus(),l=!1}else Ae.KEYCODE.ENTER==a&&(R.button.exec(t),l=!1);return!1===l&&(e.preventDefault(),e.stopPropagation()),l},!0)}(l),l}()),R.popups.setContainer("table.colors",R.$sc);var a=k(e),l=(a.left+a.right)/2,n=a.bottom;!function r(){var e=R.popups.get("table.colors"),t=R.$el.find(".fr-selected-cell").first(),a=R.helpers.RGBToHex(t.css("background-color")),l=e.find(".fr-table-colors-hex-layer input");e.find(".fr-selected-color").removeClass("fr-selected-color fr-active-item"),e.find('span[data-param1="'.concat(a,'"]')).addClass("fr-selected-color fr-active-item"),l.val(a).trigger("change")}(),R.popups.show("table.colors",l,n,a.bottom-a.top,!0)}}function o(){0===oe().length&&R.toolbar.enable()}function d(e){if(e)return R.popups.onHide("table.insert",function(){R.popups.get("table.insert").find('.fr-table-size .fr-select-table-size > span[data-row="1"][data-col="1"]').trigger("mouseover")}),!0;var t="";0<R.opts.tableInsertButtons.length&&(t='<div class="fr-buttons fr-tabs">'.concat(R.button.buildList(R.opts.tableInsertButtons),"</div>"));var a={buttons:t,rows_columns:function r(){for(var e='<div class="fr-table-size"><div class="fr-table-size-info">1 &times; 1</div><div class="fr-select-table-size">',t=1;t<=R.opts.tableInsertMaxSize;t++){for(var a=1;a<=R.opts.tableInsertMaxSize;a++){var l="inline-block";2<t&&!R.helpers.isMobile()&&(l="none");var n="fr-table-cell ";1==t&&1==a&&(n+=" hover"),e+='<span class="fr-command '.concat(n,'" tabIndex="-1" data-cmd="tableInsert" data-row="').concat(t,'" data-col="').concat(a,'" data-param1="').concat(t,'" data-param2="').concat(a,'" style="display: ').concat(l,';" role="button"><span></span><span class="fr-sr-only">').concat(t," &times; ").concat(a,"&nbsp;&nbsp;&nbsp;</span></span>")}e+='<div class="new-line"></div>'}return e+="</div></div>"}()},l=R.popups.create("table.insert",a);return R.events.$on(l,"mouseover",".fr-table-size .fr-select-table-size .fr-table-cell",function(e){p(x(e.currentTarget))},!0),function n(e){R.events.$on(e,"focus","[tabIndex]",function(e){var t=x(e.currentTarget);p(t)}),R.events.on("popup.tab",function(e){var t=x(e.currentTarget);if(!R.popups.isVisible("table.insert")||!t.is("span, a"))return!0;var a,l=e.which;if(Ae.KEYCODE.ARROW_UP==l||Ae.KEYCODE.ARROW_DOWN==l||Ae.KEYCODE.ARROW_LEFT==l||Ae.KEYCODE.ARROW_RIGHT==l){if(t.is("span.fr-table-cell")){var n=t.parent().find("span.fr-table-cell"),r=n.index(t),o=R.opts.tableInsertMaxSize,s=r%o,i=Math.floor(r/o);Ae.KEYCODE.ARROW_UP==l?i=Math.max(0,i-1):Ae.KEYCODE.ARROW_DOWN==l?i=Math.min(R.opts.tableInsertMaxSize-1,i+1):Ae.KEYCODE.ARROW_LEFT==l?s=Math.max(0,s-1):Ae.KEYCODE.ARROW_RIGHT==l&&(s=Math.min(R.opts.tableInsertMaxSize-1,s+1));var f=i*o+s,c=x(n.get(f));p(c),R.events.disableBlur(),c.focus(),a=!1}}else Ae.KEYCODE.ENTER==l&&(R.button.exec(t),a=!1);return!1===a&&(e.preventDefault(),e.stopPropagation()),a},!0)}(l),l}function p(e){var t=e.data("row");null!==t&&(t=parseInt(t));var a=e.data("col");null!==a&&(a=parseInt(a));var l=e.parent();l.siblings(".fr-table-size-info").html("".concat(t," &times; ").concat(a)),l.find("> span").removeClass("hover fr-active-item");for(var n=1;n<=R.opts.tableInsertMaxSize;n++)for(var r=0;r<=R.opts.tableInsertMaxSize;r++){var o=l.find('> span[data-row="'.concat(n,'"][data-col="').concat(r,'"]'));n<=t&&r<=a?o.addClass("hover"):n<=t+1||n<=2&&!R.helpers.isMobile()?o.css("display","inline-block"):2<n&&!R.helpers.isMobile()&&o.css("display","none")}e.addClass("fr-active-item")}function h(e){if(e)return R.popups.onHide("table.edit",o),!0;if(0<R.opts.tableEditButtons.length){var t={buttons:'<div class="fr-buttons">'.concat(R.button.buildList(R.opts.tableEditButtons),"</div>")},a=R.popups.create("table.edit",t);return R.events.$on(R.$wp,"scroll.table-edit",function(){R.popups.isVisible("table.edit")&&u()}),a}return!1}function e(){i[R.id]||(i[R.id]=x(document.createElement("div")).attr("class","fr-table-selector").html('<a role="button" tabIndex="-1" title="'.concat(R.language.translate("Select Table"),'"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path d="').concat(Ae.SVG.tableSelectorIcon,'"/></svg></a>')));var r=R.language.translate("The content you are pasting does not contain a table.\nPlease ensure the copied content is in table format.");R.events.on("paste.before",function(e,t){if(R.$el.find("table.fr-table-selected").length){var a=t||e&&e.clipboardData;if(a&&a.items){var l=(a||window.clipboardData).getData("text/html")||"";if(!l||-1===l.indexOf("<table"))return alert(r),e.preventDefault(),e.stopPropagation(),!1}}return!0}),R.events.on("html.beforeInsert",function(e){var t=R.$el.find("table.fr-table-selected");if(t.length){var a=function n(e){var t=R.doc.createElement("div");t.innerHTML=e;for(var a=t.firstChild;a;){var l=a.nextSibling;(a.nodeType===Node.TEXT_NODE&&""===a.textContent.replace(/\u200b/g,"").trim()||a.nodeType===Node.COMMENT_NODE||"SPAN"===a.tagName&&""===a.textContent.replace(/\u200b/g,"").trim())&&a.remove(),a=l}return 1===t.childNodes.length&&"TABLE"===t.childNodes[0].tagName&&t.childNodes[0]}(e);return a?(function m(e,t){for(var a=t.get(0),l=a.rows[0].cells[0],n=l&&l.parentNode?l.parentNode.rowIndex:0,r=l?l.cellIndex:0,o=a.rows,s=e.rows,i=0;i<e.rows.length;i++){if(!o[n+i])for(var f=o[o.length-1].cells.length,c=a.insertRow(),d=0;d<f;d++)c.insertCell();for(var p=o[n+i],h=0;h<e.rows[i].cells.length;h++){if(!p.cells[r+h])for(var g=0;g<o.length;g++)o[g].insertCell();var u=p.cells[r+h],b=s[i].cells[h];u.outerHTML=b.outerHTML}}}(a,t),R.keys.positionCaret(),R.events.trigger("html.inserted")):alert(r),!1}return!0}),R.events.on("mousedown",function(){q()}),R.events.$on(x(R.o_win),"resize",function(){var e=R.$el.find("table.fr-table-selected");e.length?J(e):q()}),R.events.on("commands.undo commands.redo commands.before",function(){q()});var e=null,t=null;R.events.on("snapshot.before",function(){t=R.$el.find("table.fr-table-selection-hover"),e=R.$el.find("table.fr-table-selected"),t.removeClass("fr-table-selection-hover"),e.removeClass("fr-table-selected")}),R.events.on("snapshot.after",function(){e&&e.addClass("fr-table-selected"),t&&t.addClass("fr-table-selection-hover")}),R.events.$on(i[R.id],"click",function(){var e=R.$el.find("table.fr-table-selection-hover");if(e.length){R.$el.find("table.fr-table-selected").removeClass("fr-table-selected"),i[R.id].addClass("fr-table-selector-active-selected"),e.addClass("fr-table-selected"),e.removeClass("fr-table-selection-hover");var t=R.doc.createRange();R.selection.get().removeAllRanges(),t.setStart(e.get(0),0),t.setEnd(e.get(0),0),R.selection.get().addRange(t),R.$el.focus()}}),R.events.on("destroy",function(){q(),i[R.id]=null})}function b(e){var t={startTag:"",closeTag:""};return R.opts.keepTextFormatOnTable&&(t=g(function s(e){var t=new Set,a=e.textContent,l=0,n=[e.firstChild];for(;0<n.length;){var r=n.pop();if(r&&(t.has(r)||(0===l&&a!==r.textContent&&(a=r.textContent,l++),t.add(r)),r.hasChildNodes()))for(var o=0;o<r.childNodes.length;){if(3===r.childNodes[o].nodeType)return $e(t);if(r.childNodes[o].textContent){if(0===l&&r.childNodes[o].textContent!==a){a=r.childNodes[o].textContent,n.push(r.childNodes[o]),l++;break}r.childNodes[o].textContent===a&&n.push(r.childNodes[o])}o++}}return $e(t)}(e).reverse())),{startTag:t.startTag,closeTag:t.closeTag}}function g(e){var t="",a="",l=["br","#text","#comment","font"];if(0<e.length)for(var n=e.length-1;0<=n;n--){var r=e[n],o=R.node.isBlock(r),s=l.indexOf(r.nodeName.toLowerCase());if(!o&&s<0){var i=r.style,f="";if(i&&i.length){for(var c=0;c<i.length;c++)f+=i[c]+": "+i[i[c]].replace(/"/gi,"")+"; ";f=f.trim()}t+=f?"<"+r.nodeName.toLowerCase()+' style="'+f+'">':"<"+r.nodeName.toLowerCase()+">",a="</"+r.nodeName.toLowerCase()+">"+a}}return{startTag:t,closeTag:a}}function m(){if(0<oe().length){var e=se();R.selection.setBefore(e.get(0))||R.selection.setAfter(e.get(0)),R.selection.restore(),R.popups.hide("table.edit"),R.opts.trackChangesEnabled?(R.track_changes.removedTable(e),S()):e.remove(),R.toolbar.enable()}}function v(e){var t=se();if(0<t.length){if(0<R.$el.find("thead .fr-selected-cell").length&&"above"==e||0<R.$el.find("tfoot td.fr-selected-cell").length&&"below"==e)return;var a,l,n,r=$(),o=I(r);if(null==o)return;l="above"==e?o.min_i:o.max_i;var s="<tr>";for(a=0;a<r[l].length;a++)if("below"==e&&l<r.length-1&&r[l][a]==r[l+1][a]||"above"==e&&0<l&&r[l][a]==r[l-1][a]){if(0===a||0<a&&r[l][a]!=r[l][a-1]){var i=x(r[l][a]);i.attr("rowspan",parseInt(i.attr("rowspan"),10)+1)}}else{var f=x(r[l][a]);if("TH"==f.attr("tagName").toUpperCase())s+='<th style="'+f.attr("style")+'" ><br></th>';else{var c=b(r[l][a]);s+='<td style="'+f.attr("style")+'" >'+c.startTag+"<br>"+c.closeTag+"</td>"}}s+="</tr>",n=0<R.$el.find("thead .fr-selected-cell").length&&"below"==e||0<R.$el.find("tfoot td.fr-selected-cell").length&&"above"==e?x(t.find("tbody").not(t.find("> table tbody"))):x(t.find("tr").not(t.find("> table tr")).get(l)),"below"==e?"TBODY"==n.attr("tagName")?n.prepend(s):n[0].parentNode&&n[0].insertAdjacentHTML("afterend",s):"above"==e&&("TBODY"==n.attr("tagName")?n.append(s):n.before(s),R.popups.isVisible("table.edit")&&u())}}function C(e,t,a){var l,n,r,o,s,i=0,f=$(a);if(e<(t=Math.min(t,f[0].length-1)))for(n=e;n<=t;n++)if(!(e<n&&f[0][n]==f[0][n-1])&&1<(o=Math.min(parseInt(f[0][n].getAttribute("colspan"),10)||1,t-e+1))&&f[0][n]==f[0][n+1])for(i=o-1,l=1;l<f.length;l++)if(f[l][n]!=f[l-1][n]){for(r=n;r<n+o;r++)if(f[l][r]!==undefined)if(1<(s=parseInt(f[l][r].getAttribute("colspan"),10)||1)&&f[l][r]==f[l][r+1])r+=i=Math.min(i,s-1);else if(!(i=Math.max(0,i-1)))break;if(!i)break}i&&E(f,i,"colspan",0,f.length-1,e,t)}function w(e,t,a){var l,n,r,o,s,i=0,f=$(a);if(e<(t=Math.min(t,f.length-1)))for(l=e;l<=t;l++)if(!(e<l&&f[l][0]==f[l-1][0])&&f[l][0]!==undefined&&1<(o=Math.min(parseInt(f[l][0].getAttribute("rowspan"),10)||1,t-e+1))&&f[l][0]==f[l+1][0])for(i=o-1,n=1;n<f[0].length;n++)if(f[l][n]!=f[l][n-1]){for(r=l;r<l+o;r++)if(f[r][n]!==undefined)if(1<(s=parseInt(f[r][n].getAttribute("rowspan"),10)||1)&&f[r][n]==f[r+1][n])r+=i=Math.min(i,s-1);else if(!(i=Math.max(0,i-1)))break;if(!i)break}i&&E(f,i,"rowspan",e,t,0,f[0].length-1)}function E(e,t,a,l,n,r,o){var s,i,f;for(s=l;s<=n;s++)for(i=r;i<=o;i++)l<s&&e[s][i]==e[s-1][i]||r<i&&e[s][i]==e[s][i-1]||e[s][i]!==undefined&&1<(f=parseInt(e[s][i].getAttribute(a),10)||1)&&(1<f-t?e[s][i].setAttribute(a,f-t):e[s][i].removeAttribute(a))}function T(e,t,a,l,n){w(e,t,n),C(a,l,n)}function t(e){var t=R.$el.find(".fr-selected-cell");"REMOVE"!=e?t.css("background-color",R.helpers.HEXtoRGB(e)):t.css("background-color",""),u()}function A(e){e.style.removeProperty("border"),e.style.removeProperty("border-top"),e.style.removeProperty("border-bottom"),e.style.removeProperty("border-left"),e.style.removeProperty("border-right")}function $(e){var f=[];if(null==(e=e||null)&&0<oe().length&&(e=se()),e){if(!e.find("tr").html().match(/xl[0-9]+/gi))for(var t=e.find("tr:empty"),a=t.length-1;0<=a;a--)x(t[a]).remove();e.find("tr").not(e.find("> table tr")).each(function(s,e){var t=x(e),i=0;t.find("> th, > td").each(function(e,t){for(var a=x(t),l=parseInt(a.attr("colspan"),10)||1,n=parseInt(a.attr("rowspan"),10)||1,r=s;r<s+n;r++)for(var o=i;o<i+l;o++)f[r]||(f[r]=[]),f[r][o]?i++:f[r][o]=t;i+=l})})}return f}function D(e,t){for(var a=0;a<t.length;a++)for(var l=0;l<t[a].length;l++)if(t[a][l]==e)return{row:a,col:l}}function N(e,t,a){for(var l=e+1,n=t+1;l<a.length;){if(a[l][t]!=a[e][t]){l--;break}l++}for(l==a.length&&l--;n<a[e].length;){if(a[e][n]!=a[e][t]){n--;break}n++}return n==a[e].length&&n--,{row:l,col:n}}function _(){R.el.querySelector(".fr-cell-fixed")&&R.el.querySelector(".fr-cell-fixed").classList.remove("fr-cell-fixed"),R.el.querySelector(".fr-cell-handler")&&R.el.querySelector(".fr-cell-handler").classList.remove("fr-cell-handler")}function S(){var e=R.$el.find(".fr-selected-cell");0<e.length&&e.each(function(){var e=x(this);e.removeClass("fr-selected-cell"),""===e.attr("class")&&e.removeAttr("class")}),_()}function M(){R.events.disableBlur(),R.selection.clear(),R.$el.addClass("fr-no-selection"),R.$el.blur(),R.events.enableBlur()}function I(e){var t=R.$el.find(".fr-selected-cell");if(0<t.length){var a,l=e.length,n=0,r=e[0].length,o=0;for(a=0;a<t.length;a++){var s=D(t[a],e),i=N(s.row,s.col,e);l=Math.min(s.row,l),n=Math.max(i.row,n),r=Math.min(s.col,r),o=Math.max(i.col,o)}return{min_i:l,max_i:n,min_j:r,max_j:o}}return null}function k(e){var t=I(e);if(null!=t){var a=x(e[t.min_i][t.min_j]),l=x(e[t.min_i][t.max_j]),n=x(e[t.max_i][t.min_j]);return{left:a.length&&a.offset().left,right:l.length&&l.offset().left+l.outerWidth(),top:a.length&&a.offset().top,bottom:n.length&&n.offset().top+n.outerHeight()}}}function K(e,t){if(x(e).is(t))S(),x(e).addClass("fr-selected-cell");else{M(),R.edit.off();var a=$(),l=D(e,a),n=D(t,a);if(!l||!n)return;var r=function h(e,t,a,l,n){var r,o,s,i,f=e,c=t,d=a,p=l;for(r=f;r<=c;r++)(1<(parseInt(x(n[r][d]).attr("rowspan"),10)||1)||1<(parseInt(x(n[r][d]).attr("colspan"),10)||1))&&(i=N((s=D(n[r][d],n)).row,s.col,n),f=Math.min(s.row,f),c=Math.max(i.row,c),d=Math.min(s.col,d),p=Math.max(i.col,p)),(1<(parseInt(x(n[r][p]).attr("rowspan"),10)||1)||1<(parseInt(x(n[r][p]).attr("colspan"),10)||1))&&(i=N((s=D(n[r][p],n)).row,s.col,n),f=Math.min(s.row,f),c=Math.max(i.row,c),d=Math.min(s.col,d),p=Math.max(i.col,p));for(o=d;o<=p;o++)(1<(parseInt(x(n[f][o]).attr("rowspan"),10)||1)||1<(parseInt(x(n[f][o]).attr("colspan"),10)||1))&&(i=N((s=D(n[f][o],n)).row,s.col,n),f=Math.min(s.row,f),c=Math.max(i.row,c),d=Math.min(s.col,d),p=Math.max(i.col,p)),(1<(parseInt(x(n[c][o]).attr("rowspan"),10)||1)||1<(parseInt(x(n[c][o]).attr("colspan"),10)||1))&&(i=N((s=D(n[c][o],n)).row,s.col,n),f=Math.min(s.row,f),c=Math.max(i.row,c),d=Math.min(s.col,d),p=Math.max(i.col,p));return f==e&&c==t&&d==a&&p==l?{min_i:e,max_i:t,min_j:a,max_j:l}:h(f,c,d,p,n)}(Math.min(l.row,n.row),Math.max(l.row,n.row),Math.min(l.col,n.col),Math.max(l.col,n.col),a);S(),e.classList.add("fr-cell-fixed"),t.classList.add("fr-cell-handler");for(var o=r.min_i;o<=r.max_i;o++)for(var s=r.min_j;s<=r.max_j;s++)x(e).closest("table").is(x(a[o][s]).closest("table"))&&x(a[o][s]).addClass("fr-selected-cell")}}function B(e){var t=null,a=x(e.target);return"TD"==e.target.tagName||"TH"==e.target.tagName?t=e.target:0<a.closest("th",a.closest("thead")[0]).length?t=a.closest("th",a.closest("thead")[0]).get(0):0<a.closest("td",a.closest("tr")[0]).length&&(t=a.closest("td",a.closest("tr")[0]).get(0)),-1===R.$el.html.toString().search(t)?null:t}function F(){S(),R.popups.hide("table.edit")}function L(e){var t=B(e);if("false"==x(t).parents("[contenteditable]").not(".fr-element").not(".fr-img-caption").not("body").first().attr("contenteditable"))return!0;if(0<oe().length&&!t&&F(),!R.edit.isDisabled()||R.popups.isVisible("table.edit"))if(1!=e.which||1==e.which&&R.helpers.isMac()&&e.ctrlKey)(3==e.which||1==e.which&&R.helpers.isMac()&&e.ctrlKey)&&t&&F();else if(r=!0,t){0<oe().length&&!e.shiftKey&&F(),e.stopPropagation(),R.events.trigger("image.hideResizer"),R.events.trigger("video.hideResizer"),n=!0;var a=t.tagName.toLowerCase();e.shiftKey&&0<R.$el.find("".concat(a,".fr-selected-cell")).length?x(R.$el.find("".concat(a,".fr-selected-cell")).closest("table")).is(x(t).closest("table"))?K(l,t):M():((R.keys.ctrlKey(e)||e.shiftKey)&&"TD"===e.currentTarget.tagName&&(1<oe().length||0===x(t).find(R.selection.element()).length&&!x(t).is(R.selection.element()))&&M(),l=t,0<R.opts.tableEditButtons.length&&K(l,l))}}function Y(e){if(!R.edit.isDisabled()&&R.popups.areVisible())return!0;var t=B(e);if(1===oe().length&&null===t&&(R.toolbar.enable(),S()),(1===oe().length&&t&&"TD"!==t.tagName&&"TH"!==t.tagName||!n&&!R.$tb.is(e.target)&&!R.$tb.is(x(e.target).closest(".fr-toolbar")))&&(R.toolbar.enable(),S()),("BODY"===e.target.tagName||"HTML"===e.target.tagName)&&!t&&0<oe().length&&R.toolbar.enable(),!(1!=e.which||1==e.which&&R.helpers.isMac()&&e.ctrlKey)){if(r=!1,n)n=!1,B(e)||1!=oe().length?0<oe().length?R.selection.isCollapsed()?(u(),R.toolbar.enable()):(S(),R.edit.on()):oe().length||(R.$el.removeClass("fr-no-selection"),R.edit.on()):S();if(y){y=!1,O.removeClass("fr-moving"),R.$el.removeClass("fr-no-selection"),R.edit.on();var a=parseFloat(O.css("left"))+R.opts.tableResizerOffset+R.$wp.offset().left;R.opts.iframe&&(a-=R.$iframe.offset().left),O.data("release-position",a),O.removeData("max-left"),O.removeData("max-right"),function T(){var e=O.data("origin"),t=O.data("release-position");if(e!==t){var a=O.data("first"),l=O.data("second"),n=O.data("table"),r=n.outerWidth();if(R.undo.canDo()||R.undo.saveStep(),null!=a&&null!=l){var o,s,i,f=$(n),c=[],d=[],p=[],h=[];for(o=0;o<f.length;o++)s=x(f[o][a]),i=x(f[o][l]),c[o]=s.outerWidth(),p[o]=i.outerWidth(),d[o]=c[o]/r*100,h[o]=p[o]/r*100;for(o=0;o<f.length;o++)if(s=x(f[o][a]),i=x(f[o][l]),f[o][a]!=f[o][l]){var g=(d[o]*(c[o]+t-e)/c[o]).toFixed(4);s.css("width",g+"%"),i.css("width",(d[o]+h[o]-g).toFixed(4)+"%")}}else{var u,b=n.parent(),m=parseFloat(b.css("padding-left")),v=parseFloat(b.css("padding-right")),C=m+v,w=r/(b.width()-C)*100,E=(parseInt(n.css("margin-left"),10)||0)/b.width()*100,y=(parseInt(n.css("margin-right"),10)||0)/b.width()*100;"rtl"==R.opts.direction&&0===l||"rtl"!=R.opts.direction&&0!==l?(u=(r+t-e)/r*w,n.css("margin-right","calc(100% - ".concat(Math.round(u).toFixed(4),"% - ").concat(Math.round(E).toFixed(4),"%)"))):("rtl"==R.opts.direction&&0!==l||"rtl"!=R.opts.direction&&0===l)&&(u=(r-t+e)/r*w,n.css("margin-left","calc(100% - ".concat(Math.round(u).toFixed(4),"% - ").concat(Math.round(y).toFixed(4),"%)"))),n.css("width","".concat(Math.round(u).toFixed(4),"%"))}R.selection.restore(),R.undo.saveStep(),R.events.trigger("table.resized",[n.get(0)])}O.removeData("origin"),O.removeData("release-position"),O.removeData("first"),O.removeData("second"),O.removeData("table")}(),V()}}}function z(e){if((!(x(e.currentTarget).is(x(e.originalEvent.relatedTarget))||e.currentTarget.contains(e.originalEvent.relatedTarget)||e.originalEvent.relatedTarget&&e.originalEvent.relatedTarget.contains(e.currentTarget))||e.currentTarget.querySelector("input, textarea"))&&(R.events.$on(x("input, textarea"),"click",ie),!0===n&&0<R.opts.tableEditButtons.length)){if(x(e.currentTarget).closest("table").is(se())){if("TBODY"===e.currentTarget.parentElement.parentElement.tagName&&"TD"==e.currentTarget.tagName&&0===R.$el.find("th.fr-selected-cell").length&&0===R.$el.find("tfoot td.fr-selected-cell").length)return void K(l,e.currentTarget);if("TFOOT"===e.currentTarget.parentElement.parentElement.tagName&&"TD"==e.currentTarget.tagName&&0===R.$el.find("tbody td.fr-selected-cell").length)return void K(l,e.currentTarget);if("TH"==e.currentTarget.tagName&&0===R.$el.find("td.fr-selected-cell").length)return void K(l,e.currentTarget)}"TD"!==e.currentTarget.tagName&&"TH"!==e.currentTarget.tagName&&M()}}function W(e,t,a,l){for(var n,r=t;r!=R.el&&"TD"!=r.tagName&&"TH"!=r.tagName&&("up"==l?n=r.previousElementSibling:"down"==l&&(n=r.nextElementSibling),!n);)r=r.parentNode;"TD"==r.tagName||"TH"==r.tagName?function o(e,t){for(var a=e;a&&"TABLE"!=a.tagName&&a.parentNode!=R.el;)a=a.parentNode;if(a&&"TABLE"==a.tagName){var l=$(x(a));"up"==t?H(D(e,l),a,l):"down"==t&&P(D(e,l),a,l)}}(r,l):n&&("up"==l&&R.selection.setAtEnd(n),"down"==l&&R.selection.setAtStart(n))}function H(e,t,a){0<x(".tribute-container").length&&"none"!=x(".tribute-container").css("display")||(0<e.row?R.selection.setAtEnd(a[e.row-1][e.col]):W(0,t,0,"up"))}function P(e,t,a){if(!(0<x(".tribute-container").length&&"none"!=x(".tribute-container").css("display"))){var l=parseInt(a[e.row][e.col].getAttribute("rowspan"),10)||1;e.row<a.length-l?R.selection.setAtStart(a[e.row+l][e.col]):W(0,t,0,"down")}}function V(){O&&(O.find("div").css("opacity",0),O.css("top",0),O.css("left",0),O.css("height",0),O.find("div").css("height",0),O.hide())}function j(){f&&f.removeClass("fr-visible").css("left","-9999px")}function a(e,t){var a,l=x(t);l&&(a=l.closest("table"));var n=a.parent();if(t&&"TD"!=t.tagName&&"TH"!=t.tagName&&(0<l.closest("td").length?t=l.closest("td"):0<l.closest("th").length&&(t=l.closest("th"))),!t||"TD"!=t.tagName&&"TH"!=t.tagName)O&&l.get(0)!=O.get(0)&&l.parent().get(0)!=O.get(0)&&R.core.sameInstance(O)&&V();else{if(l=x(t),0===R.$el.find(l).length)return!1;var r=l.offset().left-1,o=r+l.outerWidth();if(Math.abs(e.pageX-r)<=R.opts.tableResizerOffset||Math.abs(o-e.pageX)<=R.opts.tableResizerOffset){var s,i,f,c,d,p=$(a),h=D(t,p),g=N(h.row,h.col,p),u=a.offset().top,b=a.outerHeight()-1;"rtl"!=R.opts.direction?e.pageX-r<=R.opts.tableResizerOffset?(f=r,0<h.col?(c=r-ee(h.col-1,p)+R.opts.tableResizingLimit,d=r+ee(h.col,p)-R.opts.tableResizingLimit,s=h.col-1,i=h.col):(s=null,i=0,c=a.offset().left-1-parseInt(a.css("margin-left"),10),d=a.offset().left-1+a.width()-p[0].length*R.opts.tableResizingLimit)):o-e.pageX<=R.opts.tableResizerOffset&&(f=o,g.col<p[g.row].length&&p[g.row][g.col+1]?(c=o-ee(g.col,p)+R.opts.tableResizingLimit,d=o+ee(g.col+1,p)-R.opts.tableResizingLimit,s=g.col,i=g.col+1):(s=g.col,i=null,c=a.offset().left-1+p[0].length*R.opts.tableResizingLimit,d=n.offset().left-1+n.width()+parseFloat(n.css("padding-left")))):o-e.pageX<=R.opts.tableResizerOffset?(f=o,0<h.col?(c=o-ee(h.col,p)+R.opts.tableResizingLimit,d=o+ee(h.col-1,p)-R.opts.tableResizingLimit,s=h.col,i=h.col-1):(s=null,i=0,c=a.offset().left+p[0].length*R.opts.tableResizingLimit,d=n.offset().left-1+n.width()+parseFloat(n.css("padding-left")))):e.pageX-r<=R.opts.tableResizerOffset&&(f=r,g.col<p[g.row].length&&p[g.row][g.col+1]?(c=r-ee(g.col+1,p)+R.opts.tableResizingLimit,d=r+ee(g.col,p)-R.opts.tableResizingLimit,s=g.col+1,i=g.col):(s=g.col,i=null,c=n.offset().left+parseFloat(n.css("padding-left")),d=a.offset().left-1+a.width()-p[0].length*R.opts.tableResizingLimit)),O||function E(){R.shared.$table_resizer||(R.shared.$table_resizer=x(document.createElement("div")).attr("class","fr-table-resizer").html("<div></div>")),O=R.shared.$table_resizer,R.events.$on(O,"mousedown",function(e){return!R.core.sameInstance(O)||(0<oe().length&&F(),1==e.which?(R.selection.save(),y=!0,O.addClass("fr-moving"),M(),R.edit.off(),O.find("div").css("opacity",1),!1):void 0)}),R.events.$on(O,"mousemove",function(e){if(!R.core.sameInstance(O))return!0;y&&(R.opts.iframe&&(e.pageX-=R.$iframe.offset().left),ae(e))}),R.events.on("shared.destroy",function(){O.html("").removeData().remove(),O=null},!0),R.events.on("destroy",function(){R.$el.find(".fr-selected-cell").removeClass("fr-selected-cell"),x("body").first().append(O.hide())},!0)}(),O.data("table",a),O.data("first",s),O.data("second",i),O.data("instance",R),R.$wp.append(O);var m=f-R.win.pageXOffset-R.opts.tableResizerOffset-R.$wp.offset().left,v=u-R.$wp.offset().top+R.$wp.scrollTop();if(R.opts.iframe){var C=R.helpers.getPX(R.$wp.find(".fr-iframe").css("padding-top")),w=R.helpers.getPX(R.$wp.find(".fr-iframe").css("padding-left"));m+=R.$iframe.offset().left+w,v+=R.$iframe.offset().top+C,c+=R.$iframe.offset().left,d+=R.$iframe.offset().left}O.data("max-left",c),O.data("max-right",d),O.data("origin",f-R.win.pageXOffset),O.css("top",v),O.css("left",m),O.css("height",b),O.find("div").css("height",b),O.css("padding-left",R.opts.tableResizerOffset),O.css("padding-right",R.opts.tableResizerOffset),O.show()}else R.core.sameInstance(O)&&V()}}function X(e,t){if(R.$box.find(".fr-line-breaker").isVisible())return!1;f||ne(),R.$box.append(f),f.data("instance",R);var a,l=x(t).find("tr").first(),n=e.pageX,r=0,o=0;if(R.opts.iframe){var s=R.helpers.getPX(R.$wp.find(".fr-iframe").css("padding-top")),i=R.helpers.getPX(R.$wp.find(".fr-iframe").css("padding-left"));r+=R.$iframe.offset().left-R.helpers.scrollLeft()+i,o+=R.$iframe.offset().top-R.helpers.scrollTop()+s}l.find("th, td").each(function(){var e=x(this);return e.offset().left<=n&&n<e.offset().left+e.outerWidth()/2?(a=parseInt(f.find("a").css("width"),10),f.css("top",o+e.offset().top-R.$box.offset().top-a-5),f.css("left",r+e.offset().left-R.$box.offset().left-a/2),f.data("selected-cell",e),f.data("position","before"),f.addClass("fr-visible"),!1):e.offset().left+e.outerWidth()/2<=n&&n<e.offset().left+e.outerWidth()?(a=parseInt(f.find("a").css("width"),10),f.css("top",o+e.offset().top-R.$box.offset().top-a-5),f.css("left",r+e.offset().left-R.$box.offset().left+e.outerWidth()-a/2),f.data("selected-cell",e),f.data("position","after"),f.addClass("fr-visible"),!1):void 0})}function G(e,t){if(R.$box.find(".fr-line-breaker").isVisible())return!1;f||ne(),R.$box.append(f),f.data("instance",R);var a,l=x(t),n=e.pageY,r=0,o=0;if(R.opts.iframe){var s=R.helpers.getPX(R.$wp.find(".fr-iframe").css("padding-top")),i=R.helpers.getPX(R.$wp.find(".fr-iframe").css("padding-left"));r+=R.$iframe.offset().left-R.helpers.scrollLeft()+i,o+=R.$iframe.offset().top-R.helpers.scrollTop()+s}l.find("tr").each(function(){var e=x(this);a=parseInt(f.find("a").css("width"),10);var t=r+e.offset().left-R.$box.offset().left;return t=0!==R.$box.offset().left?t-a-5:t+a-5,e.offset().top<=n&&n<e.offset().top+e.outerHeight()/2?(f.css("top",o+e.offset().top-R.$box.offset().top-a/2),f.css("left",t),f.data("selected-cell",e.find("td").first()),f.data("position","above"),f.addClass("fr-visible"),!1):e.offset().top+e.outerHeight()/2<=n&&n<e.offset().top+e.outerHeight()?(f.css("top",o+e.offset().top-R.$box.offset().top+e.outerHeight()-a/2),f.css("left",t),f.data("selected-cell",e.find("td").first()),f.data("position","below"),f.addClass("fr-visible"),!1):void 0})}function U(){i[R.id]&&(i[R.id].css("top",-9999),i[R.id].css("left",-9999),i[R.id].removeClass("fr-table-selector-active-selected"),i[R.id].removeClass("fr-table-selector-active"),i[R.id].addClass("fr-table-selector-inactive"),R.$el.find("table.fr-table-selection-hover").removeClass("fr-table-selection-hover"))}function q(){var e=R.$el.find("table.fr-table-selected");e.removeClass("fr-table-selected"),e.removeClass("fr-table-selection-hover"),U()}function J(e){R.$box.append(i[R.id]),i[R.id].data("instance",R);var t=e.offset().left-R.$box.offset().left+16,a=e.offset().top-R.$box.offset().top-i[R.id].outerHeight();if(R.opts.iframe){var l=R.helpers.getPX(R.$wp.find(".fr-iframe").css("padding-top")),n=R.helpers.getPX(R.$wp.find(".fr-iframe").css("padding-left"));t+=R.$iframe.offset().left-R.helpers.scrollLeft()+n,a+=R.$iframe.offset().top-R.helpers.scrollTop()+l}i[R.id].css("top",a),i[R.id].css("left",t)}function Z(e){s=null;var t=R.doc.elementFromPoint(e.pageX-R.win.pageXOffset,e.pageY-R.win.pageYOffset);R.opts.tableResizer&&(!R.popups.areVisible()||R.popups.areVisible()&&R.popups.isVisible("table.edit"))&&a(e,t),!R.opts.tableInsertHelper||R.popups.areVisible()||R.$tb.hasClass("fr-inline")&&R.$tb.isVisible()||function r(e,t){if(0===oe().length){var a,l,n;if(t&&("HTML"==t.tagName||"BODY"==t.tagName||R.node.isElement(t)))for(a=1;a<=R.opts.tableInsertHelperOffset;a++){if(l=R.doc.elementFromPoint(e.pageX-R.win.pageXOffset,e.pageY-R.win.pageYOffset+a),x(l).hasClass("fr-tooltip"))return!0;if(l&&("TH"==l.tagName||"TD"==l.tagName||"TABLE"==l.tagName)&&(x(l).parents(".fr-wrapper").length||R.opts.iframe)&&"false"!=x(l).closest("table").attr("contenteditable"))return X(e,x(l).closest("table")),!0;if(n=R.doc.elementFromPoint(e.pageX-R.win.pageXOffset+a,e.pageY-R.win.pageYOffset),x(n).hasClass("fr-tooltip"))return!0;if(n&&("TH"==n.tagName||"TD"==n.tagName||"TABLE"==n.tagName)&&(x(n).parents(".fr-wrapper").length||R.opts.iframe)&&"false"!=x(n).closest("table").attr("contenteditable"))return G(e,x(n).closest("table")),!0}R.core.sameInstance(f)&&j()}}(e,t),function o(e,t){var a=x(t),l=R.$el.find("table.fr-table-selection-hover"),n=R.$el.find("table.fr-table-selected");if(R.node.isElement(t)||R.node.isElement(a.parents(".fr-view").first().get(0))||R.core.sameInstance(a)||R.core.sameInstance(a.parents("div.fr-table-selector"))){if(!a.parents(".fr-table-selector").length){var r=a?a.closest("table"):null;r.length?(J(r),i[R.id].addClass("fr-table-selector-active"),i[R.id].removeClass("fr-table-selector-inactive"),l.removeClass("fr-table-selection-hover"),n.length&&i[R.id].toggleClass("fr-table-selector-active-selected",n.get(0)===r.get(0)),r.addClass("fr-table-selection-hover")):l.length&&Math.abs(l.offset().top-e.pageY)<16||i[R.id].hasClass("fr-table-selector-inactive")||U()}}else n.length||U()}(e,t)}function Q(){if(y){var e=O.data("table").offset().top-R.win.pageYOffset;if(R.opts.iframe){var t=R.helpers.getPX(R.$wp.find(".fr-iframe").css("padding-top"));e+=R.$iframe.offset().top-R.helpers.scrollTop()+t}O.css("top",e)}}function ee(e,t){var a,l=x(t[0][e]).outerWidth();for(a=1;a<t.length;a++)l=Math.min(l,x(t[a][e]).outerWidth());return l}function te(e,t,a){var l,n=0;for(l=e;l<=t;l++)n+=ee(l,a);return n}function ae(e){if(1<oe().length&&r&&M(),!1===r&&!1===n&&!1===y)s&&clearTimeout(s),R.edit.isDisabled()&&!R.popups.isVisible("table.edit")||(s=setTimeout(Z,30,e));else if(y){var t=e.pageX-R.win.pageXOffset;R.opts.iframe&&(t+=R.$iframe.offset().left);var a=O.data("max-left"),l=O.data("max-right");a<=t&&t<=l?O.css("left",t-R.opts.tableResizerOffset-R.$wp.offset().left):t<a&&parseFloat(O.css("left"),10)>a-R.opts.tableResizerOffset?O.css("left",a-R.opts.tableResizerOffset-R.$wp.offset().left):l<t&&parseFloat(O.css("left"),10)<l-R.opts.tableResizerOffset&&O.css("left",l-R.opts.tableResizerOffset-R.$wp.offset().left)}else r&&j()}function le(e){R.node.isEmpty(e.get(0))?R.opts.keepTextFormatOnTable&&R.browser.mozilla?e.append(Ae.MARKERS):e.prepend(Ae.MARKERS):e.prepend(Ae.START_MARKER).append(Ae.END_MARKER)}function ne(){R.shared.$ti_helper||(R.shared.$ti_helper=x(document.createElement("div")).attr("class","fr-insert-helper").html('<a class="fr-floating-btn" role="button" tabIndex="-1" title="'.concat(R.language.translate("Insert"),'"><svg viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg"><path d="M22,16.75 L16.75,16.75 L16.75,22 L15.25,22.000 L15.25,16.75 L10,16.75 L10,15.25 L15.25,15.25 L15.25,10 L16.75,10 L16.75,15.25 L22,15.25 L22,16.75 Z"/></svg></a>')),R.events.bindClick(R.shared.$ti_helper,"a",function(){var e=f.data("selected-cell"),t=f.data("position"),a=f.data("instance")||R;"before"==t?(R.undo.saveStep(),e.addClass("fr-selected-cell"),a.table.insertColumn(t),e.removeClass("fr-selected-cell"),R.undo.saveStep()):"after"==t?(R.undo.saveStep(),e.addClass("fr-selected-cell"),a.table.insertColumn(t),e.removeClass("fr-selected-cell"),R.undo.saveStep()):"above"==t?(R.undo.saveStep(),e.addClass("fr-selected-cell"),a.table.insertRow(t),e.removeClass("fr-selected-cell"),R.undo.saveStep()):"below"==t&&(R.undo.saveStep(),e.addClass("fr-selected-cell"),a.table.insertRow(t),e.removeClass("fr-selected-cell"),R.undo.saveStep()),j()}),R.events.on("shared.destroy",function(){R.shared.$ti_helper.html("").removeData().remove(),R.shared.$ti_helper=null},!0),R.events.$on(R.shared.$ti_helper,"mousemove",function(e){e.stopPropagation()},!0),R.events.$on(x(R.o_win),"scroll",function(){j()},!0),R.events.$on(R.$wp,"scroll",function(){j()},!0)),f=R.shared.$ti_helper,R.events.on("destroy",function(){f=null}),R.tooltip.bind(R.$box,".fr-insert-helper > a.fr-floating-btn")}function re(){l=null,clearTimeout(s)}function oe(){return R.el.querySelectorAll(".fr-selected-cell")}function se(){var e=oe();if(e.length){for(var t=e[0];t&&"TABLE"!=t.tagName&&t.parentNode!=R.el;)t=t.parentNode;return t&&"TABLE"==t.tagName?x(t):x([])}return x([])}function ie(){n=!1}return{_init:function fe(){if(!R.$wp)return!1;if(R.helpers.isMobile()&&(R.events.$on(R.$el,"mousedown",L),R.events.$on(R.$win,"mouseup",Y)),!R.helpers.isMobile()){y=n=r=!1,R.events.$on(R.$el,"mousedown",L),R.popups.onShow("image.edit",function(){S(),n=r=!1}),R.popups.onShow("link.edit",function(){S(),n=r=!1}),R.events.on("commands.mousedown",function(e){0<e.parents(".fr-toolbar").length&&S()}),R.events.$on(R.$el,"mouseover","th, td",z),R.events.$on(R.$win,"mouseup",Y),R.opts.iframe&&R.events.$on(x(R.o_win),"mouseup",Y),R.events.$on(R.$win,"mousemove",ae),R.events.$on(x(R.o_win),"scroll",Q),R.events.on("contentChanged",function(){0<oe().length&&(u(),R.$el.find("img").on("load.selected-cells",function(){x(this).off("load.selected-cells"),0<oe().length&&u()}))}),R.events.$on(x(R.o_win),"resize",function(){S()}),R.events.on("toolbar.esc",function(){if(0<oe().length)return R.events.disableBlur(),R.events.focus(),!1},!0),R.events.$on(x(R.o_win),"keydown",function(){r&&n&&(n=r=!1,R.$el.removeClass("fr-no-selection"),R.edit.on(),R.selection.setAtEnd(R.$el.find(".fr-selected-cell").last().get(0)),R.selection.restore(),S())}),R.events.$on(R.$el,"keydown",function(e){e.shiftKey?!1===function o(e){var t=oe();if(null!=t&&0<t.length){var a,l=$(),n=e.which,r=D(1==t.length?a=t[0]:(a=R.el.querySelector(".fr-cell-fixed"),R.el.querySelector(".fr-cell-handler")),l);if(Ae.KEYCODE.ARROW_RIGHT==n){if(r.col<l[0].length-1)return K(a,l[r.row][r.col+1]),!1}else if(Ae.KEYCODE.ARROW_DOWN==n){if(r.row<l.length-1)return K(a,l[r.row+1][r.col]),!1}else if(Ae.KEYCODE.ARROW_LEFT==n){if(0<r.col)return K(a,l[r.row][r.col-1]),!1}else if(Ae.KEYCODE.ARROW_UP==n&&0<r.row)return K(a,l[r.row-1][r.col]),!1}}(e)&&setTimeout(function(){u()},0):function s(e){var t=e.which,a=R.selection.blocks();if(a.length&&("P"!==(a=a[0]).tagName&&"DIV"!==a.tagName||(a=a.parentNode),"TD"==a.tagName||"TH"==a.tagName)){for(var l=a;l&&"TABLE"!=l.tagName&&l.parentNode!=R.el;)l=l.parentNode;if(l&&"TABLE"==l.tagName&&(Ae.KEYCODE.ARROW_LEFT==t||Ae.KEYCODE.ARROW_UP==t||Ae.KEYCODE.ARROW_RIGHT==t||Ae.KEYCODE.ARROW_DOWN==t)&&(0<oe().length&&F(),R.browser.webkit&&(Ae.KEYCODE.ARROW_UP==t||Ae.KEYCODE.ARROW_DOWN==t))){var n=R.selection.ranges(0).startContainer;if(n.nodeType==Node.TEXT_NODE&&(Ae.KEYCODE.ARROW_UP==t&&(n.previousSibling&&"BR"!==n.previousSibling.tagName||n.previousSibling&&"BR"===n.previousSibling.tagName&&n.previousSibling.previousSibling)||Ae.KEYCODE.ARROW_DOWN==t&&(n.nextSibling&&"BR"!==n.nextSibling.tagName||n.nextSibling&&"BR"===n.nextSibling.tagName&&n.nextSibling.nextSibling)))return;e.preventDefault(),e.stopPropagation();var r=$(x(l)),o=D(a,r);return Ae.KEYCODE.ARROW_UP==t?H(o,l,r):Ae.KEYCODE.ARROW_DOWN==t&&P(o,l,r),R.selection.restore(),!1}}}(e)}),R.events.on("keydown",function(e){if(!1===function l(e){if(e.which==Ae.KEYCODE.TAB){var t;if(0<oe().length)t=R.$el.find(".fr-selected-cell").last();else{var a=R.selection.element();"TD"==a.tagName||"TH"==a.tagName?t=x(a):a!=R.el&&(0<x(a).parentsUntil(R.$el,"td").length?t=x(a).parents("td").first():0<x(a).parentsUntil(R.$el,"th").length&&(t=x(a).parents("th").first()))}if(t)return e.preventDefault(),!!(0===R.selection.get().focusOffset&&0<x(R.selection.element()).parentsUntil(R.$el,"ol, ul").length&&(0<x(R.selection.element()).closest("li").prev().length||x(R.selection.element()).is("li")&&0<x(R.selection.element()).prev().length))||(F(),e.shiftKey?0<t.prev().length?le(t.prev()):0<t.closest("tr").length&&0<t.closest("tr").prev().length?le(t.closest("tr").prev().find("td").last()):0<t.closest("tbody").length&&0<t.closest("table").find("thead tr").length&&le(t.closest("table").find("thead tr th").last()):0<t.next().length?le(t.next()):0<t.closest("tr").length&&0<t.closest("tr").next().length?le(t.closest("tr").next().find("td").first()):0<t.closest("thead").length&&0<t.closest("table").find("tbody tr").length?le(t.closest("table").find("tbody tr td").first()):(t.addClass("fr-selected-cell"),v("below"),S(),le(t.closest("tr").next().find("td").first())),R.selection.restore(),!1)}}(e))return!1;if(e.which==Ae.KEYCODE.ESC&&q(),R.$el.find("table.fr-table-selected").length){if(!e.ctrlKey&&!e.metaKey)return e.preventDefault(),e.stopPropagation(),!1;"a"===e.key&&q()}var t=oe();if(0<t.length){if(0<t.length&&R.keys.ctrlKey(e)&&e.which==Ae.KEYCODE.A)return S(),R.popups.isVisible("table.edit")&&R.popups.hide("table.edit"),t=[],!0;if(e.which==Ae.KEYCODE.ESC&&R.popups.isVisible("table.edit"))return S(),R.popups.hide("table.edit"),e.preventDefault(),e.stopPropagation(),e.stopImmediatePropagation(),!(t=[]);if(1<t.length&&(e.which==Ae.KEYCODE.BACKSPACE||e.which==Ae.KEYCODE.DELETE)){R.undo.saveStep();for(var a=0;a<t.length;a++)x(t[a]).html("<br>"),a==t.length-1&&x(t[a]).prepend(Ae.MARKERS);return R.selection.restore(),R.undo.saveStep(),!(t=[])}if(1<t.length&&e.which!=Ae.KEYCODE.F10&&!R.keys.isBrowserAction(e))return e.preventDefault(),!(t=[])}else if(!(t=[])===function n(e){if(e.altKey&&e.which==Ae.KEYCODE.SPACE){var t,a=R.selection.element();if("TD"==a.tagName||"TH"==a.tagName?t=a:0<x(a).closest("td").length?t=x(a).closest("td").get(0):0<x(a).closest("th").length&&(t=x(a).closest("th").get(0)),t)return e.preventDefault(),K(t,t),u(),!1}}(e))return!1},!0);var t=null,a=[];R.events.on("html.beforeGet",function(){a=oe();for(var e=0;e<a.length;e++)a[e].className=(a[e].className||"").replace(/fr-selected-cell/g,"");(t=R.$el.find("table.fr-table-selected"))&&t.length&&t.removeClass("fr-table-selected")}),R.events.on("html.afterGet",function(){for(var e=0;e<a.length;e++)a[e].className=(a[e].className?a[e].className.trim()+" ":"")+"fr-selected-cell";a=[],t&&t.length&&t.addClass("fr-table-selected")}),d(!0),h(!0),e()}R.events.on("destroy",re)},insert:function ce(e,t){var a,l,n=function f(){var e={startTag:"",closeTag:""};if(R.opts.keepTextFormatOnTable){var t=x(R.selection.element().firstChild).parentsUntil(R.$el);e=g(t)}return{startTag:e.startTag,closeTag:e.closeTag}}(),r="<table "+(R.opts.tableDefaultWidth?'style="width: '+R.opts.tableDefaultWidth+';" ':"")+'class="fr-inserted-table"><tbody>',o=100/t;for(a=0;a<e;a++){for(r+="<tr>",l=0;l<t;l++)r+="<td"+(R.opts.tableDefaultWidth?' style="width: '+o.toFixed(4)+'%;"':"")+">"+n.startTag,0===a&&0===l&&(r+=Ae.MARKERS),r+="<br>"+n.closeTag+"</td>";r+="</tr>"}if(r+="</tbody></table>",R.opts.trackChangesEnabled){R.edit.on(),R.events.focus(!0),R.selection.restore(),R.undo.saveStep(),R.markers.insert(),R.html.wrap();var s=R.$el.find(".fr-marker");R.node.isLastSibling(s)&&s.parent().hasClass("fr-deletable")&&s.insertAfter(s.parent()),s.replaceWith(r),R.selection.clear()}else R.html.insert(r);R.selection.restore();var i=R.$el.find(".fr-inserted-table");i.removeClass("fr-inserted-table"),R.events.trigger("table.inserted",[i.get(0)])},remove:m,insertRow:v,deleteRow:function de(){var e=se();if(0<e.length){var t,a,l,n=$(),r=I(n);if(null==r)return;if(0===r.min_i&&r.max_i==n.length-1)m();else{for(t=r.max_i;t>=r.min_i;t--){for(l=x(e.find("tr").not(e.find("> table tr")).get(t)),a=0;a<n[t].length;a++)if(0===a||n[t][a]!=n[t][a-1]){var o=x(n[t][a]);if(1<parseInt(o.attr("rowspan"),10)){var s=parseInt(o.attr("rowspan"),10)-1;1==s?o.removeAttr("rowspan"):o.attr("rowspan",s)}if(t<n.length-1&&n[t][a]==n[t+1][a]&&(0===t||n[t][a]!=n[t-1][a])){for(var i=n[t][a],f=a;0<f&&n[t][f]==n[t][f-1];)f--;0===f?x(e.find("tr").not(e.find("> table tr")).get(t+1)).prepend(i):x(n[t+1][f-1])[0].parentNode&&x(n[t+1][f-1])[0].insertAdjacentElement("afterend",i)}}var c=l.parent();l.remove(),0===c.find("tr").length&&c.remove(),n=$(e)}T(0,n.length-1,0,n[0].length-1,e),0<r.min_i?R.selection.setAtEnd(n[r.min_i-1][0]):R.selection.setAtEnd(n[0][0]),R.selection.restore(),R.popups.hide("table.edit")}}},insertColumn:function pe(c){var e=se();if(0<e.length){var d,p=$(),t=I(p);d="before"==c?t.min_j:t.max_j;var a,h=100/p[0].length,g=100/(p[0].length+1);e.find("th, td").each(function(){(a=x(this)).data("old-width",a.outerWidth()/e.outerWidth()*100)}),e.find("tr").not(e.find("> table tr")).each(function(e){for(var t,a=x(this),l=0,n=0;l-1<d;){if(!(t=a.find("> th, > td").get(n))){t=null;break}t==p[e][l]?(l+=parseInt(x(t).attr("colspan"),10)||1,n++):(l+=parseInt(x(p[e][l]).attr("colspan"),10)||1,"after"==c&&(t=0===n?-1:a.find("> th, > td").get(n-1)))}var r=x(t);if("after"==c&&d<l-1||"before"==c&&0<d&&p[e][d]==p[e][d-1]){if(0===e||0<e&&p[e][d]!=p[e-1][d]){var o=parseInt(r.attr("colspan"),10)+1;r.attr("colspan",o),r.css("width",(r.data("old-width")*g/h+g).toFixed(4)+"%"),r.removeData("old-width")}}else{var s,i=(r.get(0)||"").tagName;if(0<a.find("th").length&&"TH"===i)s='<th style="width: '.concat(g.toFixed(4),'%;"><br></th>');else{var f=b(p[e][d]);s='<td style="'.concat(r.attr("style"),"; width: ").concat(g.toFixed(4),'%;">')+f.startTag+"<br>"+f.closeTag+"</td>"}x(s).append("<br>"),x(s).css("width","".concat(g.toFixed(4),"%")),-1==t?a.prepend(s):null==t?a.append(s):"before"==c?r.before(s):"after"==c&&r.after(s)}}),e.find("th, td").each(function(){(a=x(this)).data("old-width")&&(a.css("width",(a.data("old-width")*g/h).toFixed(4)+"%"),a.removeData("old-width"))}),R.popups.isVisible("table.edit")&&u()}},deleteColumn:function he(){var e=se();if(0<e.length){var t,a,l,n=$(),r=I(n);if(null==r)return;if(0===r.min_j&&r.max_j==n[0].length-1)m();else{var o=0;for(t=0;t<n.length;t++)for(a=0;a<n[0].length;a++)(l=x(n[t][a])).hasClass("fr-selected-cell")||(l.data("old-width",l.outerWidth()/e.outerWidth()*100),(a<r.min_j||a>r.max_j)&&(o+=l.outerWidth()/e.outerWidth()*100));for(o/=n.length,a=r.max_j;a>=r.min_j;a--)for(t=0;t<n.length;t++)if(0===t||n[t][a]!=n[t-1][a])if(l=x(n[t][a]),1<(parseInt(l.attr("colspan"),10)||1)){var s=parseInt(l.attr("colspan"),10)-1;1==s?l.removeAttr("colspan"):l.attr("colspan",s),l.css("width",(100*(l.data("old-width")-ee(a,n))/o).toFixed(4)+"%"),l.removeData("old-width")}else{var i=x(l.parent().get(0));l.remove(),0===i.find("> th, > td").length&&(0===i.prev().length||0===i.next().length||i.prev().find("> th[rowspan], > td[rowspan]").length<i.prev().find("> th, > td").length)&&i.remove()}T(0,n.length-1,0,n[0].length-1,e),0<r.min_j?R.selection.setAtEnd(n[r.min_i][r.min_j-1]):R.selection.setAtEnd(n[r.min_i][0]),R.selection.restore(),R.popups.hide("table.edit"),e.find("th, td").each(function(){(l=x(this)).data("old-width")&&(l.css("width",(100*l.data("old-width")/o).toFixed(4)+"%"),l.removeData("old-width"))})}}},mergeCells:function ge(){if(1<oe().length&&(0===R.$el.find("th.fr-selected-cell").length||0===R.$el.find("td.fr-selected-cell").length)){_();var e,t,a=I($());if(null==a)return;var l=R.$el.find(".fr-selected-cell"),n=x(l[0]),r=n.parent().find(".fr-selected-cell"),o=n.closest("table"),s=n.html(),i=0;for(e=0;e<r.length;e++)i+=x(r[e]).outerWidth();for(n.css("width",Math.min(100,i/o.outerWidth()*100).toFixed(4)+"%"),a.min_j<a.max_j&&n.attr("colspan",a.max_j-a.min_j+1),a.min_i<a.max_i&&n.attr("rowspan",a.max_i-a.min_i+1),e=1;e<l.length;e++)"<br>"!=(t=x(l[e])).html()&&""!==t.html()&&(s+="<br>".concat(t.html())),t.remove();n.html(s),R.selection.setAtEnd(n.get(0)),R.selection.restore(),R.toolbar.enable(),w(a.min_i,a.max_i,o);var f=o.find("tr:empty");for(e=f.length-1;0<=e;e--)x(f[e]).remove();C(a.min_j,a.max_j,o),u()}},splitCellVertically:function ue(){if(1==oe().length){var e=R.$el.find(".fr-selected-cell"),t=parseInt(e.attr("colspan"),10)||1,a=e.parent().outerWidth(),l=e.outerWidth(),n=e.clone().html("<br>"),r=$(),o=D(e.get(0),r);if(1<t){var s=Math.ceil(t/2);l=te(o.col,o.col+s-1,r)/a*100;var i=te(o.col+s,o.col+t-1,r)/a*100;1<s?e.attr("colspan",s):e.removeAttr("colspan"),1<t-s?n.attr("colspan",t-s):n.removeAttr("colspan"),e.css("width",l.toFixed(4)+"%"),n.css("width",i.toFixed(4)+"%")}else{var f;for(f=0;f<r.length;f++)if(0===f||r[f][o.col]!=r[f-1][o.col]){var c=x(r[f][o.col]);if(!c.is(e)){var d=(parseInt(c.attr("colspan"),10)||1)+1;c.attr("colspan",d)}}l=l/a*100/2,e.css("width","".concat(l.toFixed(4),"%")),n.css("width","".concat(l.toFixed(4),"%"))}e[0].parentNode&&e[0].insertAdjacentElement("afterend",n[0]),S(),R.popups.hide("table.edit")}},splitCellHorizontally:function be(){if(1==oe().length){var e=R.$el.find(".fr-selected-cell"),t=e.parent(),a=e.closest("table"),l=parseInt(e.attr("rowspan"),10),n=$(),r=D(e.get(0),n),o=e.clone().html("<br>");if(1<l){var s=Math.ceil(l/2);1<s?e.attr("rowspan",s):e.removeAttr("rowspan"),1<l-s?o.attr("rowspan",l-s):o.removeAttr("rowspan");for(var i=r.row+s,f=0===r.col?r.col:r.col-1;0<=f&&(n[i][f]==n[i][f-1]||0<i&&n[i][f]==n[i-1][f]);)f--;-1==f?x(a.find("tr").not(a.find("> table tr")).get(i)).prepend(o):x(n[i][f])[0].parentNode&&x(n[i][f])[0].insertAdjacentElement("afterend",o[0])}else{var c,d=x(document.createElement("tr")).append(o);for(c=0;c<n[0].length;c++)if(0===c||n[r.row][c]!=n[r.row][c-1]){var p=x(n[r.row][c]);p.is(e)||p.attr("rowspan",(parseInt(p.attr("rowspan"),10)||1)+1)}t[0].parentNode&&t[0].insertAdjacentElement("afterend",d[0])}S(),R.popups.hide("table.edit")}},addHeader:function me(){var e=se();if(0<e.length&&0===e.find("> th").length){var t,a="<thead><tr>",l=0;for(l=e.find("tr").first().find("> th").length,e.find("tr").first().find("> td").each(function(){var e=x(this);l+=parseInt(e.attr("colspan"),10)||1}),t=0;t<l;t++)a+="<th><br></th>";a+="</tr></thead>",e.prepend(a),u()}},addFooter:function ve(){var e=se();if(0<e.length&&0===e.find("tfoot").length){var t="<tfoot><tr>",a=0,l=e.find("thead tr").first();0===l.length&&(l=e.find("tbody tr").first()),l.find("th, td").each(function(){a+=parseInt(x(this).attr("colspan"),10)||1});for(var n=0;n<a;n++)t+="<td><br></td>";t+="</tr></tfoot>",e.append(t),u()}},removeHeader:function Ce(){var e=se(),t=e.find("> thead");if(0<(t=x(t)).length)if(0===e.find("tbody tr").length)m();else if(t.remove(),0<oe().length)u();else{R.popups.hide("table.edit");var a=e.find("tbody tr").first().find("td").first().get(0);a&&(R.selection.setAtEnd(a),R.selection.restore())}},removeFooter:function we(){var e=se(),t=e.find("tfoot");if(0<t.length)if(0===e.find("tbody tr").length)m();else if(t.remove(),0<oe().length)u();else{R.popups.hide("table.edit");var a=e.find("tbody tr").first().find("td").first().get(0);a&&(R.selection.setAtEnd(a),R.selection.restore())}},setBackground:t,showInsertPopup:function Ee(){var e=R.$tb.find('.fr-command[data-cmd="insertTable"]'),t=R.popups.get("table.insert");if(t||(t=d()),!t.hasClass("fr-active")){R.popups.refresh("table.insert"),R.popups.setContainer("table.insert",R.$tb);var a=R.button.getPosition(e),l=a.left,n=a.top;R.popups.show("table.insert",l,n,e.outerHeight())}},showEditPopup:u,showColorsPopup:c,back:function ye(){0<oe().length?u():(R.popups.hide("table.insert"),R.toolbar.showInline())},verticalAlign:function Te(e){R.$el.find(".fr-selected-cell").css("vertical-align",e)},horizontalAlign:function Re(e){R.$el.find(".fr-selected-cell").css("text-align",e)},applyStyle:function Oe(e,t,a,l){if(0<t.length){if(R.opts.useClasses||("TABLE"===t.get(0).tagName?function r(e,t){var a=e.childNodes;if(0<a.length)for(var l=0;l<a.length;l++)"TD"===a[l].tagName?"fr-dashed-borders"===t||"fr-highlighted"===t||"fr-thick"===t?A(a[l]):"fr-alternate-rows"===t&&a[l].style.removeProperty("background-color"):r(a[l],t)}(t.get(0),e):A(t.get(0))),!a){var n=Object.keys(l);n.splice(n.indexOf(e),1),t.removeClass(n.join(" "))}t.toggleClass(e)}},selectedTable:se,selectedCells:oe,customColor:function xe(){var e=R.popups.get("table.colors").find(".fr-table-colors-hex-layer input");e.length&&t(e.val())},selectCells:K}},Ae.DefineIcon("insertTable",{NAME:"table",SVG_KEY:"insertTable"}),Ae.RegisterCommand("insertTable",{title:"Insert Table",undo:!1,focus:!0,refreshOnCallback:!1,popup:!0,callback:function(){this.popups.isVisible("table.insert")?(this.$el.find(".fr-marker").length&&(this.events.disableBlur(),this.selection.restore()),this.popups.hide("table.insert")):this.table.showInsertPopup()},plugin:"table"}),Ae.RegisterCommand("tableInsert",{callback:function(e,t,a){this.table.insert(t,a),this.popups.hide("table.insert")}}),Ae.DefineIcon("tableHeader",{NAME:"header",FA5NAME:"heading",SVG_KEY:"tableHeader"}),Ae.RegisterCommand("tableHeader",{title:"Table Header",focus:!1,toggle:!0,callback:function(){this.popups.get("table.edit").find('.fr-command[data-cmd="tableHeader"]').hasClass("fr-active")?this.table.removeHeader():this.table.addHeader()},refresh:function(e){var t=this.table.selectedTable();0<t.length&&(0===t.find("> thead").length?e.removeClass("fr-active").attr("aria-pressed",!1):e.addClass("fr-active").attr("aria-pressed",!0))}}),Ae.DefineIcon("tableFooter",{NAME:"table",FA5NAME:"table",SVG_KEY:"tableFooter"}),Ae.RegisterCommand("tableFooter",{title:"Table Footer",focus:!1,toggle:!0,callback:function(){this.popups.get("table.edit").find('.fr-command[data-cmd="tableFooter"]').hasClass("fr-active")?this.table.removeFooter():this.table.addFooter()},refresh:function(e){var t=this.table.selectedTable();0<t.length&&(0===t.find("tfoot").length?e.removeClass("fr-active").attr("aria-pressed",!1):e.addClass("fr-active").attr("aria-pressed",!0))}}),Ae.DefineIcon("tableRows",{NAME:"bars",SVG_KEY:"row"}),Ae.RegisterCommand("tableRows",{type:"dropdown",focus:!1,title:"Row",options:{above:"Insert row above",below:"Insert row below","delete":"Delete row"},html:function(){var e='<ul class="fr-dropdown-list" role="presentation">',t=Ae.COMMANDS.tableRows.options;for(var a in t)t.hasOwnProperty(a)&&(e+='<li role="presentation"><a class="fr-command" tabIndex="-1" role="option" data-cmd="tableRows" data-param1="'+a+'" title="'+this.language.translate(t[a])+'">'+this.language.translate(t[a])+"</a></li>");return e+="</ul>"},callback:function(e,t){"above"==t||"below"==t?this.table.insertRow(t):this.table.deleteRow()}}),Ae.DefineIcon("tableColumns",{NAME:"bars fa-rotate-90",SVG_KEY:"columns"}),Ae.RegisterCommand("tableColumns",{type:"dropdown",focus:!1,title:"Column",options:{before:"Insert column before",after:"Insert column after","delete":"Delete column"},html:function(){var e='<ul class="fr-dropdown-list" role="presentation">',t=Ae.COMMANDS.tableColumns.options;for(var a in t)t.hasOwnProperty(a)&&(e+='<li role="presentation"><a class="fr-command" tabIndex="-1" role="option" data-cmd="tableColumns" data-param1="'.concat(a,'" title="').concat(this.language.translate(t[a]),'">').concat(this.language.translate(t[a]),"</a></li>"));return e+="</ul>"},callback:function(e,t){"before"==t||"after"==t?this.table.insertColumn(t):this.table.deleteColumn()}}),Ae.DefineIcon("tableCells",{NAME:"square-o",FA5NAME:"square",SVG_KEY:"cellOptions"}),Ae.RegisterCommand("tableCells",{type:"dropdown",focus:!1,title:"Cell",options:{merge:"Merge cells","vertical-split":"Vertical split","horizontal-split":"Horizontal split"},html:function(){var e='<ul class="fr-dropdown-list" role="presentation">',t=Ae.COMMANDS.tableCells.options;for(var a in t)t.hasOwnProperty(a)&&(e+='<li role="presentation"><a class="fr-command" tabIndex="-1" role="option" data-cmd="tableCells" data-param1="'.concat(a,'" title="').concat(this.language.translate(t[a]),'">').concat(this.language.translate(t[a]),"</a></li>"));return e+="</ul>"},callback:function(e,t){"merge"==t?this.table.mergeCells():"vertical-split"==t?this.table.splitCellVertically():this.table.splitCellHorizontally()},refreshOnShow:function(e,t){1<this.$el.find(".fr-selected-cell").length?(t.find('a[data-param1="vertical-split"]').addClass("fr-disabled").attr("aria-disabled",!0),t.find('a[data-param1="horizontal-split"]').addClass("fr-disabled").attr("aria-disabled",!0),t.find('a[data-param1="merge"]').removeClass("fr-disabled").attr("aria-disabled",!1)):(t.find('a[data-param1="merge"]').addClass("fr-disabled").attr("aria-disabled",!0),t.find('a[data-param1="vertical-split"]').removeClass("fr-disabled").attr("aria-disabled",!1),t.find('a[data-param1="horizontal-split"]').removeClass("fr-disabled").attr("aria-disabled",!1))}}),Ae.DefineIcon("tableRemove",{NAME:"trash",SVG_KEY:"removeTable"}),Ae.RegisterCommand("tableRemove",{title:"Remove Table",focus:!1,callback:function(){this.table.remove()}}),Ae.DefineIcon("tableStyle",{NAME:"paint-brush",SVG_KEY:"tableStyle"}),Ae.RegisterCommand("tableStyle",{title:"Table Style",type:"dropdown",focus:!1,html:function(){var e='<ul class="fr-dropdown-list" role="presentation">',t=this.opts.tableStyles;for(var a in t)t.hasOwnProperty(a)&&(e+='<li role="presentation"><a class="fr-command" tabIndex="-1" role="option" data-cmd="tableStyle" data-param1="'.concat(a,'" title="').concat(this.language.translate(t[a]),'">').concat(this.language.translate(t[a]),"</a></li>"));return e+="</ul>"},callback:function(e,t){this.table.applyStyle(t,this.$el.find(".fr-selected-cell").closest("table"),this.opts.tableMultipleStyles,this.opts.tableStyles)},refreshOnShow:function(e,t){var a=this.$,l=this.$el.find(".fr-selected-cell").closest("table");l&&t.find(".fr-command").each(function(){var e=a(this).data("param1"),t=l.hasClass(e);a(this).toggleClass("fr-active",t).attr("aria-selected",t)})}}),Ae.DefineIcon("tableCellBackground",{NAME:"tint",SVG_KEY:"cellBackground"}),Ae.RegisterCommand("tableCellBackground",{title:"Cell Background",focus:!1,popup:!0,callback:function(){this.table.showColorsPopup()}}),Ae.RegisterCommand("tableCellBackgroundColor",{undo:!0,focus:!1,callback:function(e,t){this.table.setBackground(t)}}),Ae.DefineIcon("tableBack",{NAME:"arrow-left",SVG_KEY:"back"}),Ae.RegisterCommand("tableBack",{title:"Back",undo:!1,focus:!1,back:!0,callback:function(){this.table.back()},refresh:function(e){0!==this.table.selectedCells().length||this.opts.toolbarInline?(e.removeClass("fr-hidden"),e.next(".fr-separator").removeClass("fr-hidden")):(e.addClass("fr-hidden"),e.next(".fr-separator").addClass("fr-hidden"))}}),Ae.DefineIcon("tableCellVerticalAlign",{NAME:"arrows-v",FA5NAME:"arrows-alt-v",SVG_KEY:"verticalAlignMiddle"}),Ae.RegisterCommand("tableCellVerticalAlign",{type:"dropdown",focus:!1,title:"Vertical Align",options:{Top:"Align Top",Middle:"Align Middle",Bottom:"Align Bottom"},html:function(){var e='<ul class="fr-dropdown-list" role="presentation">',t=Ae.COMMANDS.tableCellVerticalAlign.options;for(var a in t)t.hasOwnProperty(a)&&(e+='<li role="presentation"><a class="fr-command" tabIndex="-1" role="option" data-cmd="tableCellVerticalAlign" data-param1="'.concat(a.toLowerCase(),'" title="').concat(this.language.translate(t[a]),'">').concat(this.language.translate(a),"</a></li>"));return e+="</ul>"},callback:function(e,t){this.table.verticalAlign(t)},refreshOnShow:function(e,t){t.find('.fr-command[data-param1="'+this.$el.find(".fr-selected-cell").css("vertical-align")+'"]').addClass("fr-active").attr("aria-selected",!0)}}),Ae.DefineIcon("tableCellHorizontalAlign",{NAME:"align-left",SVG_KEY:"alignLeft"}),Ae.DefineIcon("align-left",{NAME:"align-left",SVG_KEY:"alignLeft"}),Ae.DefineIcon("align-right",{NAME:"align-right",SVG_KEY:"alignRight"}),Ae.DefineIcon("align-center",{NAME:"align-center",SVG_KEY:"alignCenter"}),Ae.DefineIcon("align-justify",{NAME:"align-justify",SVG_KEY:"alignJustify"}),Ae.RegisterCommand("tableCellHorizontalAlign",{type:"dropdown",focus:!1,title:"Horizontal Align",options:{left:"Align Left",center:"Align Center",right:"Align Right",justify:"Align Justify"},html:function(){var e='<ul class="fr-dropdown-list" role="presentation">',t=Ae.COMMANDS.tableCellHorizontalAlign.options;for(var a in t)t.hasOwnProperty(a)&&(e+='<li role="presentation"><a class="fr-command fr-title" tabIndex="-1" role="option" data-cmd="tableCellHorizontalAlign" data-param1="'.concat(a,'" title="').concat(this.language.translate(t[a]),'">').concat(this.icon.create("align-".concat(a)),'<span class="fr-sr-only">').concat(this.language.translate(t[a]),"</span></a></li>"));return e+="</ul>"},callback:function(e,t){this.table.horizontalAlign(t)},refresh:function(e){var t=this.table.selectedCells(),a=this.$;t.length&&e.find("> *").first().replaceWith(this.icon.create("align-".concat(this.helpers.getAlignment(a(t[0])))))},refreshOnShow:function(e,t){t.find('.fr-command[data-param1="'+this.helpers.getAlignment(this.$el.find(".fr-selected-cell").first())+'"]').addClass("fr-active").attr("aria-selected",!0)}}),Ae.DefineIcon("tableCellStyle",{NAME:"magic",SVG_KEY:"cellStyle"}),Ae.RegisterCommand("tableCellStyle",{title:"Cell Style",type:"dropdown",focus:!1,html:function(){var e='<ul class="fr-dropdown-list" role="presentation">',t=this.opts.tableCellStyles;for(var a in t)t.hasOwnProperty(a)&&(e+='<li role="presentation"><a class="fr-command" tabIndex="-1" role="option" data-cmd="tableCellStyle" data-param1="'.concat(a,'" title="').concat(this.language.translate(t[a]),'">').concat(this.language.translate(t[a]),"</a></li>"));return e+="</ul>"},callback:function(e,t){this.table.applyStyle(t,this.$el.find(".fr-selected-cell"),this.opts.tableCellMultipleStyles,this.opts.tableCellStyles)},refreshOnShow:function(e,t){var a=this.$,l=this.$el.find(".fr-selected-cell").first();l&&t.find(".fr-command").each(function(){var e=a(this).data("param1"),t=l.hasClass(e);a(this).toggleClass("fr-active",t).attr("aria-selected",t)})}}),Ae.RegisterCommand("tableCellBackgroundCustomColor",{title:"OK",undo:!0,callback:function(){this.table.customColor()}}),Ae.DefineIcon("tableColorRemove",{NAME:"eraser",SVG_KEY:"remove"})});