/*!
 * froala_editor v4.5.0 (https://www.froala.com/wysiwyg-editor)
 * License https://froala.com/wysiwyg-editor/terms/
 * Copyright 2014-2025 Froala Labs
 */

!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(require("froala-editor")):"function"==typeof define&&define.amd?define(["froala-editor"],t):t(e.FroalaEditor)}(this,function(e){"use strict";(e=e&&e.hasOwnProperty("default")?e["default"]:e).PLUGINS.trimVideoPlugin=function(c){var o,d,s,l,u,m,f,v,r;function p(e){e.preventDefault(),null!=l&&l.abort(),l=null;var t=document.getElementById("videoTrimContainer");t.parentNode.removeChild(t),c.filesManager.setChildWindowState(!1)}function g(e){var t=parseInt(e,10),n=Math.floor(t/3600)?String(Math.floor(t/3600)):"00",a=Math.floor(t/60)%60?String(Math.floor(t/60)%60):"00",i=t%60?String(t%60):"00";return i=i.length<2?"0"+i:i,a=a.length<2?"0"+a:a,(n=n.length<2?"0"+n:n)+":"+a+":"+i}function y(e){e.preventDefault();var t=g(document.getElementById("startTime").value),n=g(document.getElementById("endTime").value);o.constructor===Blob&&(o=new File([o],o.name,{type:o.type||"",lastModified:o.lastModified}));var a=new FormData;a.append("startTime",t),a.append("endTime",n),a.append("file",o);var i=new XMLHttpRequest;document.getElementById("trim-file-loader").classList.add("fr-file-loader"),document.getElementsByClassName("fr-trim-button")[0].style.display="none",i.onload=function(){if(200==this.status){var e=new Blob([this.response],{type:this.response.type||""});e.name=o.name,e.lastModified=o.lastModified,e.lastModifiedDate=o.lastModifiedDate,r.set(d,e),c.filesManager.upload(e,[],null,d),document.getElementById("trim-file-loader").classList.remove("fr-file-loader"),document.getElementsByClassName("fr-trim-button")[0].style.display="block";var t=document.getElementById("videoTrimContainer");t.parentNode.removeChild(t),c.filesManager.setChildWindowState(!1)}},i.open("POST","http://localhost:3000/convert",!0),i.responseType="blob",(l=i).send(a)}return{_init:function e(){},trimVideo:function i(e,t,n){o=e,d=t,r=n,function a(){var e=URL.createObjectURL(o),t=c.o_doc.body,n=c.o_doc.createElement("div");n.setAttribute("id","videoTrimContainer"),n.style.cssText="position: fixed; top: 0;left: 0;padding: 0;overflow-y:auto;width: 100%;height: 100%;background: rgba(0,0,0,0.4);z-index: 9998;display:block",t.appendChild(n);var r=document.createElement("div");r.setAttribute("id","fr-form-container"),r.innerHTML='\n    <h3 class="fr-trim-video-name"> '.concat(o.name.replace(/\.[^.]*$/,""),"</h3>\n    <div style='display:block;text-align: center; margin-left:50%;' id='trim-file-loader'></div>\n      <video id='fr-video-edit'  controls>\n       <source src=").concat(e," >\n        Your browser does not support the video tag.\n    </video> \n  "),document.getElementById("videoTrimContainer").appendChild(r),document.getElementById("fr-video-edit").addEventListener("loadedmetadata",function(){s=document.getElementById("fr-video-edit").duration,r.innerHTML+=' \n        \n        <form>\n        <center>\n         <section class="fr-range-slider ">\n          <div id="startTimeValue" class=" fr-range-value-start"> </div>\n            <input type="range"  class="fr-slider" value=\'0\' min="0" max='.concat(s,' id=\'startTime\' >\n          <div  id="endTimeValue" class="fr-range-value-end" ></div>\n            <input type="range"class="fr-slider" value=').concat(s," min='0' max=").concat(s," id='endTime'>\n         <div id=\"selectedRange\"style=\" pointer-events: none; position: absolute;left: 0;top: 12px;width: 100%;\n         outline: none;\n         height: 6px;\n         border-radius: 10px;\"></div>\n            </section>\n        </center>\n        <div class=\"fr-video-trim-buttons\" >\n          <button id='convert' class='fr-trim-button'>").concat(c.language.translate("Trim"),"</button>               \n          <button id='cancel' class='fr-trim-button' onsubmit='cancel()'>").concat(c.language.translate("Cancel"),"</button>\n        </div>\n    </form>\n  ");var t=document.getElementById("startTime"),e=document.getElementById("startTimeValue"),n=function n(){u=Number(100*(t.value-t.min)/(t.max-t.min)),m=10-.2*u,f=Number(100*(a.value-a.min)/(a.max-a.min)),v=10-.2*f,e.innerHTML="<span>".concat(g(t.value),"</span>"),e.style.left="calc(".concat(u,"% + (").concat(m,"px))"),selectedRange.style.left=e.style.left,selectedRange.style.width="calc((".concat(f,"% + (").concat(v,"px)) - (").concat(u,"% + (").concat(m,"px)))"),selectedRange.style.backgroundColor="#03A9F4"};document.addEventListener("DOMContentLoaded",n);var a=document.getElementById("endTime"),i=document.getElementById("endTimeValue"),o=function o(){u=Number(100*(t.value-t.min)/(t.max-t.min)),m=10-.2*u,f=Number(100*(a.value-a.min)/(a.max-a.min)),v=10-.2*f,i.innerHTML="<span>".concat(g(a.value),"</span>"),i.style.left="calc(".concat(f,"% + (").concat(v,"px))");var e=document.getElementById("selectedRange");e.style.left="calc(".concat(u,"% + (").concat(m,"px))"),e.style.width="calc((".concat(f,"% + (").concat(v,"px)) - (").concat(u,"% + (").concat(m,"px)))"),e.style.backgroundColor="#03A9F4"};document.addEventListener("DOMContentLoaded",o),document.getElementById("convert").addEventListener("click",y),document.getElementById("cancel").addEventListener("click",p);var d=document.getElementById("startTime"),l=document.getElementById("endTime");d.oninput=function(e){if(Number(d.value)>=Number(l.value))return e.preventDefault(),d.value=String(Number(l.value)-1),!1;n()},l.oninput=function(e){if(Number(l.value)<=Number(d.value))return e.preventDefault(),l.value=String(Number(d.value)+1),!1;o()}})}()}}}});