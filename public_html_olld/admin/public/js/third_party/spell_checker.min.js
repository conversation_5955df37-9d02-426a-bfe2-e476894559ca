/*!
 * froala_editor v4.5.0 (https://www.froala.com/wysiwyg-editor)
 * License https://froala.com/wysiwyg-editor/terms/
 * Copyright 2014-2025 Froala Labs
 */

!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(require("froala-editor")):"function"==typeof define&&define.amd?define(["froala-editor"],t):t(e.FroalaEditor)}(this,function(p){"use strict";(p=p&&p.hasOwnProperty("default")?p["default"]:p).DEFAULT_SCAYT_OPTIONS={enableOnTouchDevices:!1,disableOptionsStorage:["all"],localization:"en",extraModules:"ui",DefaultSelection:"American English",spellcheckLang:"en_US",contextMenuSections:"suggest|moresuggest",serviceProtocol:"https",servicePort:"80",serviceHost:"svc.webspellchecker.net",servicePath:"spellcheck/script/ssrv.cgi",contextMenuForMisspelledOnly:!0,scriptPath:"https://svc.webspellchecker.net/spellcheck31/wscbundle/wscbundle.js"},Object.assign(p.DEFAULTS,{scaytAutoload:!1,scaytCustomerId:"1:ldogw1-MSDuT3-slyfO-0YJgB1-Wx7262-HIT741-MAMDv4-10qfb3-A4LDP-c60m3-hSQgd2-az2",scaytOptions:{}}),p.PLUGINS.spellChecker=function(s){var l;function e(e){if(l&&l.isDisabled){var t=!l.isDisabled();e.toggleClass("fr-active",t).attr("aria-pressed",t),s.$el.attr("spellcheck",s.opts.spellcheck&&!t)}}function t(e){l&&l.isDisabled&&!l.isDisabled()&&0<=["bold","italic","underline","strikeThrough","subscript","superscript","fontFamily","fontSize","html"].indexOf(e)&&l.removeMarkupInSelectionNode({removeInside:!0})}function o(e){l&&l.isDisabled&&!l.isDisabled()&&0<=["bold","italic","underline","strikeThrough","subscript","superscript","fontFamily","fontSize","html"].indexOf(e)&&l.reloadMarkup()}function a(e){l&&l.isDisabled&&!l.isDisabled()&&(e.which==p.KEYCODE.ENTER&&setTimeout(l.reloadMarkup,0))}function i(e){if(e&&e.getAttribute&&e.getAttribute("data-scayt-word"))e.outerHTML=e.innerHTML;else if(e&&e.nodeType==Node.ELEMENT_NODE)for(var t=e.querySelectorAll("[data-scayt-word], [data-spelling-word]"),s=0;s<t.length;s++)t[s].outerHTML=t[s].innerHTML}function r(){s.events.on("commands.before",t),s.events.on("commands.after",o),s.events.on("keydown",a,!0),s.events.on("html.processGet",i),e(s.$tb.find('[data-cmd="spellChecker"]'))}function n(){var e=s.opts.scaytOptions;e.customerId=s.opts.scaytCustomerId,e.container=s.$iframe?s.$iframe.get(0):s.$el.get(0),e.autoStartup=s.opts.scaytAutoload,e.onLoad=r,null!==s.opts.language&&(s.opts.spellCheckerLanguage=s.opts.language),!0===s.opts.scaytAutoload&&(s.opts.spellcheck=!1),l=new SCAYT.CUSTOMSCAYT(e)}return{_init:function c(){if(!s.$wp)return!1;if(s.opts.scaytOptions=Object.assign({},p.DEFAULT_SCAYT_OPTIONS,s.opts.scaytOptions),"undefined"!=typeof SCAYT)n();else if(s.shared.spellCheckerLoaded||(s.shared.spellCheckerCallbacks=[]),s.shared.spellCheckerCallbacks.push(n),!s.shared.spellCheckerLoaded){s.shared.spellCheckerLoaded=!0;var e=document.createElement("script");e.type="text/javascript",e.src=s.opts.scaytOptions.scriptPath,e.innerText="",e.onload=function(){for(var e=0;e<s.shared.spellCheckerCallbacks.length;e++)s.shared.spellCheckerCallbacks[e]()},document.getElementsByTagName("head")[0].appendChild(e)}},refresh:e,toggle:function d(){l&&l.isDisabled&&l.setDisabled(!l.isDisabled())}}},p.DefineIcon("spellChecker",{NAME:"keyboard-o",FA5NAME:"keyboard",SVG_KEY:"spellcheck"}),p.RegisterCommand("spellChecker",{title:"Spell Checker",undo:!1,focus:!1,accessibilityFocus:!0,forcedRefresh:!0,toggle:!0,callback:function(){this.spellChecker.toggle()},refresh:function(e){this.spellChecker.refresh(e)},plugin:"spellChecker"})});