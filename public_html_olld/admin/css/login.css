/* General Styles */
body {
    font-family: 'Arial', sans-serif;
    margin: 0;
    padding: 10px;
    background-color: #222a31;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
}

.login-container {
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 400px;
    text-align: center;
}

h2 {
    color: #333;
    font-size: 24px;
    margin-bottom: 20px;
}

/* Error Message Styling */
.error-message {
    color: #e74c3c;
    background-color: #f8d7da;
    padding: 10px;
    border-radius: 5px;
    margin-bottom: 20px;
    font-size: 16px;
    border: 1px solid #f5c6cb;
}

/* Input Group */
.input-group {
    margin-bottom: 15px;
    text-align: left;
}

.input-group label {
    font-size: 14px;
    color: #666;
}

.input-group input {
    width: 93%;
    padding: 12px;
    margin-top: 5px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 16px;
}

.input-group input:focus {
    border-color: #CDA565;
    outline: none;
}

/* Login Button */
.login-btn {
    width: 100%;
    padding: 12px;
    background-color: #CDA565;
    color: #fff;
    border: none;
    border-radius: 5px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.login-btn:hover {
    background-color: #b68e49;
}

/* Extra Options (Forgot Password & Sign Up) */
.extra-options {
    margin-top: 15px;
}

.extra-options a {
    font-size: 14px;
    color: #888;
    text-decoration: none;
    margin: 0 10px;
}

.extra-options a:hover {
    color: #CDA565;
}

/* Media Queries for Responsiveness */
@media (max-width: 768px) {
    .login-container {
        width: 90%;
    }
}
