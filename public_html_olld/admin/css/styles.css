/* General styles */
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    background-color: #f3f1f5;
}

a {
    text-decoration: none;
    color: #fff;
}

/* Sidebar Styles */
.sidebar {
    background-color: #1a2025;
    color: #fff;
    width: 250px;
    height: 100vh;
    padding-top: 30px;
    position: fixed;
    transition: transform 0.3s ease-in-out;
}

.sidebar .logo {
    text-align: center;
    margin-bottom: 20px;
}

.sidebar h2 {
    color: #CDA565;
}

.sidebar ul {
    list-style-type: none;
    padding: 0;
}

.sidebar ul li {
    padding: 15px;
    border-bottom: 1px solid #444;
}

.sidebar ul li:hover {
    background-color: #CDA565;
}

.sidebar ul li a {
    color: #fff;
    font-size: 18px;
}

/* Sidebar toggle (hidden on mobile) */
.sidebar.hidden {
    transform: translateX(-100%);
}

/* Main content area */
.main-content {
    margin-left: 250px;
    padding: 0px;
    transition: margin-left 0.3s ease-in-out;
}

.navbar {
    background-color: #222a31;
    padding: 20px;
    color: #CDA565;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.content {
    margin: 20px;
}

/* Dashboard */
.dashboard {
    display: flex;
    justify-content: space-around;
}

.dashboard .card {
    background-color: #222a31;
    color: #fff;
    border-radius: 8px;
    padding: 20px;
    margin:10px;
    width: 30%;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.dashboard .card h3 {
    margin-bottom: 10px;
}

/* Forms */
form {
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

form input, form textarea, form select {
    width: 100%;
    padding: 10px;
    margin: 10px 0;
    border: 1px solid #ddd;
    border-radius: 5px;
}

form button {
    background-color: #CDA565;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

form button:hover {
    background-color: #b68e49;
}

/* Media Queries for responsiveness */
@media (max-width: 768px) {
    /* Sidebar */
    .sidebar {
        width: 100%;
        height: 100vh;
        position: absolute;
        transform: translateX(-100%);
    }

    .sidebar.active {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    /* Navbar (Hamburger Icon) */
    .navbar {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .menu-icon {
        display: block;
        cursor: pointer; /* Add pointer cursor to hamburger menu icon */
        font-size: 30px; /* Adjust size if needed */
    }

    /* Ensure pointer cursor for the span inside the hamburger icon */
    .menu-icon span {
        cursor: pointer; /* Pointer cursor for span element */
    }

    /* Adjust dashboard layout */
    .dashboard {
        flex-direction: column;
        align-items: center;
    }

    .dashboard .card {
        width: 80%;
        margin-bottom: 20px;
    }

    /* For active Sidebar */
    .sidebar.active {
        transform: translateX(0);
    }
}

/* Add pointer cursor to sidebar links */
.sidebar ul li a {
    cursor: pointer;
}

.menu-icon {
    display: none;
    @media (max-width: 768px) {
        display: block;
    }
}


/* Users Table */
.user-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.user-table th, .user-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
    color: #333;
}

.user-table th {
    background-color: #222a31;
    color: #CDA565;
    font-weight: bold;
}

.user-table tr:hover {
    background-color: #f1f1f1;
}

.user-table td a {
    color: #CDA565;
    padding: 5px 10px;
    margin: 0 5px;
    text-decoration: none;
    border-radius: 5px;
    background-color: #fff;
    border: 1px solid #CDA565;
}

.user-table td a:hover {
    background-color: #CDA565;
    color: #fff;
}

/* Styling the form inside the table (for changing role) */
.user-table form {
    display: flex;
    align-items: center;
}

.user-table select {
    padding: 8px 12px;
    margin-right: 10px;
    border-radius: 5px;
    border: 1px solid #CDA565;
    background-color: #f9f9f9;
    font-size: 14px;
    color: #333;
    transition: background-color 0.3s ease;
}

.user-table select:focus {
    background-color: #CDA565;
    color: white;
    border-color: #CDA565;
    outline: none;
}

.user-table button {
    background-color: #CDA565;
    color: white;
    padding: 8px 16px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s ease;
}

.user-table button:hover {
    background-color: #b68e49;
}

/* Table Responsiveness */
@media (max-width: 768px) {
    .user-table th, .user-table td {
        padding: 8px;
    }

    .user-table {
        font-size: 14px;
    }
}


/* Add New Post Button - Align to the right */
.add-post-btn {
    background-color: #CDA565;
    color: white;
    padding: 10px 20px;
    border-radius: 5px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    margin-top: 20px;
    float: right; /* Aligns to the right */
    font-size: 16px;
    transition: background-color 0.3s ease;
}

.add-post-btn:hover {
    background-color: #b68e49;
}


/* Styling the Blog Header Section */
.blog-header {
    display: flex;
    justify-content: space-between; /* Distribute the elements to the sides */
    align-items: center; /* Vertically align the elements */
    margin-bottom: 20px;
}

.blog-header h3 {
    margin: 0; /* Remove the default margin */
}

.add-post-btn {
    background-color: #CDA565;
    color: white;
    padding: 10px 20px;
    border-radius: 5px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 16px;
    transition: background-color 0.3s ease;
}

.add-post-btn:hover {
    background-color: #b68e49;
}


/* Form styles for adding new blog post */
form {
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    margin: 20px auto;
}

form label {
    display: block;
    font-size: 16px;
    margin-bottom: 5px;
}

form input, form textarea {
    width: 98%;
    padding: 12px;
    margin-bottom: 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
}

form textarea {
    height: 150px;
}

form button {
    background-color: #CDA565;
    color: white;
    padding: 10px 20px;
    border-radius: 5px;
    border: none;
    cursor: pointer;
}

form button:hover {
    background-color: #b68e49;
}


/* Pagination */
.pagination {
    text-align: center;
    margin-top: 20px;
}

.pagination a {
    padding: 10px 15px;
    margin: 0 5px;
    background-color: #CDA565;
    color: #fff;
    border-radius: 5px;
    text-decoration: none;
}

.pagination a.active {
    background-color: #b68e49;
}

.pagination a:hover {
    background-color: #b68e49;
}

.pagination a:first-child,
.pagination a:last-child {
    font-weight: bold;
}


/* For Tables on Mobile */
@media (max-width: 768px) {
    /* Hide table header and use divs for each row */
    .user-table {
        display: block;
        width: 100%;
        overflow-x: auto;
    }

    .user-table thead {
        display: none; /* Hide the headers on small screens */
    }

    .user-table tr {
        display: block;
        border-bottom: 1px solid #ddd;
        margin-bottom: 10px;
    }

    .user-table td {
        display: block;
        text-align: left; /* Align text to left for better readability */
        font-size: 14px;
        padding: 10px;
        border-bottom: 1px solid #ddd;
    }

    .user-table td::before {
        /* Add pseudo-element to mimic table headers */
        content: attr(data-label);
        font-weight: bold;
        text-transform: uppercase;
        margin-right: 10px;
        color: #CDA565;
        display: inline-block;
    }

    /* Remove the left padding and margin */
    .user-table td {
        background-color: #fff;
        border-radius: 5px;
        margin-bottom: 5px;
        padding-left: 0; /* Remove padding-left */
    }
}


