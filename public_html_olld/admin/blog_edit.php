<?php
session_start();
include '../includes/config.php';
include 'connect.php';
require '../vendor/autoload.php';

use s9e\TextFormatter\Bundles\Forum as TextFormatter;

// Handle Froala Editor image upload
if (isset($_FILES['file']) && $_FILES['file']['error'] === UPLOAD_ERR_OK) {
    $uploadDir = '../uploads/';
    $fileName = basename($_FILES['file']['name']);
    $uploadFilePath = $uploadDir . $fileName;

    if (move_uploaded_file($_FILES['file']['tmp_name'], $uploadFilePath)) {
        $response = [
            'link' => '../uploads/' . $fileName
        ];
        echo json_encode($response);
        exit;
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to move uploaded file.']);
        exit;
    }
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $id = $_POST['id'];
    $title = $_POST['title'];
    $content = $_POST['content'];
    $description = $_POST['short_description'];
    $category = $_POST['category'];
    $url = $_POST['url'];
    $seo_tags = $_POST['seo_tags'];
    $image = $_FILES['image']['name'];

    if (empty($title) || empty($content) || empty($description) || empty($category)) {
        $error = "All fields are required!";
    } else {
        $image_path = '../uploads/' . basename($image);
        if (move_uploaded_file($_FILES['image']['tmp_name'], $image_path)) {
            $xml = TextFormatter::parse($content);
            $htmlContent = TextFormatter::render($xml);
            $htmlContent = htmlspecialchars_decode($htmlContent);

            $sql = "UPDATE blog_posts SET 
                    title = :title, 
                    content = :content, 
                    short_description = :short_description, 
                    image = :image, 
                    category = :category, 
                    url = :url, 
                    seo_tags = :seo_tags 
                    WHERE id = :id";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([
                ':title' => $title,
                ':content' => $htmlContent,
                ':short_description' => $description,
                ':image' => '../uploads/' . basename($image),
                ':category' => $category,
                ':url' => $url,
                ':seo_tags' => $seo_tags,
                ':id' => $id
            ]);

            header("Location: blog.php");
            exit;
        } else {
            $error = "Failed to upload image.";
        }
    }
}

// Fetch the blog post to edit
if (isset($_GET['id'])) {
    $id = $_GET['id'];
    $sql = "SELECT * FROM blog_posts WHERE id = :id";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([':id' => $id]);
    $post = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$post) {
        header("Location: blog.php");
        exit;
    }
} else {
    header("Location: blog.php");
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Blog Post</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="public/css/froala_editor.pkgd.min.css">
    <link rel="stylesheet" href="public/css/froala_style.min.css">
    <style>
        div[style=""] {
            display: none !important;
            visibility: hidden !important;
            font-size: 0 !important;
            color: transparent !important;
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>
    <div class="main-content">
        <div class="navbar">
            <h2>Edit Blog Post</h2>
            <div class="menu-icon">
                <span>&#9776;</span>
            </div>
        </div>

        <div class="content">
            <h3>Edit Blog Post</h3>
            <?php if (isset($error)): ?>
                <div class="error-message">
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>

            <form method="POST" enctype="multipart/form-data">
                <input type="hidden" name="id" value="<?php echo $post['id']; ?>">
                <label for="title">Title</label>
                <input type="text" name="title" id="title" placeholder="Enter the title" value="<?php echo htmlspecialchars($post['title']); ?>" required>

                <label for="url">Custom URL</label>
                <input type="text" name="url" id="url" placeholder="Enter the custom URL" value="<?php echo htmlspecialchars($post['url']); ?>" required>

                <label for="category">Category</label>
                <input type="text" name="category" id="category" placeholder="Enter the category" value="<?php echo isset($post['category']) ? htmlspecialchars($post['category']) : ''; ?>" required>

                <label for="short_description">Short Description</label>
                <textarea name="short_description" id="short_description" placeholder="Enter a short description" required><?php echo htmlspecialchars($post['short_description']); ?></textarea>

                <label for="content">Content</label>
                <textarea name="content" id="content" placeholder="Enter the content"><?php echo htmlspecialchars($post['content']); ?></textarea>
                <br>
                <label for="seo_tags">SEO Tags</label>
                <textarea name="seo_tags" id="seo_tags" placeholder="Enter SEO tags (comma-separated)"><?php echo htmlspecialchars($post['seo_tags']); ?></textarea>

                <label for="image">Image</label>
                <input type="file" name="image" id="image" accept="image/*">
                <div id="image-preview" style="margin-top: 10px; display: none;">
                    <img id="preview" src="#" alt="Image Preview" style="max-width: 200px; max-height: 200px;">
                </div>

                <button type="submit" class="btn">Update Post</button>
            </form>
        </div>
    </div>

    <!-- Froala JS -->
    <script src="public/js/fix-editor.js"></script>
    <script src="public/js/fix-editor.min.js"></script>
    <script src="js/script.js"></script>
</body>
</html>