<?php
session_start();
require '../includes/config.php';  
require 'connect.php';

// აქ გამოთვლილია გვერდი (თუ არ არსებობს, იქნება პირველი გვერდი)
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$perPage = 25; 
$offset = ($page - 1) * $perPage;

// SQL მოთხოვნა ყველა შეკვეთისთვის (გვერდის მიხედვით)
$sql = "SELECT * FROM orders ORDER BY created_at DESC LIMIT :limit OFFSET :offset";
$stmt = $pdo->prepare($sql);
$stmt->bindValue(':limit', $perPage, PDO::PARAM_INT);
$stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
$stmt->execute();
$orders = $stmt->fetchAll(PDO::FETCH_ASSOC);

// მეორე SQL მოთხოვნა იმისათვის, რომ გამოვთვალოთ რამდენი გვექნება გვერდი
$totalSql = "SELECT COUNT(*) FROM orders";
$totalStmt = $pdo->prepare($totalSql);
$totalStmt->execute();
$totalOrders = $totalStmt->fetchColumn();

// გამოთვლის რა რაოდენობის გვერდი იქნება საჭირო
$totalPages = ceil($totalOrders / $perPage);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Orders</title>
    <link rel="stylesheet" href="css/styles.css">
</head>
<body>
    <!-- Sidebar -->
    <?php include 'includes/sidebar.php'; ?>

    <!-- Main Content -->
    <div class="main-content">
        <div class="navbar">
            <h2>Manage Orders</h2>
            <div class="menu-icon">
                <span>&#9776;</span>
            </div>
        </div>

        <div class="content">
            <h3>All Orders</h3>
            
            <!-- Orders Table -->
            <table class="user-table">
                <thead>
                    <tr>
                        <th>Lead #</th>
                        <th>From</th>
                        <th>To</th>
                        <th>Transport Type</th>
                        <th>Vehicle Year</th>
                        <th>Vehicle Brand</th>
                        <th>Vehicle Model</th>
                        <th>Operable</th>
                        <th>Email</th>
                        <th>Available Date</th>
                        <th>Name</th>
                        <th>Phone</th>
                        <th>Created At</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($orders as $order): ?>
                        <tr>
                            <td><?php echo $order['id']; ?></td>
                            <td><?php echo htmlspecialchars($order['transport_from']); ?></td>
                            <td><?php echo htmlspecialchars($order['transport_to']); ?></td>
                            <td><?php echo htmlspecialchars($order['transport_type']); ?></td>
                            <td><?php echo htmlspecialchars($order['vehicle_year']); ?></td>
                            <td><?php echo htmlspecialchars($order['vehicle_brand']); ?></td>
                            <td><?php echo htmlspecialchars($order['vehicle_model']); ?></td>
                            <td><?php echo htmlspecialchars($order['vehicle_operable']); ?></td>
                            <td><?php echo htmlspecialchars($order['email']); ?></td>
                            <td><?php echo htmlspecialchars($order['available_date']); ?></td>
                            <td><?php echo htmlspecialchars($order['name']); ?></td>
                            <td><?php echo htmlspecialchars($order['phone']); ?></td>
                            <td><?php echo htmlspecialchars($order['created_at']); ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>

            <!-- Pagination -->
            <div class="pagination">
                <?php if ($page > 1): ?>
                    <a href="orders.php?page=1">&laquo; First</a>
                    <a href="orders.php?page=<?php echo $page - 1; ?>">Prev</a>
                <?php endif; ?>

                <?php
                // Start from page 1 or previous pages
                $start = max(1, $page - 1); // Always show current page and 1 previous
                $end = min($totalPages, $page + 1); // Always show current page and 1 next

                for ($i = $start; $i <= $end; $i++): ?>
                    <a href="orders.php?page=<?php echo $i; ?>" class="<?php echo ($i == $page) ? 'active' : ''; ?>"><?php echo $i; ?></a>
                <?php endfor; ?>

                <!-- Always show the last page -->
                <?php if ($page < $totalPages): ?>
                    <a href="orders.php?page=<?php echo $page + 1; ?>">Next</a>
                    <a href="orders.php?page=<?php echo $totalPages; ?>">Last &raquo;</a>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="js/script.js"></script>
</body>
</html>
