<?php
session_start();

include '../includes/config.php';
include 'connect.php';

// SQL მოთხოვნა ბლოგ პოსტების რაოდენობის მისაღებად
$blog_sql = "SELECT COUNT(*) as total_blogs FROM blog_posts";
$blog_stmt = $pdo->prepare($blog_sql);
$blog_stmt->execute();
$blog_count = $blog_stmt->fetch(PDO::FETCH_ASSOC)['total_blogs'];

// SQL მოთხოვნა შეკვეთების რაოდენობის მისაღებად
$order_sql = "SELECT COUNT(*) as total_orders FROM orders";
$order_stmt = $pdo->prepare($order_sql);
$order_stmt->execute();
$order_count = $order_stmt->fetch(PDO::FETCH_ASSOC)['total_orders'];

// SQL მოთხოვნა Enclosed ტრანსპორტების რაოდენობის მისაღებად
$enclosed_sql = "SELECT COUNT(*) as total_enclosed FROM orders WHERE transport_type = 'Enclosed'";
$enclosed_stmt = $pdo->prepare($enclosed_sql);
$enclosed_stmt->execute();
$enclosed_count = $enclosed_stmt->fetch(PDO::FETCH_ASSOC)['total_enclosed'];

// SQL მოთხოვნა Open ტრანსპორტების რაოდენობის მისაღებად
$open_sql = "SELECT COUNT(*) as total_open FROM orders WHERE transport_type = 'Open'";
$open_stmt = $pdo->prepare($open_sql);
$open_stmt->execute();
$open_count = $open_stmt->fetch(PDO::FETCH_ASSOC)['total_open'];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard</title>
    <link rel="stylesheet" href="css/styles.css">
</head>
<body>
    <!-- Sidebar -->
    <?php include 'includes/sidebar.php'; ?>
    
    <!-- Main Content -->
    <div class="main-content">
        <div class="navbar">
         <h2>Welcome, <?php echo htmlspecialchars($_SESSION['username']); ?>!</h2>
            <div class="menu-icon">
                <span>&#9776;</span>
            </div>
        </div>

        <div class="content">
            <h3>Dashboard Overview</h3>
            
            <!-- Dashboard Stats -->
            <div class="dashboard">
                <div class="card">
                    <h3>Total Blog Posts</h3>
                    <p><?php echo $blog_count; ?></p>
                </div>

                <div class="card">
                    <h3>Total Order</h3>
                    <p><?php echo $order_count; ?></p>
                </div>

                <div class="card">
                    <h3>Total Enclosed Order</h3>
                    <p><?php echo $enclosed_count; ?></p>
                </div>

                <div class="card">
                    <h3>Total Open Order</h3>
                    <p><?php echo $open_count; ?></p>
                </div>
            </div>
        </div>
    </div>

    <script src="js/script.js"></script>
</body>
</html>