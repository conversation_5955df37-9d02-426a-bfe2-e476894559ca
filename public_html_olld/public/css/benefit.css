
.fix-benefit-container {
    display: flex;
    gap: 20px; 
    justify-content: center;
    flex-wrap: wrap; 
    max-width: 100%;
    margin: 0 auto;
  }
  
  .fix-benefit-title {
    max-width: 75%;
    font-size: 38px;
    font-weight: 700;
    color: #222a31;
    margin-top: 100px;
    margin-bottom: 30px;
    text-align: center;
  }
  
  .fix-benefit-card {
    border: 1px solid #EBE2D8;
    border-radius: 10px;
    width: 32%;
    padding: 20px;
    text-align: center;
    flex: 1 1 calc(33.333% - 20px); 
    box-sizing: border-box; 
    background: #222a31;
  }

  .fix-benefit-icon {
    position: relative;
    margin-bottom: 20px;
  }
  
  .fix-benefit-car-icon {
    background-image: url('../icons/Door-to-Door.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    width: 100px;
    height: 50px;
    margin: 0 auto;
  }
  
  .fix-benefit-magnifier-icon {
    background-image: url('../icons/No-Payment.png'); 
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    width: 100px;
    height: 50px;
    margin: 0 auto;
  }
  
  .fix-benefit-car-3 {
    background-image: url('../icons/Safety.png'); 
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    width: 100px;
    height: 50px;
    margin: 0 auto;
  }

  
  .fix-benefit-insurance-icon {
    background-image: url('../icons/Insurance.png'); 
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    width: 100px;
    height: 50px;
    margin: 0 auto;
  }

  .fix-benefit-nationwide-icon {
    background-image: url('../icons/Nationwide.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    width: 100px;
    height: 50px;
    margin: 0 auto;
  }
  
  .fix-benefit-inspections-icon {
    background-image: url('../icons/Inspections.png'); 
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    width: 100px;
    height: 50px;
    margin: 0 auto;
  }
  
  .h1-title {
    font-size: 20px;
    color: #EBE2D8;
    font-weight: bold;
    margin: 10px 0;
  }
  
  .p-title {
    font-size: 16px;
    color: #EBE2D8;
    line-height: 1.5;
    @media only screen and (max-width: 768px) {
      font-size: 14px;
    color: #0000007f;
    line-height: 1.5;
  }
  }
  

  
  .fix-benefit-mob-card {
          background-color: #fff;
          color: #222a31;
          padding: 16px;
          margin-bottom: 16px;
          border-radius: 5px;
          width: 100%;
          cursor: pointer;
          display: flex;
          flex-direction: column;
          align-items: flex-start;
      }
  
      .fix-benefit-mob-title {
    font-size: 28px;
    font-weight: bold;
    color: #222a31;
    margin-top: 0px;
    margin-bottom: 30px;
    text-align: center;
    @media only screen and (max-width: 550px) {
      margin-bottom: 15px;
      font-size: 23px;
      line-height: 25px;
      font-weight: 700;
  }
  }
  
      .fix-benefit-mob-card .fix-benefit-mob-icon {
          width: 28px;
          height: 28px;
          margin-right: 10px;
          background-size: cover;
          background-position: center;
      }
  
      .fix-benefit-mob-card h1 {
          font-size: 16px;
          font-weight: 400;
          margin: 0;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          width: 100%;
      }
  
      .fix-benefit-mob-card .fix-benefit-mob-toggle-arrow {
          width: 10px;
          height: 10px;
          border-left: 2px solid #9ba3af;
          border-bottom: 2px solid #9ba3af;
          transform: rotate(45deg);
          transition: transform 0.3s ease;
          margin-left: auto;
      }
  
      .fix-benefit-mob-card p {
          display: none;
          color: #222A31;
          margin-top: 15px;
          line-height: 1.5;
          font-size: 14px;
          font-weight: 300;
          @media only screen and (max-width: 1000px) {
            text-align: justify;
          }
      }
  
      .fix-benefit-mob-card.fix-benefit-mob-open .fix-benefit-mob-toggle-arrow {
          transform: rotate(-45deg);
      }
  
      .fix-benefit-mob-mobile-only {
  display: none;
  }
  
  @media (max-width: 768px) {
  .fix-benefit-mob-mobile-only {
      display: block;
      margin-bottom: 30px;
  }
  }
  
  .fix-benefit-mob-desk-only {
  display: block;
  margin-bottom: 100px;
  }
  
  @media (max-width: 768px) {
  .fix-benefit-mob-desk-only {
      display: none;
      margin-bottom: 30px;
  }
  }