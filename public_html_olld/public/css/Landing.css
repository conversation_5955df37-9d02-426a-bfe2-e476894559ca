/* General Styles */
.fix-landing-container {
  margin: 0 auto;
  max-width: 1150px;
}

.fix-landing-section-title {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #000;
}

.fix-landing-info-sections {
  display: flex;
  justify-content: space-between;
  margin-bottom: 40px;
}

.fix-landing-info-box {
  flex: 1;
  padding: 20px;
  margin-right: 20px;
  border-radius: 8px;
}.fix-landing-routes-table th

.fix-landing-info-box:last-child {
  margin-right: 0;
}

.fix-landing-info-box h3 {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #000;
}

.fix-landing-info-box p {
  color: #333;
  line-height: 1.6;
}

.fix-landing-info-box ul {
  padding-left: 20px;
  list-style-type: disc;
  color: #333;
}

/* CTA Section */
.fix-landing-cta-section {
  text-align: center;
  margin-top:100px;
  margin-bottom: 20px;
}

.fix-landing-cta-box {
  background-color: #222A31;
  color: #fff;
  padding: 40px;
  border-radius: 8px;
}

.fix-landing-cta-box h2 {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 20px;
  color: #fff;
}

.fix-landing-cta-box p {
  font-size: 16px;
  font-weight: 300;
  text-align: center;
  line-height: 16.07px;
  margin-bottom: 20px;
  color: #FFFFFF;
}

.fix-landing-cta-box button {
  background-color: #D2B67F;
  color: #333;
  padding: 15px 30px;
  font-size: 16px;
  font-weight: bold;
  max-width: 250px;
  text-transform: uppercase;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

.fix-landing-cta-box button:hover {
  background-color: #c2a060;
}

/* Table Section */
.fix-landing-routes-section {
  margin-bottom: 0px;
}

.fix-landing-routes-section h3 {
  font-size: 32px;
  margin-bottom: 30px;
  font-weight: 700;
  line-height: 30px;
  color: #222A31;
  @media (max-width: 550px) {
    margin-bottom: 10px;
    font-size: 22px;
}
}

.fix-landing-routes-section p {
  margin-bottom: 20px;
  color: #666;
}

.fix-landing-routes-table {
  width: 100%;
  border-collapse: collapse;
  border-radius: 8px;
  overflow: hidden;
  @media only screen and (max-width: 1050px) {
    display: math;
  }
}


.fix-landing-routes-table th,
.fix-landing-routes-table td {
  border: 1px solid #ddd;
  padding: 22px;
  text-align: left;
}

.fix-landing-routes-table th {
  font-family: 'Nohemi';
  background-color: #222A31;
  font-weight: 700;
  font-size: 18px;
  color: #F3F1F5;
  text-align: center;
  line-height: 20.09px;
}

.fix-landing-routes-table td {
  font-size: 18px;
  font-weight: 400;
  line-height: 20.09px;
  text-align: center;
  font-family: 'Nohemi';
}

.fix-landing-routes-table td a {
  color: #222A31;
  text-decoration: none;
}

.fix-landing-routes-table td a:hover {
  text-decoration: underline;
}



.fix-landing-cities-list {
  margin-bottom: 100px;
  line-height: 1.8;
}

.fix-landing-cities-list strong {
  font-size: 16px;
  font-weight: 700;
  line-height: 16.07px;
  color: #222A31;
  margin-top: 20px;
}

.fix-landing-cities-list hr {
  border: none;
  border-bottom: 2px solid #CDA565;
  margin: 20px 0;
}

.fix-landing-cities-list a {
  color: #CDA565;
  font-size: 16px;
  line-height: 16.07px;
  font-weight: 500;
  text-decoration: none;
}

.fix-landing-cities-list a:hover {
  text-decoration: underline;
}

/* Mobile Styles */
@media (max-width: 768px) {
  
  .fix-landing-container {
    padding: 0px 20px;
    line-height: 20px;
  }

  .fix-landing-info-sections {
    flex-direction: column;
  }

  .fix-landing-info-box {
    margin-right: 0;
    margin-bottom: 20px;
    background-color: #fff;
    padding: 20px;
  }

  .fix-landing-info-box h3 {
    font-size: 22px;
  }

  .fix-landing-info-box p, .fix-landing-info-box ul {
    font-size: 14px;
  }

  .fix-landing-info-box ul {
    padding-left: 20px;
  }

  .fix-landing-cta-box {
    padding: 30px 20px;
  }

  .fix-landing-cta-box h2 {
    font-size: 20px;
  }

  .fix-landing-cta-box p {
    font-size: 14px;
  }

  .fix-landing-cta-box button {
    padding: 10px 20px;
    font-size: 14px;
  }

  .fix-landing-routes-table th, 
  .fix-landing-routes-table td {
    padding: 12px;
    font-size: 14px;
  }

  .fix-landing-cities-list {
    font-size: 14px;
    line-height: 1.5;
    padding-left: 0;
  }

  .fix-landing-cities-list strong {
    font-size: 16px;
    margin-bottom: 10px;
  }

  .fix-landing-cities-list p {
    font-size: 14px;
    margin-bottom: 5px;
  }

  .fix-landing-cities-list hr {
    margin: 10px 0;
  }

  .fix-landing-show-more {
    text-align: center;
    margin-top: 10px;
  }

  .fix-landing-show-more a {
    color: #007bff;
    font-size: 14px;
    cursor: pointer;
  }

  .fix-landing-show-more a:hover {
    text-decoration: underline;
  }

  .fix-landing-book-section {
    padding: 20px;
    text-align: left;
  }

  .fix-landing-book-section h2 {
    font-size: 20px;
    margin-bottom: 10px;
  }

  .fix-landing-book-section p {
    font-size: 14px;
    margin-bottom: 10px;
  }
}


.fix-landing-list {
  margin-bottom: 40px;
  line-height: 1.8;
}

.fix-landing-list h3 {
  margin-top: 100px;
  font-size: 30px;
  font-weight: 700;
  line-height: 36.16px;
  margin-bottom: 15px;
  color: #222A31;
  @media (max-width:768px) {
    margin-top: 50px;
    font-size: 19px;
    line-height: 22px;
    font-weight: 700;
}
}

.fix-landing-list p {
  font-size: 16px;
  color: #00000080;
}

.fix-landing-list strong {
  font-weight: 700;
  color: #00000080;
}

.fix-landing-links-section {
  margin-top: 100px;
  @media (max-width:768px) {
    margin-top: 50px;
}
}

.fix-landing-links-section h3 {
  color: #222A31;
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 50px;
  @media (max-width: 768px) {
    margin-bottom: 20px;
    font-size: 24px;
    font-weight: 700;
}
}

.fix-landing-list h2 {
  @media (max-width: 768px) {
    font-size: 18px;
}
}

.fix-landing-links-section ul {
  list-style-type: square;
  padding-left: 20px;
}

.fix-landing-links-section ul li {
  margin-bottom: 20px;
}

.fix-landing-links-section ul li a {
  font-family: 'Nohemi';
  color: #CDA565;
  font-size: 18px;
  line-height: 26px;
  font-weight: 600 !important;
  text-decoration: none;
  @media (max-width: 550px) {
    font-size: 16px;
  line-height: 26px;
  font-weight: 500 !important;
}
}

.fix-landing-links-section ul li a:hover {
  text-decoration: underline;
}


.dot::marker {
  color: #CDA565;
}

.fix-landing-from-section {
  font-size: 22px;
  font-weight: 700;
  line-height: 30px;
  color: #222A31;
}

.fix-landing-from-section h3 {
  font-size: 32px;
  font-weight: 700;
  line-height: 30px;
  margin-bottom: 30px;
  color: #222A31;
  @media (max-width: 550px) {
    margin-bottom: 10px;
    font-size: 22px;
}
}


.fix-landing-additional-section h3 {
  font-size: 32px;
  margin-bottom: 30px;
  font-weight: 700;
  line-height: 30px;
  color: #222A31;
  @media (max-width: 550px) {
    margin-bottom: 10px;
    font-size: 22px;
}
}

@media (max-width: 768px) {
  .fix-landing-from-table th, .fix-landing-from-table td {
      padding: 5px;
  }
}

@media (max-width: 768px) {
  .fix-landing-routes-table th, .fix-landing-routes-table td {
      padding: 5px;
  }
}

.mtvleli {
  padding-top: 20px;
  padding-bottom: 50px;
  @media (max-width: 768px) {
        padding: 0px;
    }
}


.fix-p-li {
  color: #7b6f65; 
  font-size:18px; 
  font-weight:600;
  line-height: 26px;
}

.fix-p-li::marker {
  color: #CDA565; 
  font-size:18px; 
  font-weight:600;
  line-height: 26px;
}

.fix-p-strong {
  color: #7b6f65; 
  font-size:18px; 
  font-weight:600; 
  line-height: 26px
}

.fix-p-p {
  color: #00000080;
  font-size: 16px;
  font-weight: 300;
  line-height: 26px;
}

@media (max-width: 600px) {
  .fix-p-li {
    font-size: 16px;
    line-height: 22px;
  }

  .fix-p-strong {
    font-size: 16px;
    line-height: 22px;
  }

  .fix-p-p {
    font-size: 16px;
    line-height: 22px;
  }
}

.fix-pst-landing-cities-list {
  width: 100%;
  margin: auto;
}

.fix-pst-city-name {
  font-weight: 700;
  font-size: 16px;
  color: #222A31;
}

.fix-pst-subtext {
  font-weight: 400;
  font-size: 16px;
  color: #222A31;
}

.fix-pst-zipcode-list {
  font-size: 14px;
  color: #222A31;
  margin-top: -10px;
  line-height: 18.2px;
}

.fix-pst-divider {
  border: 0;
  height: 2px;
  background-color: #CDA565;
  margin: 15px 0;
}

.fix-pst-hidden-content {
  display: none;
}

.fix-pst-toggle-link {
  display: inline-block;
  font-size: 14px;
  color: #CDA565;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  line-height: 16.07px;
}

.fix-pst-toggle-link:hover {
  text-decoration: underline;
}


.instantquote2 {
  margin-top: 100px;
  gap: var(--space-5xl);
  display: flex;
  background-color: var(--blue_gray_900);
  flex-direction: column;
  align-items: center;
  padding: var(--space-6xl);
  border-radius: var(--radius-md);
  text-align: center;
  @media only screen and (max-width: 550px) {
    margin-top: 0px !important;
    padding: var(--space-5xl);
    border-radius: 10px;
}
}

.instantquote2 .wantan  {
  margin-top: 20px;
}

.instantquote2 .useour   {
  max-width:600px;
}

