/*=========== TABLE OF CONTENTS ===========
1. Common CSS
2. Page CSS
==========================================*/

/*-------------------------------------
  1. Common CSS
--------------------------------------*/
.dhi-group {
  cursor: pointer;
  &:hover > :last-child {
    display: block;
  }
}

.services {
  gap: var(--space-sm);
  display: flex;
  align-items: center;
  cursor: pointer;
}

.arrowdown_one {
  height: 4px;
  margin-bottom: 6px;
  width: 12px;
}

.dhi-group-2 {
  position: absolute;
  top: auto;
  min-width: 200px;
  padding-top: var(--space-lg);
  z-index: 99;
  display: none;
  group-hover {
    display: block;
  }
}

.menu-container {
  background-color: var(--white_a700);
  box-shadow: var(--shadow-xs);
  width: 100%;
  padding: var(--space-5xl);
  border-radius: var(--radius-sm);
}

.menu-group {
  gap: var(--space-6xl);
  display: flex;
}

.mega-menu__link {
  color: var(--black_600) !important;
  font-size: 16px;
  font-weight: 400;
  align-self: center;
  @media only screen and (max-width: 550px) {
    font-size: 13px;
  }
}

.menu {
  gap: var(--space-3xl);
  display: flex;
  flex-direction: column;
  align-items: start;
  @media only screen and (max-width: 550px) {
    gap: var(--space-3xl);
  }
}

.mega-menu__title {
  color: var(--black_900) !important;
  font-size: 18px;
  font-weight: 700;
  @media only screen and (max-width: 550px) {
    font-size: 15px;
  }
}

.menu-column {
  gap: var(--space-lg);
  display: flex;
  flex-direction: column;
  align-items: start;
  @media only screen and (max-width: 550px) {
    gap: var(--space-lg);
  }
}

.menu-link {
  align-self: center;
  @media only screen and (max-width: 550px) {
    font-size: 13px;
  }
}

.mega-menu__text {
  color: var(--black_600) !important;
  font-size: 16px;
  font-weight: 400;
}

.home {
  font-weight: 400 !important;
}

.open {
  color: var(--gray_800);
  font-size: 14px;
  font-weight: 300;
  gap: var(--space-sm);
  display: flex;
}

.columnclassicca {
  gap: var(--space-xs);
  display: flex;
  flex-direction: column;
  align-items: start;
}

.preservingthe {
  color: var(--black_900_7f) !important;
  line-height: 12px;
}

.columndoortodoo-1 {
  display: flex;
  flex-direction: column;
  align-items: start;
}

.columnmakeiteas {
  gap: var(--space-6xl);
  display: flex;
  background-color: var(--gray_300);
  width: 100%;
  flex-direction: column;
  align-items: start;
  justify-content: center;
  padding: var(--space-6xl) 36px;
  border-radius: var(--radius-md);
  @media only screen and (max-width: 550px) {
    padding: var(--space-5xl);
  }
}

.description-2 {
  width: 94%;
  line-height: 17px;
  @media only screen and (max-width: 1050px) {
    width: 100%;
  }
}

.rowwhatisdoorto {
  display: flex;
  align-items: center;
  @media only screen and (max-width: 1050px) {
    flex-direction: column;
  }
}

.description {
  width: 68%;
  line-height: 17px;
  @media only screen and (max-width: 1050px) {
    width: 100%;
  }
}

.view {
  height: 352px;
  background-color: var(--blue_gray_100);
  width: 40%;
  border-radius: var(--radius-md);
}

.gridmakeiteasy {
  align-self: stretch;
  display: grid;
  gap: 24px;
  grid-template-columns: repeat(2, minmax(0, 1fr));
  justify-content: center;
  @media only screen and (max-width: 1050px) {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
}

.rowbestprice {
  background-color: var(--gray_300);
  width: 100%;
  display: flex;
  padding: 28px var(--space-2xl);
  border-radius: var(--radius-md);
  @media only screen and (max-width: 550px) {
    padding-top: var(--space-5xl);
    padding-bottom: var(--space-5xl);
  }
}

.columnbestprice {
  gap: var(--space-3xl);
  display: flex;
  width: 100%;
  flex-direction: column;
  align-items: start;
}

.bestprice {
  margin-left: 82px;
  @media only screen and (max-width: 1050px) {
    margin-left: 0px;
  }
}

.rowdescription {
  gap: var(--space-xl);
  align-self: stretch;
  display: flex;
  justify-content: center;
  align-items: center;
  @media only screen and (max-width: 550px) {
    flex-direction: column;
  }
}

.image {
  height: 70px;
  width: 70px;
  @media only screen and (max-width: 550px) {
    width: 100%;
  }
}

.description-7 {
  width: 80%;
  line-height: 17px;
  @media only screen and (max-width: 550px) {
    width: 100%;
  }
}

.columnhome_one {
  gap: var(--space-2xl);
  display: flex;
  flex-direction: column;
  align-items: start;
}

.doortodoor_one {
  color: var(--black_900) !important;
}

.socialicon_one {
  height: 24px;
  width: 24px;
}

.weoffer-1 {
  color: var(--black_900_7f) !important;
}

/*-------------------------------------
  2. Page CSS
--------------------------------------*/
.columnheader_logo-one {
  background-color: var(--gray_100);
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.header {
  z-index: 1;
  position: relative;
  display: flex;
  background-color: var(--gray_100);
  align-self: stretch;
  justify-content: space-between;
  align-items: center;
  gap: var(--space-5xl);
  padding: 26px 28px;
  @media only screen and (max-width: 1050px) {
    flex-direction: column;
  }

  @media only screen and (max-width: 550px) {
    padding: var(--space-5xl);
  }
}

.headerlogo_one {
  height: 38px;
  margin-left: 110px;
  width: 230px;
  object-fit: contain;
  @media only screen and (max-width: 1050px) {
    margin-left: 0px;
  }
}

.rowhome {
  gap: var(--space-8xl);
  display: flex;
  @media only screen and (max-width: 550px) {
    flex-direction: column;
  }
}

.cta {
  gap: var(--space-5xl);
  display: flex;
}

.quote {
  color: var(--white_a700);
  padding-left: var(--space-6xl);
  padding-right: var(--space-6xl);
  font-size: 16px;
  font-weight: 700;
  background-color: var(--deep_orange_300);
  text-align: center;
  height: 46px;
  min-width: 110px;
  border-radius: var(--radius-lg);
  @media only screen and (max-width: 550px) {
    padding-left: var(--space-5xl);
    padding-right: var(--space-5xl);
  }
}

.call_one {
  height: 46px;
  padding-left: var(--space-lg);
  padding-right: var(--space-lg);
  background-color: var(--blue_gray_900);
  width: 46px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-lg);
}

.rowdoortodoor {
  height: 744px;
  margin-top: -4px;
  position: relative;
  background-image: url(../images/img_group_10.png);
  background-size: cover;
  background-repeat: no-repeat;
  align-self: stretch;
  @media only screen and (max-width: 1050px) {
    height: auto;
  }
}

.stackdoortodoor {
  height: 740px;
  padding-left: 140px;
  padding-right: 140px;
  background-color: var(--black_900_8c);
  position: relative;
  @media only screen and (max-width: 1050px) {
    padding-left: var(--space-5xl);
    padding-right: var(--space-5xl);
  }
}

.columndoortodoo {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: start;
  position: absolute;
  left: 0px;
  bottom: 0px;
  right: 0px;
  top: 0px;
  height: max-content;
  margin: auto;
}

.doortodoor {
  color: var(--white_a700) !important;
  font-size: 50.8px !important;
  @media only screen and (max-width: 550px) {
    font-size: 40px !important;
  }
}

.safecarhauler {
  color: var(--white_a700) !important;
  margin-left: 4px;
  width: 60%;
  line-height: 20px;
  @media only screen and (max-width: 1050px) {
    width: 100%;
    margin-left: 0px;
  }
}

.get_an_instant {
  color: var(--white_a700);
  margin-top: 14px;
  margin-left: 4px;
  padding-left: 34px;
  padding-right: 34px;
  font-size: 16px;
  font-weight: 700;
  background-color: var(--deep_orange_300);
  text-align: center;
  height: 46px;
  min-width: 230px;
  border-radius: var(--radius-lg);
  @media only screen and (max-width: 1050px) {
    margin-left: 0px;
  }

  @media only screen and (max-width: 550px) {
    padding-left: var(--space-5xl);
    padding-right: var(--space-5xl);
  }
}

.quote-1 {
  background-color: var(--gray_300);
  width: 32%;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: absolute;
  right: 10%;
  bottom: 0px;
  top: 0px;
  margin-top: auto;
  margin-bottom: auto;
  height: max-content;
  border-radius: var(--radius-sm);
}

.getaninstant {
  color: var(--gray_800) !important;
  margin-top: 28px;
  line-height: 30px;
}

.getaninstant-span {
  font-size: 16px;
  font-weight: 300;
}

.city {
  color: var(--gray_800);
  margin-top: 26px;
  margin-left: 32px;
  margin-right: 36px;
  padding-left: var(--space-lg);
  padding-right: var(--space-lg);
  font-size: 14px;
  font-weight: 300;
  gap: 24px;
  display: flex;
  background-color: var(--gray_100);
  align-self: stretch;
  align-items: center;
  justify-content: center;
  height: 44px;
  border-radius: var(--radius-sm);
  border: 1px solid var(--deep_orange_100);
  @media only screen and (max-width: 1050px) {
    margin-left: 0px;
    margin-right: 0px;
  }
}

.dhi-group-4 {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 12px;
  height: 16px;
}

.linkedin {
  height: 14px;
  width: 12px;
}

.row {
  margin-top: 14px;
  margin-left: 32px;
  margin-right: 36px;
  background-color: var(--gray_100);
  align-self: stretch;
  padding: var(--space-lg);
  border-radius: var(--radius-sm);
  border: 1px solid var(--deep_orange_100);
  @media only screen and (max-width: 1050px) {
    margin-left: 0px;
    margin-right: 0px;
  }
}

.rowlinkedin_one {
  gap: 21px;
  display: flex;
}

.linkedin_one {
  height: 16px;
}

.toziporcity {
  color: var(--gray_800) !important;
}

.columnvehicle {
  margin-top: 18px;
  gap: 28px;
  display: flex;
  align-self: stretch;
  flex-direction: column;
}

.rowtransporttyp {
  margin-left: 32px;
  margin-right: 32px;
  @media only screen and (max-width: 1050px) {
    margin-left: 0px;
    margin-right: 0px;
  }
}

.columntransport {
  gap: var(--space-md);
  display: flex;
  flex-direction: column;
  align-items: start;
}

.transporttype {
  color: var(--gray_800) !important;
  font-weight: 500 !important;
}

.rowopen {
  gap: var(--space-5xl);
  align-self: stretch;
  display: flex;
  align-items: center;
}

.vehicle_details {
  color: var(--gray_100);
  padding-left: 34px;
  padding-right: 34px;
  border-bottom-left-radius: var(--radius-sm);
  border-bottom-right-radius: var(--radius-sm);
  font-size: 16px;
  font-weight: 700;
  background-color: var(--deep_orange_300);
  align-self: stretch;
  text-align: center;
  height: 44px;
  @media only screen and (max-width: 550px) {
    padding-left: var(--space-5xl);
    padding-right: var(--space-5xl);
  }
}

.dropdown-1 {
  border-bottom-left-radius: var(--radius-md);
  border-bottom-right-radius: var(--radius-md);
  background-color: var(--white_a700);
  width: 58%;
  display: flex;
  justify-content: center;
  align-items: start;
  position: absolute;
  top: 0px;
  right: 0px;
  left: 0px;
  padding: 26px;
  margin: auto;
  @media only screen and (max-width: 550px) {
    flex-direction: column;
    position: relative;
    padding: var(--space-5xl);
  }
}

.weoffer {
  margin-top: 6px;
  gap: var(--space-md);
  display: flex;
  width: 100%;
  flex-direction: column;
  align-items: start;
  @media only screen and (max-width: 550px) {
    width: 100%;
  }
}

.column {
  gap: var(--space-4xl);
  display: flex;
  align-self: stretch;
  flex-direction: column;
}

.weserve {
  margin-top: 6px;
  gap: var(--space-md);
  align-self: center;
  display: flex;
  width: 100%;
  flex-direction: column;
  align-items: start;
  @media only screen and (max-width: 550px) {
    width: 100%;
  }
}

.column_three {
  gap: var(--space-md);
  display: flex;
  align-self: stretch;
  flex-direction: column;
}

.column_one {
  gap: 6px;
  display: flex;
  flex-direction: column;
}

.column_two {
  gap: var(--space-4xl);
  display: flex;
  flex-direction: column;
}

.column_four {
  margin-top: 38px;
  align-self: stretch;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.columnwhatyoull {
  gap: var(--space-7xl);
  display: flex;
  flex-direction: column;
  align-items: center;
  @media only screen and (max-width: 1050px) {
    padding-left: var(--space-5xl);
    padding-right: var(--space-5xl);
  }
}

.whatyoull {
  text-align: center;
  width: 64%;
  line-height: 48px;
  @media only screen and (max-width: 1050px) {
    width: 100%;
  }

  @media only screen and (max-width: 550px) {
    font-size: 38px;
  }
}

.columnview {
  margin-top: 88px;
  gap: var(--space-8xl);
  display: flex;
  flex-direction: column;
  @media only screen and (max-width: 1050px) {
    gap: 60px;
    padding-left: var(--space-5xl);
    padding-right: var(--space-5xl);
  }

  @media only screen and (max-width: 550px) {
    gap: 40px;
  }
}

.columnwhatisdoo {
  gap: var(--space-5xl);
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: start;
  @media only screen and (max-width: 1050px) {
    align-self: stretch;
  }
}

.whatisdoorto {
  width: 68%;
  line-height: 36px;
  @media only screen and (max-width: 1050px) {
    width: 100%;
  }

  @media only screen and (max-width: 550px) {
    font-size: 32px;
  }
}

.rowview_one {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--space-5xl);
  @media only screen and (max-width: 1050px) {
    flex-direction: column;
  }
}

.columnhowmuchdo {
  margin-bottom: 36px;
  gap: var(--space-5xl);
  align-self: end;
  display: flex;
  width: 46%;
  flex-direction: column;
  @media only screen and (max-width: 1050px) {
    align-self: auto;
    width: 100%;
  }
}

.howmuchdo {
  line-height: 36px;
  @media only screen and (max-width: 550px) {
    font-size: 32px;
  }
}

.description-1 {
  width: 88%;
  line-height: 17px;
  @media only screen and (max-width: 1050px) {
    width: 100%;
  }
}

.columnview_two {
  gap: var(--space-7xl);
  display: flex;
  flex-direction: column;
}

.columnwhatis {
  gap: var(--space-5xl);
  align-self: end;
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: start;
  @media only screen and (max-width: 1050px) {
    align-self: stretch;
  }

  @media only screen and (max-width: 550px) {
    align-self: auto;
  }
}

.whatis {
  width: 76%;
  line-height: 36px;
  @media only screen and (max-width: 1050px) {
    width: 100%;
  }

  @media only screen and (max-width: 550px) {
    font-size: 32px;
  }
}

.columnwhychoose {
  gap: 42px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.whychoose {
  text-align: center;
  width: 66%;
  line-height: 48px;
  @media only screen and (max-width: 1050px) {
    width: 100%;
  }

  @media only screen and (max-width: 550px) {
    font-size: 38px;
  }
}

.footer {
  margin-top: 250px !important;
  display: flex;
  background-color: var(--gray_300);
  align-self: stretch;
  justify-content: center;
  align-items: end;
  padding: var(--space-4xl);
}

.columnfooterlog {
  margin-top: 42px;
  gap: 48px;
  display: flex;
  width: 84%;
  flex-direction: column;
  align-items: center;
  @media only screen and (max-width: 1050px) {
    width: 100%;
  }
}

.rowfooterlogo {
  align-self: stretch;
  display: flex;
  justify-content: space-between;
  align-items: start;
  gap: var(--space-5xl);
  @media only screen and (max-width: 1050px) {
    flex-direction: column;
  }
}

.footerlogo_one {
  height: 120px;
  width: 328px;
  object-fit: contain;
}

.rowcontact_two {
  align-self: center;
  width: 66%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--space-5xl);
  @media only screen and (max-width: 1050px) {
    flex-direction: column;
    width: 100%;
  }
}

.rownavigation {
  width: 48%;
  display: flex;
  justify-content: space-between;
  align-items: start;
  gap: var(--space-5xl);
  @media only screen and (max-width: 1050px) {
    width: 100%;
  }

  @media only screen and (max-width: 550px) {
    flex-direction: column;
  }
}

.columnnavigatio {
  gap: var(--space-lg);
  display: flex;
  width: 52%;
  flex-direction: column;
  align-items: start;
  @media only screen and (max-width: 550px) {
    width: 100%;
  }
}

.columnservices {
  gap: var(--space-lg);
  align-self: center;
  display: flex;
  width: 68%;
  flex-direction: column;
  align-items: start;
  @media only screen and (max-width: 550px) {
    width: 100%;
  }
}

.columnemail {
  width: 38%;
  display: flex;
  flex-direction: column;
  align-items: start;
  @media only screen and (max-width: 1050px) {
    width: 100%;
  }
}

.columncontact {
  gap: var(--space-4xl);
  display: flex;
  align-self: stretch;
  flex-direction: column;
  align-items: start;
}

.description-link {
  line-height: 16px;
}

.email {
  color: var(--black_900) !important;
  margin-top: 20px;
}

.class-18778788008 {
  color: var(--black_900) !important;
  margin-top: 14px;
}

.rowsocialicon {
  margin-top: 28px;
  width: 54%;
  display: flex;
  justify-content: space-between;
  gap: var(--space-5xl);
  @media only screen and (max-width: 1050px) {
    width: 100%;
  }
}

.rowcopyright202 {
  margin-left: 34px;
  display: flex;
  justify-content: space-between;
  align-items: start;
  gap: var(--space-5xl);
  @media only screen and (max-width: 1050px) {
    margin-left: 0px;
  }

  @media only screen and (max-width: 550px) {
    flex-direction: column;
  }
}

.rowprivacypolic {
  align-self: center;
  width: 26%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--space-5xl);
  @media only screen and (max-width: 550px) {
    width: 100%;
  }
}

.row_one {
  display: flex;
}

.rowtermandcondi {
  gap: var(--space-4xl);
  display: flex;
  align-items: start;
}

.lineone_one {
  height: 28px;
  align-self: center;
  background-color: var(--black_900);
  width: 1px;
}
