.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 20px;
}

.contact-header {
    max-width: 1440px;
    margin: 0 auto;
    position: relative;
    background-image: url('../images/Contact-page-background.jpg'); 
    background-size: cover;
    background-position: center;
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: left;
    color: white;
    text-align: left;
    z-index: 1;
}

.contact-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 0;
}

.contact-header h1 {
    color: #fff;
    font-size: 50px;
    font-weight: 700;
    line-height: 51.03px;
    position: relative; 
    z-index: 1;
    margin-left: 150px;
}

.contact-header p {
    margin-top: 10px;
    color: #fff;
    font-size: 20px;
    font-weight: 300;
    line-height: 20.09px;
    position: relative; 
    z-index: 1;
    margin-left: 150px;
}

.contact-body {
    display: flex;
    justify-content: space-between;
    margin-top: 40px;
}

.contact-form-container {
    display: flex;
    align-items: center;
    justify-content: center;
}

.contact-form, .contact-info {
    flex: 1;
}

.contact-form {
    background-color: #EBE2D8;
    width: 510px;
    height: 650px;
    padding: 25px;
    position: relative;
    border-radius: 10px;
}

.contact-form h2 {
    font-size: 35.25px;
    font-weight: 700;
    line-height: 35.41px;
    margin-bottom: 20px;
    color: #46351A;
    margin-left: 5px;
}

.contact-form label {
    font-size: 24.52px;
    font-weight: 500;
    line-height: 24.63px;
    display: block;
    margin-bottom: 10px;
    margin-left: 5px;
    color: #46351A;
}

.contact-form input {
    width: 458.25px;
    height: 68.97px;
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid #EAD4B9;
    border-radius: 13.79px;
    background-color: #F3F1F5;
    font-size: 16px;
}

.contact-form textarea {
    width: 458.25px;
    height: 211.5px;
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid #EAD4B9;
    border-radius: 13.79px;
    background-color: #F3F1F5;
    font-size: 16px;
}

.contact-form button {
    background-color: #b58544;
    color: white;
    border: none;
    padding: 15px;
    cursor: pointer;
    width: 100%;
    font-size: 16px;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 1px;
    height: 60px;
    position: absolute;
    bottom: -30px;
    left: 0;
    right: 0;
    border-radius: 0 0 8px 8px;
}

.contact-form button:hover {
    background-color: #222a31;
    color: #b58544;
}

.contact-info {
    margin-left: 100px;
    border-radius: 8px;
}

.contact-info h2 {
    font-size: 24px;
    margin-bottom: 20px;
    color:#46351A;
    @media (max-width: 678px) {
        font-size: 22px;
        line-height: 20.39px;
    }
}

.contact-info p {
    margin-bottom: 10px;
    font-size: 18px;
    line-height: 1.6;
    color: #222A31;
    @media (max-width: 678px) {
        font-size: 14px;
        font-weight: 300;
    }
}

.contact-info strong {
    display: block;
    margin-top: 50px;
    margin-bottom:20px;
    font-size: 29px;
    color: #46351A;
    @media (max-width: 678px) {
        font-size: 18px;
        font-weight: 700;
    }
}

.contact-info a {
    color: #222A31;
    text-decoration: none;
}

.business-hours {
    display: flex; 
    justify-content: space-between; 
}

.business-hours p {
    margin-bottom: 10px;
}

.alert {
    position: absolute;
    top: 20px;
    right: 20px;
    padding: 15px;
    background-color: #4CAF50; 
    color: white;
    border-radius: 5px;
    display: none;
    z-index: 1000;
}

.alert.error {
    background-color: #f44336;
}

@media (max-width: 1175px) {
    .contact-body {
        flex-direction: column;
        align-items: center;
        margin-top: 0px;
    }

    .contact-form-container, .contact-info {
        width: 100%;
        margin: 0;
        padding: 20px;
    }

    .contact-form {
        width: 100%;
    }
    
    .contact-info {
        margin-top: 50px;
        width: 100%;
    }

    .contact-form input,
    .contact-form textarea {
        width: 100%;
        box-sizing: border-box;
    }

    .contact-form button {
        margin-top: 20px;
    }

    .contact-header h1 {
        font-size: 28px;
        margin-left: 35px;
    }

    .contact-header p {
        font-size: 16px;
        margin-left: 35px;
    }
}
