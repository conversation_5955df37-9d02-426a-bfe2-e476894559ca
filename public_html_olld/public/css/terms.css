.fix-term-container {
    background-color: #F3F1F5;
    padding: 20px;
    border: 1px solid #d3d3d3;
    border-radius: 5px;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
    page-break-after: always;
    width: 210mm;
    height: 297mm;
    
}

.fix-term-header {
    text-align: center;
    margin-bottom: 20px;
}

.fix-term-header h1 {
    margin-top: 20px;
    font-size: 48px;
    font-weight: 700;
    line-height: 48.22px;
    margin-bottom: 10px;
    text-transform: uppercase;
    color: #222A31;
}

.fix-term-header h2 {
    text-align: left;
    font-size: 16px;
    font-weight: 700;
    line-height: 16.07px;
    margin-top: 15px;
    margin-bottom: 15px;
    color: #222A31;
    margin-left: 50px;
}

.fix-term-header p {
    text-align: left;
    font-size: 16px;
    font-weight: 300;
    line-height: 16.07px;
    margin-top: 15px;
    margin-bottom: 15px;
    color: #222A31;
    margin-left: 50px;
}

.fix-term-header p strong {
    text-align: left;
    font-size: 16px;
    font-weight: 300;
    line-height: 16.07px;
    margin-top: 15px;
    margin-bottom: 15px;
    color: #222A31;
}

.fix-term-content {
    line-height: 1.5;
    color: #222A31;
    height: calc(100% - 150px);
    overflow-y: auto;
    padding-bottom: 50px;
    margin-left: 50px;
    padding-right: 50px;
}

.fix-term-content h3 {
    font-size: 16px;
    font-weight: 300;
    margin-bottom: 10px;
    color: #222A31;
}

.fix-term-content p, .fix-term-content ol {
    font-size: 14px;
    font-weight: 300;
    margin-bottom: 15px;
    color: #222A31;
}

.fix-term-content ol {
    list-style-type: none; 
    padding-left: 0; 
}

.fix-term-content ol li {
    margin-bottom: 10px;
}


.fix-term-content strong {
    font-size: 16px;
    font-weight: 300;
    color: #222A31;
}

.fix-term-pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: absolute;
    bottom: 20px;
    left: 0;
    right: 0;
    padding: 0 30px;
}

.fix-term-pagination p {
    font-size: 14px;
    color: #333;
}

.fix-term-page-controls {
    display: flex;
}

.fix-term-page-controls button {
    background-color: #333;
    color: #fff;
    border: none;
    padding: 8px;
    margin: 0 5px;
    font-size: 14px;
    cursor: pointer;
    border-radius: 3px;
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.fix-term-page-controls button:hover {
    background-color: #555;
}

.fix-term-page-controls button:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

@media (max-width: 768px) {
    .fix-term-container {
        width: 100vw;
        height: auto;
        max-width: 350px;
        aspect-ratio: 210 / 297;
        padding: 15px;
    }

    .fix-term-header h1 {
        font-size: 6vw;
    }

    .fix-term-header h2 {
        font-size: 4vw;
    }

    .fix-term-header p {
        font-size: 3vw;
    }

    .fix-term-content {
        height: auto;
        padding-bottom: 30px;
    }

    .fix-term-content h3 {
        font-size: 5vw;
    }

    .fix-term-content p, .fix-term-content ol {
        font-size: 4vw;
    }

    .fix-term-pagination p {
        font-size: 4vw;
    }

    .fix-term-page-controls button {
        font-size: 4vw;
        width: 10vw;
        height: 10vw;
    }
}
