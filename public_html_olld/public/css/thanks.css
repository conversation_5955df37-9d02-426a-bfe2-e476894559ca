* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    background-color: #f3f1f5; 
    color: #cda565;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
}

.container {
    padding: 20px;
    max-width: 600px;
}

.thank-you h1 {
    font-size: 2em;
    margin-bottom: 20px;
    color: #cda565; 
}

.thank-you p {
    font-size: 1em;
    margin-bottom: 20px;
    color: #222a31;
    text-align: justify;
}

.back-button {
    margin-top: 10px;
    font-size: 1em;
    color: #cda565; 
    text-decoration: none;
    padding: 12px 25px;
    border: 2px solid #cda565;
    border-radius: 5px;
    transition: background-color 0.3s, color 0.3s;
}

.back-button:hover {
    background-color: #cda565;
    color: #222a31;
}

.thank-you img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    margin-bottom: 30px;
}

.phone-number {
    color: #cda565;
    text-decoration: none;
    font-weight: bold;
    transition: color 0.3s, transform 0.3s;
}

.phone-number:hover {
    color: #222a31;
    transform: scale(1.1); 
}

.phone-number:active {
    color: #f39c12; 
    transform: scale(1); 
}

.timer {
    font-size: 1em;
    color: #0000007f;
    margin-top: 20px;
}

/* მობილური დისპლეებისთვის */
@media (max-width: 768px) {
    body {
        font-size: 14px; 
        padding: 10px; 
        height: 80vh;
    }

    .container {
        padding: 15px; 
    }

    .thank-you h1 {
        font-size: 1.5em; 
    }

    .thank-you p {
        font-size: 0.9em;
    }

    .back-button {
        padding: 10px 20px;
        font-size: 0.9em;
    }

    .thank-you img {
        max-width: 100%;  
        height: auto;
    }

    .timer {
        font-size: 0.9em;
    }
}

@media (max-width: 480px) {
    body {
        font-size: 12px;
    }

    .thank-you h1 {
        font-size: 1.2em; 
    }

    .thank-you p {
        font-size: 0.8em;
    }

    .back-button {
        padding: 8px 15px; 
        font-size: 0.8em;
    }
}
