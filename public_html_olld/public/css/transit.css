.order-fix-slider {
    width: 100%;
    overflow: hidden;
    position: relative;
    margin-bottom: 100px;
  }
  
  .order-fix-slides-container {
    display: flex;
    transition: transform 0.5s ease;
    margin-left: 38px;
  }
  
  .order-fix-slide {
    display: flex;
    flex-shrink: 0;
    width: 33.333%;
  }
  
  .order-fix-card {
    background: #222a31;
    border: 2px solid #cda565;
    color: #cda565;
    margin: 10px;
    border-radius: 8px;
    overflow: hidden;
    text-align: left;
    width: 100%;
    max-width: 300px;
  }
  
  .order-fix-card p {
    color: #fff;
  }
  
  .order-fix-card p strong {
    color: #cda565;
  }
  
  .order-fix-card img {
    width: 100%;
    height: auto;
  }
  
  .order-fix-info-section {
    padding: 15px;
  }
  
  .order-fix-h2 {
    font-size: 16px;
    margin: 0 0 10px;
    color: #fff;
  }
  
  .order-fix-button {
    margin-top: 10px;
    padding: 10px;
    font-size: 14px;
    border: none;
    cursor: pointer;
    border-radius: 4px;
  }
  
  .order-fix-call-specialist {
    background-color: #cda565;
    color: black;
  }
  
  .order-fix-view-profile {
    background-color: #222a31;
    color: #cda565;
  }
  
  .order-fix-slider-controls {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    color: #cda565;
    padding: 10px;
    cursor: pointer;
    border-radius: 10px;
    background: #222a31;
    border: 1px solid #cda565;
    -webkit-appearance: none;    
    appearance: none;  
  }
  
  .order-fix-slider-controls-left {
    left: 5px;
  }
  
  .order-fix-slider-controls-right {
    right: 20px;
    left: auto;
  }
  
  .order-fix-card-cta {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin: 0 auto;
  }
  
  .order-fix-card-cta .order-fix-card-quote,
  .order-fix-card-cta .order-fix-card-call {
    cursor: pointer;
    color: #222a31;
    padding: 10px;
    font-size: 16px;
    font-weight: 600;
    background-color: var(--deep_orange_300);
    text-align: center;
    height: 37px;
    border-radius: 10px;
    transition: all 0.3s ease;
  
    /* Flexbox დამატება */
    display: flex;
    align-items: center; 
    justify-content: center; 
    gap: 8px; 
  }
  
  .order-fix-card-cta .order-fix-card-quote:hover,
  .order-fix-card-cta .order-fix-card-call:hover {
    background-color: #cda565;
    color: #fff;
    transform: scale(1.05);
  }
  
  .order-fix-card-call .order-fix-card-call-icon {
    height: 15px !important;
    width: 15px !important;
    display: inline-block;
    vertical-align: middle;
  }
  
  
  .order-fix-title {
    max-width: 75%;
    font-size: 38px;
    font-weight: 700;
    color: #222a31;
    margin-top: 0px;
    margin-bottom: 30px;
    text-align: center;
    margin-left: auto;
    margin-right: auto;
    display: block;
    @media screen and (max-width: 768px) {
      font-size: 27px;
      margin-bottom: 10px;
    }
  }
  
  @media screen and (max-width: 768px) {
    .order-fix-slider {
    margin-bottom: 50px;
    }
    .order-fix-slide {
      width: 100%; 
    }
  
    .order-fix-slider-controls {
      display: block; 
    }
  
    .order-fix-slider-controls-left {
      display: none;
      -webkit-appearance: none; 
      appearance: none;
    left: 0;
    }
    .order-fix-slider-controls-right {
      display: none;
      -webkit-appearance: none; 
      appearance: none;
      right: -10px;
    }
  
    .order-fix-slides-container {
      margin-left: 15px;
    }
  }
  
  
  .order-fix-slider-indicators {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 10px;
    gap: 5px;
    position: relative;
  }
  
  .order-fix-slider-indicator {
    display: none; 
    cursor: pointer;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #cda565;
    margin: 0 5px;
    transition: width 0.3s, height 0.3s;
  }
  
  .order-fix-slider-indicator.active {
    opacity: 1;
    background-color: #222a31;
    width: 8px; 
    height: 8px;
  }
  
  .order-fix-slider-indicator[style*="display: block"] {
    display: inline-block;
  }