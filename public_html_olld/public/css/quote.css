html, body {
    height: 100%;
    margin: 0;
    background-image: url('../images/quote-bg.png'); 
    background-repeat: no-repeat;
    background-position: bottom center;
}

.fix-quote-centered-form {
    display: flex;
    flex-direction: column; 
    justify-content: center;
    align-items: center;
    padding: 0 20px;
}

.fix-quote-logo {
    margin-bottom: 80px; 
    max-width: 300px;
    width: 100%;
    height: 100%;
}



form {
    max-width: 350px;
}

@media (max-width: 768px) {
.fix-quote-logo {
max-width: 300px;
margin-bottom:30px; 
}

body {
    padding-top: 30px;
}
}

.dropdown-icon {
right: 40px !important;
width: 25px !important;
}