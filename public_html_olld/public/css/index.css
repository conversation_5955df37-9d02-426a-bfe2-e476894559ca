/*-------------------------------------
  2. Font-face declarations
--------------------------------------*/
@font-face {
  font-family: 'Nohemi';
  src: url('fonts/Nohemi-Thin.woff2') format('woff2'),
       url('fonts/Nohemi-Thin.woff') format('woff'),
       url('fonts/Nohemi-Thin.ttf') format('truetype');
  font-weight: 100;
  font-style: normal;
}

@font-face {
  font-family: 'Nohemi';
  src: url('fonts/Nohemi-Light.woff2') format('woff2'),
       url('fonts/Nohemi-Light.woff') format('woff'),
       url('fonts/Nohemi-Light.ttf') format('truetype');
  font-weight: 300;
  font-style: normal;
}

@font-face {
  font-family: 'Nohemi';
  src: url('fonts/Nohemi-Regular.woff2') format('woff2'),
       url('fonts/Nohemi-Regular.woff') format('woff'),
       url('fonts/Nohemi-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: 'Nohemi';
  src: url('fonts/Nohemi-Medium.woff2') format('woff2'),
       url('fonts/Nohemi-Medium.woff') format('woff'),
       url('fonts/Nohemi-Medium.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: 'Nohemi';
  src: url('fonts/Nohemi-SemiBold.woff2') format('woff2'),
       url('fonts/Nohemi-SemiBold.woff') format('woff'),
       url('fonts/Nohemi-SemiBold.ttf') format('truetype');
  font-weight: 600;
  font-style: normal;
}

@font-face {
  font-family: 'Nohemi';
  src: url('fonts/Nohemi-Bold.woff2') format('woff2'),
       url('fonts/Nohemi-Bold.woff') format('woff'),
       url('fonts/Nohemi-Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
}

@font-face {
  font-family: 'Nohemi';
  src: url('fonts/Nohemi-ExtraBold.woff2') format('woff2'),
       url('fonts/Nohemi-ExtraBold.woff') format('woff'),
       url('fonts/Nohemi-ExtraBold.ttf') format('truetype');
  font-weight: 800;
  font-style: normal;
}

@font-face {
  font-family: 'Nohemi';
  src: url('fonts/Nohemi-Black.woff2') format('woff2'),
       url('fonts/Nohemi-Black.woff') format('woff'),
       url('fonts/Nohemi-Black.ttf') format('truetype');
  font-weight: 900;
  font-style: normal;
}


body {
  margin: 0;
  padding: 0;
  font-family: Nohemi;
  background-color: #f3f1f5;
}

.thin-text {
  font-family: 'Nohemi';
  font-weight: 100;
}

.light-text {
  font-family: 'Nohemi';
  font-weight: 300;
}

.regular-text {
  font-family: 'Nohemi';
  font-weight: 400;
}

.medium-text {
  font-family: auto;
  font-weight: 500;
}

.semibold-text {
  font-family: 'Nohemi';
  font-weight: 600;
}

.bold-text {
  font-family: 'Nohemi';
  font-weight: 700;
}

.extrabold-text {
  font-family: 'Nohemi';
  font-weight: 800;
}

.black-text {
  font-family: 'Nohemi';
  font-weight: 900;
}

* {
  box-sizing: border-box;
  font-family: inherit;
  margin: unset;
}

a {
  text-decoration: none;
  display: block;
}

img {
  max-width: 100%;
  height: auto;
}

ul {
  margin: 0;
  margin-inline: unset !important;
  padding: 0;
  list-style: none;
}

details {
  width: 100%;
}

details > summary {
  list-style: none;
}

[type="checkbox"] {
  margin: 0px;
}

[type="text"],
input:where(:not([type])),
[type="email"],
[type="url"],
[type="password"],
[type="number"],
[type="date"],
[type="datetime-local"],
[type="month"],
[type="search"],
[type="tel"],
[type="time"],
[type="week"],
[multiple],
textarea,
select,
button {
  width: 100%;
  appearance: none;
  background-color: transparent;
  border-color: unset;
  border-width: 0;
  border-radius: unset;
  padding: unset;
  font-size: unset;
  line-height: unset;
  color: inherit;
}
input:focus-visible,
[multiple]:focus-visible,
textarea:focus-visible,
select:focus-visible {
  outline: none;
}
