.blog-fix-singe-post-page {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 20px;
}

.blog-fix-rowjuly162024 {
    display: flex;
    justify-content: center; 
    align-items: center;
    margin-bottom: 50px;
}

.blog-fix-columnjuly16202 {
    flex: 1;
    max-width: 50%;
    margin-top: 0;
}


.blog-fix-july162024 {
    font-size: 24px !important;
    font-weight: 17.5px !important;
    line-height: 300;
    color:#222A31;
    @media only screen and (max-width: 768px) {
        font-size: 14px !important;
        line-height: 17.5px !important;
    }
}

.blog-fix-columnjuly16202 h1 {
    font-size: 48px;
    font-weight: 500;
    line-height: 48.22px;
    color: #222A31;
    @media only screen and (max-width: 768px) {
        font-size: 24px;
        line-height: 24px;
    }
}

.blog-fix-stackview {
    flex: 1;
    margin-left: 20px;
}

.blog-fix-stackview img {
    width: 100%;
    border-radius: 8px;
    object-fit: cover;
    height: auto;
    max-width: 100%;
    margin-right: 20px;
}

.blog-fix-columntableof {
    display: flex;
    justify-content: space-between;
    gap: 40px; 
    margin-bottom: 40px;
    align-items: flex-start;
}

.blog-fix-columntableof-1, .blog-fix-columntableof-2 {
    flex: 1;
}

.blog-fix-columntableof-1 {
    background-color: #EBE2D8;
    padding: 20px;
    border-radius: 8px;
    max-width: 50%;
}

.blog-fix-columntableof-1 h2 {
    font-size: 24px !important;
    font-weight: 700;
    line-height: 24.11px;
    margin-bottom: 20px;
    color: #222A31;
}

.blog-fix-columntableof-1 p {
    font-size: 16px;
    font-weight: 400 !important;
    line-height: 16.07px !important;
    margin-bottom: 10px;
}

.blog-fix-columntableof-2 {
    background-color: #222A31;
    color: white;
    padding: 30px;
    border-radius: 8px;
    text-align: center;
    max-width: 50%;
    min-height: 200px;
}

.blog-fix-columntableof-2 p {
    font-size: 15px;
    font-weight: 300;
    line-height: 15.07px;
    margin-bottom: 20px;
    color: #fff;
    display: inline;
}

.blog-fix-columntableof-2 h2 {
    margin-top: 10px;
    font-size: 18px;
    font-weight: 700;
    line-height: 24.11px;
    margin-bottom: 20px;
    color: #fff;
}

.blog-fix-quote-button {
    background-color: #CDA565;
    color: #222A31;
    padding: 15px 20px;
    border: none;
    font-weight: 700;
    font-size: 14.92px;
    cursor: pointer;
    max-width: 250px;
}

.blog-fix-quote-button:hover {
    background-color: #c2a060;
    color: #fff;
}

.blog-fix-full-text {
    line-height: 1.7;
    margin-top: 40px;
}

.blog-fix-full-text p {
    margin-bottom: 20px;
    color: #222A31;
    font-size: 24px;
    font-weight: 300;
    line-height: 36px;
}

.blog-fix-full-text h2 {
    font-size: 24px;
    margin-bottom: 20px;
}

.blog-fix-description-span {
    color: #007bff;
}

@media (max-width: 768px) {
    .blog-fix-rowjuly162024 {
        flex-direction: column;
    }

    .blog-fix-columnjuly16202 {
        max-width: 100%;
        margin-bottom: 20px;
        margin-top: -5px;
    }

    .blog-fix-stackview {
        margin-left: 0;
    }

    .blog-fix-columntableof {
        flex-direction: column;
    }

    .blog-fix-columntableof-1, .blog-fix-columntableof-2 {
        min-width: 100%;
    }
}