.fix-door-section-container {
    max-width: 1152px;
    width: 100%;
}

@media (max-width: 768px) {
    .fix-door-section-container {
        grid-template-columns: 1fr; 
        justify-items: center; 
    }
}

.fix-door-section-title {
    text-align: center;
    font-size: 38px;
    font-weight: 700;
    margin-bottom: 40px;
    max-width: 705px;
    margin: 0 auto;
    color: var(--blue_gray_900);
    @media (max-width: 768px) {
        font-size: 26px !important;
    }
}

.fix-door-features-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 40px;
    margin-top: 50px;
}


.fix-door-feature-box {
    background-color: #EBE2D8;
    color: #222A31;
    padding: 20px;
    border-radius: 8px;
    max-width: 564px;
    max-height: 100%;
}

.fix-door-feature-box:hover {
    cursor: pointer;
    background-color: #222A31;
    color: #EBE2D8;
}

.fix-door-feature-box:hover h3,
.fix-door-feature-box:hover a,
.fix-door-feature-box:hover p {
    color: #EBE2D8;
}



.fix-door-feature-box h3 {
    font-size: 20px;
    margin-left: 20px;
    margin-top: 20px;
    color: #222A31;
    @media (max-width: 768px) {
        font-size: 18px;
    }
}

.fix-door-feature-box p {
    font-size: 16px;
    line-height: 26px;
    font-weight: 300;
    margin-left: 20px;
    margin-top: 30px;
    @media (max-width: 768px) {
        font-size: 15px;
    }
}

.fix-door-grid-container {
    display: flex;
    flex-direction: column;
    gap: 40px;
    max-width: 1150px;
}

@media (max-width: 1000px) {
    .fix-door-grid-container {
        margin-top:30px;
        grid-template-columns: 1fr; 
        justify-items: center; 
    }
}


.fix-door-grid-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.fix-door-grid-item img {
    width: 100%;
    height: 350px;
    max-width: 450px;
    border-radius: 8px;
}

.fix-door-grid-text {
    padding: 20px;
    line-height: 26px;
    flex: 1;
    @media (max-width: 1000px) {
        line-height: 20px;
        text-align: left;
    }
}

.fix-door-grid-text h3 {
    font-size: 32px;
    font-weight: 700;
    line-height: 36.16px;
    margin-bottom: 10px;
    color: #222A31;
    padding-right: 200px;
    @media (max-width: 1000px) {
        font-size: 20px;
        line-height: 20.09px;
        padding-right: 0px;
    }
}

.fix-door-grid-text p {
    font-size: 16px;
    line-height: 26px;
    font-weight: 300;
    color: var(--black_900_7f) !important;
    padding-right: 200px;
    @media (max-width: 1000px) {
        font-size: 14px;
        line-height: 17.5px;
        padding-right: 0px;
        text-align: justify;
    }
}

.fix-door-grid-text-c h3 {
    font-size: 36px;
    font-weight: 700;
    line-height: 36.16px;
    margin-bottom: 10px;
    color: #222A31;
    margin-left: 200px;
    @media (max-width: 1000px) {
        font-size: 20px;
        line-height: 20.09px;
        margin-left: 0;
        text-align: left;
    }
}

.fix-door-grid-text-c p {
    font-size: 16px;
    line-height: 26px;
    font-weight: 300;
    color: var(--black_900_7f) !important;;
    margin-left: 200px;
    @media (max-width: 1000px) {
        font-size: 14px;
        line-height: 20px;
        margin-left: 0;
        text-align: left;
    }
}

.fix-door-image-left {
    flex-direction: row-reverse;
}

@media (max-width: 1000px) {
    .fix-door-features-grid {
        margin-top: 20px;
    }

    .fix-door-grid-item {
        flex-direction: column;
        text-align: center;
    }

    .fix-door-grid-item img {
        margin-bottom: 20px;
        max-width: 100%;
        height: 255px;
    }

    .fix-door-grid-text {
        padding: 10px;
    }
}

@media (max-width: 768px) {
    .fix-door-features-grid {
        grid-template-columns: 1fr;
    }
}

.fix-benefit-mob-desk-only {
    margin-bottom: 50px !important;
    margin-top: -50px;
}

.column_eight.door {
    margin-top: 70px !important;
}

.door-margin-benefit {
    margin-top: -30px;
    @media (max-width: 768px) {
        margin-top: 0px;
    }
}

.door-margin-slider {
    margin-bottom: 0px;
    margin-top: 50px;
    @media (max-width: 768px) {
        margin-top: 0px;
        margin-bottom: 0px;
    }
}

.door-margin-faq {
    margin-bottom: 0px;
    margin-top: 70px;
    @media (max-width: 768px) {
        margin-top: -10px;
        margin-bottom: 0px;
    }
}

.pt {
    font-family: 'Nohemi';
    font-size: 18px;
    font-weight: 300;
    color: #00000080;
    line-height: 26px;
    margin-left: 15px;
}