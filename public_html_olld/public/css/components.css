/*=========== TABLE OF CONTENTS ===========
1. Text CSS
2. Heading CSS
3. Container CSS
==========================================*/

/*-------------------------------------
  1. Text CSS
--------------------------------------*/
.ui.text.size-textxs {
  font-size: 14px;
  font-weight: 400;
  font-style: normal;
}


.ui.text.size-texts {
  font-family: 'Nohemi';
  font-size: 16px;
  font-weight: 300;
  font-style: bold;
  line-height: 26px;
  @media only screen and (max-width: 550px) {
    font-size: 14px !important;
    line-height: 20.8px !important;
  }
}

.experience {
  @media only screen and (max-width: 550px) {
    margin-top:30px !important;
  }
}

.ui.text.size-textmd {
  font-family: 'Nohemi';
  font-size: 18px;
  font-weight: 300;
  font-style: bold;
  line-height: 26px;
  @media only screen and (max-width: 1175px) {
    font-size: 17px;
    line-height: 26px;
    margin-left: auto;
    text-align: justify;
  }
  @media only screen and (max-width: 550px) {
    font-size: 16px;
    text-align: left;
    line-height: 20px;
    margin-top: -15px;
    text-align: justify;
  }
}

.ui.text.size-textmd.center {
  @media only screen and (max-width: 768px) {
    text-align: center;
  }
}

.ui.text.size-textlg {
  font-family: 'Nohemi';
  font-size: 24px;
  font-weight: 300;
  font-style: bold;
  @media only screen and (max-width: 1175px) {
    font-size: 22px;
  }

  @media only screen and (max-width: 550px) {
    font-size: 20px;
  }
}

.ui.text {
  font-family: 'Nohemi';
  color: var(--blue_gray_900);
  font-size: 16px;
  font-weight: 300;
  line-height: 26px;
}

/*-------------------------------------
  2. Heading CSS
--------------------------------------*/
.ui.heading.size-headingxs {
  font-family: 'Nohemi';
  font-size: 18px;
  font-weight: 500;
  font-style: bold;
  @media only screen and (max-width: 550px) {
    font-size: 20px;
    font-weight: 700;
  }
}


.ui.heading.size-headingxsh {
  font-family: 'Nohemi';
  font-size: 16px;
  line-height: 16px;
  font-weight: 600;
  @media only screen and (max-width: 550px) {
    font-size: 20px;
    font-weight: 700;
  }
}

.ui.text.size-textxsh {
  font-size: 12px;
  line-height: 12px;
  font-weight: 300;
  font-style: normal;
}

.ui.heading.size-headingxs.left {
  margin-left: 95px;
  @media only screen and (max-width: 550px) {
    margin-left: 135px;
  }
}

.ui.heading.size-headingm {
  font-family: 'Nohemi';
  font-size: 20px;
  font-weight: 700;
  font-style: bold;
  @media only screen and (max-width: 550px) {
    font-size: 20px;
    font-weight: 700;
  }
}

.ui.heading.size-headingm.margin-top {
  @media only screen and (max-width: 550px) {
    margin-top:15px;
  }
}

.ui.heading.size-headings {
  font-family: 'Nohemi';
  font-size: 23px;
  font-weight: 700;
  font-style: bold;
  margin-left: -20px;
  @media only screen and (max-width: 1175px) {
    font-size: 21px;
  }

  @media only screen and (max-width: 550px) {
    font-size: 19px;
  }
}

.ui.heading.size-headingmd {
  font-family: 'Nohemi';
  font-size: 32px;
  font-weight: 700;
  font-style: bold;
  @media only screen and (max-width: 1175px) {
    font-size: 30px;
  }

  @media only screen and (max-width: 768px) {
    font-size: 27px;
  }
}

.ui.heading.size-headinglg {
  font-family: 'Nohemi';
  font-size: 36px;
  font-weight: 700;
  font-style: bold;
  @media only screen and (max-width: 1175px) {
    font-size: 34px;
  }

  @media only screen and (max-width: 550px) {
    font-size: 30px;
  }
}

.ui.heading.size-headingxl.one { 
  @media only screen and (max-width: 550px) {
    font-size: 24px !important;
    line-height: 25px;
    font-weight: 700;
  }
}

.ui.heading.size-headingxl.two { 
  @media only screen and (max-width: 550px) {
    font-size: 27px !important;
    line-height: 30px;
    font-weight: 700;
  }
  @media only screen and (max-width: 1175px) {
    font-size: 27px !important;
    line-height: 30px;
    font-weight: 700;
  }
}


.ui.heading.size-headingxl {
  font-family: 'Nohemi';
  font-size: 38px;
  font-weight: 700;
  font-style: bold;
  @media only screen and (max-width: 1175px) {
    font-size: 30px;
  }

  @media only screen and (max-width: 550px) {
    font-size: 22px;
    line-height: 22.1px;
    font-weight: 700;
  }
}

.ui.heading.size-heading2xl {
  font-family: 'Nohemi';
  font-size: 50px;
  font-weight: 700;
  font-style: bold;
  @media only screen and (max-width: 1175px) {
    font-size: 46px;
    text-align: center;
  }

  @media only screen and (max-width: 550px) {
        font-size: 24px !important;
        text-align: center;
        line-height: 22.1px;
        margin-top:15px;
  }
}

.ui.heading.size-heading2xl.margin {
  @media only screen and (max-width: 550px) {
    margin-top: -90px;
}
}

.columnquicksecu.margin-right {
  margin-right: 100px;
  @media only screen and (max-width: 550px) {
    margin-right: 0px;
}
}

.ui.heading {
  font-family: 'Nohemi';
  color: var(--blue_gray_900);
}

/*-------------------------------------
  3. Container CSS
--------------------------------------*/
.container-xs {
  font-family: 'Nohemi';
  max-width: 1152px;
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  @media only screen and (max-width: 1175px) {
    padding-left: var(--space-5xl);
    padding-right: var(--space-5xl);
}
}


.slider-pad {
  @media (max-width: 1175px) {
    margin-top: 20px;
    padding-bottom:20px;
}
}