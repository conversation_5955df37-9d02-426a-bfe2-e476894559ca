/*=========== TABLE OF CONTENTS ===========
1. Common CSS
2. Page CSS
==========================================*/


.columnview {
  gap: var(--space-2xl);
  display: flex;
  width: 100%;
  flex-direction: column;
}

.stackview {
  height: 352px;
  position: relative;
  align-content: center;
  @media only screen and (max-width: 1050px) {
    height: auto;
  }
}

.image-1 {
  height: 352px;
  flex: 1;
  object-fit: cover;
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  border-radius: var(--radius-sm);
}

.view {
  height: 352px;
  background: linear-gradient(180deg, #222a3100, #1a20257f);
  flex: 1;
  position: absolute;
  left: 0px;
  bottom: 0px;
  right: 0px;
  top: 0px;
  margin: auto;
  border-radius: var(--radius-sm);
}

.columnhowtochan-1 {
  gap: var(--space-xs);
  display: flex;
  flex-direction: column;
  align-items: start;
}

.howtochange-1 {
  width: 100%;
  line-height: 24px;
  font-size: 24px;
  font-weight: 500;
  color: #222A31;
}

.description {
  color: var(--blue_gray_900_7f) !important;
  width: 100%;
  line-height: 17.5px;
  font-size: 16px;
  font-weight: 400 !important;
}

.three {
  margin-left: 50px;
  @media only screen and (max-width: 550px) {
    margin-left: 0px;
  }
}

.columnhome_one {
  gap: var(--space-lg);
  display: flex;
  flex-direction: column;
  align-items: start;
}

.description-9 {
  color: var(--black_900) !important;
  font-weight: 300 !important;
}

.navigation {
  color: var(--black_900_7f) !important;
}

.socialicon_one {
  height: 24px;
  width: 24px;
}

.copyright2024 {
  color: var(--black_900_7f) !important;
  font-weight: 400 !important;
  font-size: 14px;
  font-weight: 17.5px;
}

/*-------------------------------------
  2. Page CSS
--------------------------------------*/
.blog {
  background-color: var(--gray_100);
  width: 100%;
}

.columnourblog {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-left: 20px;
  margin-right: 20px;
}

.ourblog {
  z-index: 1;
  position: relative;
}

.lateststories {
  font-size: 50.8px !important;
  @media only screen and (max-width: 550px) {
    font-size: 32px !important;
  }
}

.search {
  color: var(--blue_gray_900);
  margin-top: 15px;
  padding-left: 24px;
  padding-right: 24px;
  padding-top: 15px;
  font-size: 16px;
  width: 90%;
  max-width: 500px;
  align-items: center;
  justify-content: center;
  height: 50px;
  border-radius: var(--radius-lg);
  border: 1px solid var(--blue_gray_900);
  @media only screen and (max-width: 550px) {
    padding-left: var(--space-3xl);
    padding-right: var(--space-3xl);
  }
}

.rowhowtochange {
  margin-top: 58px;
  background-size: cover;
  background-repeat: no-repeat;
  align-self: stretch;
  border-radius: var(--radius-sm);
  position: relative;
}


.columnhowtochan {
  background: linear-gradient(180deg, #222a3100, #1a20257f);
  display: flex;
  flex-direction: column;
  align-items: start;
  justify-content: flex-end;
  padding: 56px 56px 56px 94px;
  border-radius: var(--radius-sm);
  @media only screen and (max-width: 1050px) {
    padding: var(--space-3xl);
  }
}

.howtochange {
  color: var(--white_a700) !important;
  margin-top: 376px;
  width: 70%;
  font-size: 36px;
  line-height: 36.16px;
  font-weight: 500;
  @media only screen and (max-width: 1050px) {
    width: 100%;
  }

  @media only screen and (max-width: 550px) {
    font-size: 32px;
  }
}

.movingtoanew {
  color: var(--white_a700) !important;
  margin-top: 50px;
  font-weight: 300 !important;
  font-size: 16px;
  width: 74%;
  line-height: 17.5px;
  @media only screen and (max-width: 1050px) {
    width: 100%;
  }
}

.july162024 {
  color: var(--white_a700) !important;
  font-weight: 300 !important;
}

.blog-1 {
  margin-top: 18px;
  margin-left: 20px;
  margin-right: 20px;
  gap: 24px;
  display: grid;
  row-gap: var(--space-3xl);
  grid-template-columns: repeat(3, minmax(0, 1fr));
  justify-content: center;
  @media only screen and (max-width: 1050px) {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  @media only screen and (max-width: 550px) {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
}

.columnview-5 {
  gap: var(--space-2xl);
  display: flex;
  background-color: var(--blue_gray_900);
  width: 100%;
  flex-direction: column;
  padding: 204px 32px;
  border-radius: var(--radius-sm);
  @media only screen and (max-width: 1050px) {
    padding-top: var(--space-3xl);
    padding-bottom: var(--space-3xl);
  }

  @media only screen and (max-width: 550px) {
    padding: var(--space-3xl);
  }
}

.rowofcounter {
  margin-top: 58px;
  margin-left: 240px;
  margin-right: 244px;
  gap: 26px;
  display: flex;
  justify-content: center;
  align-items: center;
  @media only screen and (max-width: 1050px) {
    flex-direction: column;
    margin-left: 0px;
    margin-right: 0px;
  }
}

.rowone {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 5px;
  @media only screen and (max-width: 1050px) {
    align-self: stretch;
  }

  @media only screen and (max-width: 550px) {
    flex-direction: column;
  }
}

.one-1 {
  height: 40px;
  color: var(--white_a700);
  padding-left: var(--space-xl);
  padding-right: var(--space-xl);
  font-size: 16px;
  background-color: var(--blue_gray_900);
  text-align: center;
  min-width: 40px;
  border-radius: var(--radius-sm);
}

.two {
  margin-left: 36px;
  @media only screen and (max-width: 550px) {
    margin-left: 0px;
  }
}

.image {
  height: 3px;
  margin-bottom: 14px;
  margin-left: 26px;
  align-self: end;
  @media only screen and (max-width: 550px) {
    align-self: auto;
    width: 100%;
    margin-left: 0px;
  }
}

.ten {
  margin-left: 28px;
  @media only screen and (max-width: 550px) {
    margin-left: 0px;
  }
}

.eleven {
  margin-left: 32px;
  @media only screen and (max-width: 550px) {
    margin-left: 0px;
  }
}

.class-_ {
  margin-left: 26px;
  @media only screen and (max-width: 550px) {
    margin-left: 0px;
  }
}

.goto {
  margin-left: 22px;
  @media only screen and (max-width: 550px) {
    margin-left: 0px;
  }
}

.one {
  color: var(--blue_gray_900_7f);
  padding-left: 25px;
  padding-right: 25px;
  font-size: 16px;
  text-align: center;
  height: 40px;
  min-width: 62px;
  border-radius: var(--radius-sm);
  border: 1px solid var(--blue_gray_900_7f);
  @media only screen and (max-width: 550px) {
    padding-left: var(--space-3xl);
    padding-right: var(--space-3xl);
  }
}

.footer {
  margin-top: 30px;
  display: flex;
  background-color: var(--gray_300);
  justify-content: center;
  align-items: end;
  padding: var(--space-2xl);
}

.columnfooterlog {
  margin-top: 42px;
  gap: 48px;
  display: flex;
  width: 84%;
  flex-direction: column;
  align-items: center;
  @media only screen and (max-width: 1050px) {
    width: 100%;
  }
}

.rowfooterlogo {
  align-self: stretch;
  display: flex;
  justify-content: space-between;
  align-items: start;
  gap: var(--space-3xl);
  @media only screen and (max-width: 1050px) {
    flex-direction: column;
  }
}

.footerlogo_one {
  height: 120px;
  width: 328px;
  object-fit: contain;
}

.rowcontact_two {
  align-self: center;
  width: 66%;
  display: flex;
  justify-content: center;
  align-items: center;
  @media only screen and (max-width: 1050px) {
    flex-direction: column;
    width: 100%;
  }
}

.rownavigation {
  width: 60%;
  display: flex;
  justify-content: space-between;
  align-items: start;
  gap: var(--space-3xl);
  @media only screen and (max-width: 1050px) {
    width: 100%;
  }

  @media only screen and (max-width: 550px) {
    flex-direction: column;
  }
}

.columnnavigatio {
  gap: var(--space-md);
  display: flex;
  width: 40%;
  flex-direction: column;
  align-items: start;
  @media only screen and (max-width: 550px) {
    width: 100%;
  }
}

.columnservices {
  gap: var(--space-md);
  align-self: center;
  display: flex;
  width: 60%;
  flex-direction: column;
  align-items: start;
  @media only screen and (max-width: 550px) {
    width: 100%;
  }
}

.columnemail {
  width: 38%;
  display: flex;
  flex-direction: column;
  align-items: start;
  @media only screen and (max-width: 1050px) {
    width: 100%;
  }
}

.columncontact {
  gap: var(--space-2xl);
  display: flex;
  align-self: stretch;
  flex-direction: column;
  align-items: start;
}

.description-link {
  line-height: 16px;
}

.email {
  color: var(--black_900) !important;
  margin-top: 20px;
  font-weight: 300 !important;
}

.class-18778788008 {
  color: var(--black_900) !important;
  margin-top: 14px;
  font-weight: 300 !important;
}

.rowsocialicon {
  margin-top: 28px;
  width: 54%;
  display: flex;
  justify-content: space-between;
  gap: var(--space-3xl);
  @media only screen and (max-width: 1050px) {
    width: 100%;
  }
}

.rowcopyright202 {
  margin-left: 34px;
  display: flex;
  justify-content: space-between;
  align-items: start;
  gap: var(--space-3xl);
  @media only screen and (max-width: 1050px) {
    margin-left: 0px;
  }

  @media only screen and (max-width: 550px) {
    flex-direction: column;
  }
}

.rowtermandcondi {
  gap: var(--space-2xl);
  align-self: center;
  display: flex;
  align-items: center;
}

.termandconditio-link {
  align-self: start;
}

.lineone_one {
  height: 28px;
  background-color: var(--black_900);
  width: 1px;
}

.quote-block {
  display: flex;
  flex-direction: column;
  justify-content: center; 
  align-items: center;
  background-color: #222A31;
  color: white;
  padding: 20px;
  text-align: center;
  border-radius: 8px;
}

.quote-block h2 {
  font-size: 24px;
  font-weight: 700;
  line-height: 24.11px;
  color: #FFFFFF;
  margin-bottom: 10px;
  @media only screen and (max-width: 768px) {
    margin-top: 20px;
    font-size: 20px;
    line-height: 20.09px;
  }
}

.quote-block p {
  font-weight: 300;
  font-size: 15px;
  line-height: 15.07px;
  color: #FFFFFF;
  margin-bottom: 20px;
  @media only screen and (max-width: 768px) {
    margin-top: 20px;
    font-size: 14px;
    line-height: 14px;
  }
}

.quote-block button {
  background-color: #CDA565;
  color: #222A31;
  padding: 20px 20px;
  border: none;
  cursor: pointer;
  font-weight: 700;
  font-size: 15px;
  line-height: 14.98px;
  @media only screen and (max-width: 768px) {
    max-width: 188px;
    margin-top: 20px;
    margin-bottom: 20px;
    font-size: 14px;
    line-height: 14px;
  }
}

.quote-block button:hover {
  color: #FFF;
}