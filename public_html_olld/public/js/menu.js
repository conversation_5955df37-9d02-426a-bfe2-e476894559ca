document.addEventListener('DOMContentLoaded', function() {
    const dropdowns = document.querySelectorAll('.dhi-group');
    let fixedDropdown = null;
  
    dropdowns.forEach(function(dropdown) {
      const dropdownMenu3 = dropdown.querySelector('.dhi-group-3');
      const dropdownMenu2 = dropdown.querySelector('.dhi-group-2');
  
      dropdown.addEventListener('mouseenter', function() {
        if (fixedDropdown === dropdown) return;
        if (dropdownMenu3) dropdownMenu3.style.display = 'flex';
        if (dropdownMenu2) dropdownMenu2.style.display = 'flex';
      });
  
      dropdown.addEventListener('mouseleave', function() {
        if (fixedDropdown === dropdown) return;
        if (dropdownMenu3) dropdownMenu3.style.display = 'none';
        if (dropdownMenu2) dropdownMenu2.style.display = 'none';
      });
  
      dropdown.addEventListener('click', function(e) {
        e.stopPropagation();
  
        if (fixedDropdown === dropdown) {
          if (dropdownMenu3) dropdownMenu3.style.display = 'none';
          if (dropdownMenu2) dropdownMenu2.style.display = 'none';
          fixedDropdown = null;
          return;
        }
  
        if (fixedDropdown && fixedDropdown !== dropdown) {
          const previousDropdownMenu3 = fixedDropdown.querySelector('.dhi-group-3');
          const previousDropdownMenu2 = fixedDropdown.querySelector('.dhi-group-2');
  
          if (previousDropdownMenu3) previousDropdownMenu3.style.display = 'none';
          if (previousDropdownMenu2) previousDropdownMenu2.style.display = 'none';
        }
  
        fixedDropdown = dropdown;
      });
    });
  
    document.addEventListener('click', function() {
      if (fixedDropdown) {
        const dropdownMenu3 = fixedDropdown.querySelector('.dhi-group-3');
        const dropdownMenu2 = fixedDropdown.querySelector('.dhi-group-2');
  
        if (dropdownMenu3) dropdownMenu3.style.display = 'none';
        if (dropdownMenu2) dropdownMenu2.style.display = 'none';
  
        fixedDropdown = null;
      }
    });
  });
  

  document.getElementById("burgerMenu").addEventListener("click", function() {
    document.getElementById("mobileMenu").style.display = "flex";
  });
  
  document.getElementById("closeMenu").addEventListener("click", function() {
    document.getElementById("mobileMenu").style.display = "none";
  });
  
  function toggleDropdown(id, arrowId) {
    var dropdowns = ["services-dropdown", "how-it-works-dropdown"];
    
    dropdowns.forEach(function(dropdownId) {
      if (dropdownId !== id) {
        var otherDropdown = document.getElementById(dropdownId);
        if (otherDropdown && otherDropdown.style.display === "block") {
          otherDropdown.style.display = "none";
          
          var otherArrow = document.getElementById(otherDropdown.getAttribute("data-arrow"));
          if (otherArrow) {
            otherArrow.classList.remove("rotated");
          }
        }
      }
    });
  
    var dropdown = document.getElementById(id);
    var arrow = document.getElementById(arrowId);
    
    if (dropdown.style.display === "block") {
      dropdown.style.display = "none";
      arrow.classList.remove("rotated");
    } else {
      dropdown.style.display = "block";
      arrow.classList.add("rotated");
    }
  }