$(function () {
    function initializeAutocomplete(inputElement) {
      const autocomplete = new google.maps.places.Autocomplete(inputElement, {
        types: ['(regions)'],
        componentRestrictions: { country: 'us' }
      });

      autocomplete.addListener('place_changed', function () {
        const place = autocomplete.getPlace();
        if (place.address_components) {
          let city = '';
          let state = '';
          let zip = '';

          place.address_components.forEach(component => {
            const types = component.types;
            if (types.includes('locality') || types.includes('postal_town')) {
              city = component.long_name;
            }
            if (types.includes('administrative_area_level_1')) {
              state = component.short_name;
            }
            if (types.includes('postal_code')) {
              zip = component.long_name;
            }
          });

          const formatted = `${city}, ${state} ${zip}, USA`;
          $(inputElement).val(formatted);
        }
      });
    }

    initializeAutocomplete(document.getElementById('transport_from'));
    initializeAutocomplete(document.getElementById('transport_to'));

    window.nextStep = function (step) {
      $('.step').hide();
      $('#step-' + step).show();
    };
  });