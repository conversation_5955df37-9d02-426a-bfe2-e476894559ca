function countUp(elementId, endValue, duration, isSlowToEight = false) {
    let currentValue = 0;
    const element = document.getElementById(elementId);
    const interval = isSlowToEight ? 200 : 20; // ნელი განახლების ინტერვალი `years`-ის 8-მდე ასვლისთვის
    const slowIncrement = 1; // თითოეულ განახლებაზე 1-ით გაზრდა 8-მდე ასვლისთვის
    const fastIncrement = isSlowToEight ? Math.ceil((endValue - 8) / ((duration - 1000) / interval)) : Math.ceil(endValue / (duration / interval));

    function updateCounter() {
        element.innerHTML = currentValue.toLocaleString() + '+';

        if (isSlowToEight && currentValue < 8) {
            currentValue += slowIncrement;
            setTimeout(updateCounter, interval);
        } else if (currentValue < endValue) {
            currentValue += fastIncrement;
            if (currentValue > endValue) currentValue = endValue;
            setTimeout(updateCounter, interval / (isSlowToEight ? 2 : 2)); // უფრო სწრაფი განახლება 8-ის შემდეგ
        } else {
            element.innerHTML = endValue.toLocaleString() + '+';
        }
    }

    updateCounter();
}

document.addEventListener("DOMContentLoaded", function() {
    const elementsToAnimate = [
        { id: "years", endValue: 8, isSlowToEight: true },
        { id: "cars", endValue: 135000 },
        { id: "carriers", endValue: 27000 },
        { id: "shipments", endValue: 40000 }
    ];

    const observerOptions = {
        threshold: 0.1 // მინიმუმ 10%-ით არეალში, რომ დაიწყოს ათვლა
    };

    const observer = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const elementToAnimate = elementsToAnimate.find(e => e.id === entry.target.id);
                if (elementToAnimate) {
                    countUp(elementToAnimate.id, elementToAnimate.endValue, 3000, elementToAnimate.isSlowToEight);
                    observer.unobserve(entry.target);
                }
            }
        });
    }, observerOptions);

    elementsToAnimate.forEach(item => {
        const element = document.getElementById(item.id);
        if (element) observer.observe(element);
    });
});





function scrollToTop() {
    window.scrollTo({
      top: 0,
      behavior: 'smooth' 
    });
}

function redirectToBlog() {
    window.location.href = 'blog.php'; 
}

function formatPhoneNumber(input) {
    let phoneNumber = input.value.replace(/\D/g, '');
    if (phoneNumber.length > 10) {
      phoneNumber = phoneNumber.slice(0, 10);
    }
    if (phoneNumber.length <= 3) {
      input.value = '(' + phoneNumber;
    } else if (phoneNumber.length <= 6) {
      input.value = '(' + phoneNumber.slice(0, 3) + ') ' + phoneNumber.slice(3);
    } else {
      input.value = '(' + phoneNumber.slice(0, 3) + ') ' + phoneNumber.slice(3, 6) + '-' + phoneNumber.slice(6);
    }
}

function showMessage() {
    const successMessage = document.querySelector('.success-message');
    const errorMessage = document.querySelector('.error-message');

    if (successMessage) {
        successMessage.style.display = 'block';
        setTimeout(function() {
            successMessage.classList.add('fade-out');
        }, 3000);
    }

    if (errorMessage) {
        errorMessage.style.display = 'block';
        setTimeout(function() {
            errorMessage.classList.add('fade-out');
        }, 3000);
    }
}

window.onload = showMessage;
