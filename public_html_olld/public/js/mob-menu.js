document.getElementById("burgerMenu").addEventListener("click", function() {
  var mobileMenu = document.getElementById("mobileMenu");
  document.body.classList.add("menu-open"); // გვერდის გადაადგილება არ მოხდება
  mobileMenu.style.display = "flex"; // მენიუს გამოჩენა
  setTimeout(function() {
    mobileMenu.classList.add("open");
  }, 10);
});

document.getElementById("closeMenu").addEventListener("click", function() {
  var mobileMenu = document.getElementById("mobileMenu");
  mobileMenu.classList.remove("open"); // მენიუს დახურვა
  document.body.classList.remove("menu-open"); // გვერდის გადაადგილების ხელახლა დაშვება
  setTimeout(function() {
    mobileMenu.style.display = "none"; // მენიუს გამორთვა
  }, 1000); // ანიმაციის დასრულება
});

  
  function toggleDropdown(id, arrowId) {
    var dropdowns = ["services-dropdown", "how-it-works-dropdown"];
    
    dropdowns.forEach(function(dropdownId) {
      if (dropdownId !== id) {
        var otherDropdown = document.getElementById(dropdownId);
        if (otherDropdown && otherDropdown.style.display === "block") {
          otherDropdown.style.display = "none";
          
          var otherArrow = document.getElementById(otherDropdown.getAttribute("data-arrow"));
          if (otherArrow) {
            otherArrow.classList.remove("rotated");
          }
        }
      }
    });
  
    var dropdown = document.getElementById(id);
    var arrow = document.getElementById(arrowId);
    
    if (dropdown.style.display === "block") {
      dropdown.style.display = "none";
      arrow.classList.remove("rotated");
    } else {
      dropdown.style.display = "block";
      arrow.classList.add("rotated");
    }
  }