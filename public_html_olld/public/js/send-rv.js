// 1. Populate Vehicle Years
function populateYears() {
    const yearSelect = document.getElementById("vehicle_year");
    for (let year = 2025; year >= 1900; year--) {
        const option = document.createElement("option");
        option.value = year;
        option.textContent = year;
        yearSelect.appendChild(option);
    }
}

// 2. Populate Vehicle Brands
const topBrands = [
    "Toyota", "Honda", "Ford", "Tesla", "BMW", "Mercedes-Benz", "Chevrolet", "Nissan",
    "Volkswagen", "Hyundai", "Kia", "Audi", "Mazda", "Jeep", "Subaru", "Buick",
    "Dodge", "Chrysler", "Volvo", "Land Rover", "Jaguar", "Mitsubishi", "Lexus",
    "Infiniti", "Acura", "Cadillac", "Lincoln", "Porsche", "Mini", "Fiat", "Alfa Romeo",
    "Maserati", "Ferrari", "Lamborghini", "Bentley", "Rolls-Royce", "Genesis",
    "GMC", "Ram", "Suzuki", "Isuzu", "Peugeot", "Renault", "Citroën", "Saab",
    "Scion", "Saturn", "Pontiac", "Oldsmobile", "Hummer", "Wagoneer"
].sort();

function populateBrands() {
    const brandSelect = document.getElementById("vehicle_brand");
    topBrands.forEach(brand => {
        const option = document.createElement("option");
        option.value = brand;
        option.textContent = brand;
        brandSelect.appendChild(option);
    });
}

// 3. Populate Vehicle Models
function populateModels(brand) {
    const modelSelect = document.getElementById("vehicle_model");
    modelSelect.innerHTML = '<option value="" disabled selected>Vehicle model</option>'; 
    
    if (brand === "Wagoneer") {
        const wagoneerModels = ["Grand Wagoneer", "Grand Wagoneer L", "Wagoneer", "Wagoneer L"];
        wagoneerModels.forEach(modelName => {
            const option = document.createElement("option");
            option.value = modelName;
            option.textContent = modelName;
            modelSelect.appendChild(option);
        });
        modelSelect.disabled = false; 
    } else if (brand) {
        fetch(`https://vpic.nhtsa.dot.gov/api/vehicles/GetModelsForMake/${brand}?format=json`)
            .then(response => response.json())
            .then(data => {
                const models = data.Results;
                models.forEach(model => {
                    const option = document.createElement("option");
                    option.value = model.Model_Name;
                    option.textContent = model.Model_Name;
                    modelSelect.appendChild(option);
                });
                modelSelect.disabled = false; 
            })
            .catch(error => {
                console.error('Error fetching vehicle models:', error);
            });
    }
}

// 4. Enable/Disable Dropdowns
document.addEventListener("DOMContentLoaded", function() {
    populateYears();
    populateBrands();

    const yearSelect = document.getElementById("vehicle_year");
    const brandSelect = document.getElementById("vehicle_brand");
    const modelSelect = document.getElementById("vehicle_model");

    yearSelect.addEventListener("change", function() {
        brandSelect.disabled = false;
        modelSelect.disabled = true;
    });

    brandSelect.addEventListener("change", function() {
        const selectedBrand = brandSelect.value;
        populateModels(selectedBrand);
        if (selectedBrand === "Wagoneer") {
            modelSelect.disabled = false;
        } else {
            modelSelect.disabled = true;
        }
    });
});

// 5. City Validation
function isValidCity(city) {
    // ქალაქის სახელი უნდა შედგებოდეს მინიმუმ 2 ასოსგან
    const cityPattern = /^[A-Za-z\s]{2,}$/;
    return cityPattern.test(city);
}

// 6. ZIP Code Validation
function isValidZipCode(zipCode) {
    // ზიპ კოდი უნდა შედგებოდეს ზუსტად 5 ციფრისგან
    const zipCodePattern = /^\d{5}$/;
    return zipCodePattern.test(zipCode);
}

// 7. Location Validation
function isValidLocation(location) {
    // დაყოფა city და zipCode-ად
    const parts = location.split(",").map(part => part.trim());
    const city = parts[0];
    const state = parts[1]?.split(" ")[0]; // შტატის აბრევიატურა
    const zipCode = parts[1]?.split(" ")[1]; // ზიპ კოდი
    const country = parts[2]; // ქვეყანა

    // ქალაქი უნდა იყოს სწორად შეყვანილი
    const isCityValid = isValidCity(city);

    // ზიპ კოდი უნდა იყოს სწორად შეყვანილი (თუ არსებობს)
    const isZipCodeValid = !zipCode || isValidZipCode(zipCode);

    // ქვეყანა უნდა იყოს სწორად შეყვანილი
    const isCountryValid = country && /^[A-Za-z\s]+$/.test(country);

    // ან ქალაქი, ან ზიპ კოდი უნდა იყოს სწორად შეყვანილი
    return (isCityValid || isZipCodeValid) && isCountryValid;
}

// 8. Step 1 Validation
function isStepOneValid() {
    let isValid = true;

    // Transport From Validation
    const from = document.getElementById("transport_from").value.trim();
    const fromWrapper = document.getElementById("transport_from").closest(".input-wrapper");
    if (!isValidLocation(from)) {
        fromWrapper.classList.add("input-error");
        showNotification("Please enter a valid Zip or City", "error");
        isValid = false;
    } else {
        fromWrapper.classList.remove("input-error");
    }

    // Transport To Validation
    const to = document.getElementById("transport_to").value.trim();
    const toWrapper = document.getElementById("transport_to").closest(".input-wrapper");
    if (!isValidLocation(to)) {
        toWrapper.classList.add("input-error");
        showNotification("Please enter a valid Zip or City", "error");
        isValid = false;
    } else {
        toWrapper.classList.remove("input-error");
    }

    return isValid;
}

// 9. Step 2 Validation
function isStepTwoValid() {
    let isValid = true;

    // Vehicle Year Validation
    const vehicleYear = document.getElementById("vehicle_year").value;
    const vehicleYearWrapper = document.getElementById("vehicle_year").closest(".styled-select");
    if (!vehicleYear) {
        vehicleYearWrapper.classList.add("input-error");
        showNotification("Please select a vehicle year", "error");
        isValid = false;
    } else {
        vehicleYearWrapper.classList.remove("input-error");
    }

    // Vehicle Brand Validation
    const vehicleBrand = document.getElementById("vehicle_brand").value;
    const vehicleBrandWrapper = document.getElementById("vehicle_brand").closest(".input-wrapper");
    if (!vehicleBrand) {
        vehicleBrandWrapper.classList.add("input-error");
        showNotification("Please select a vehicle brand", "error");
        isValid = false;
    } else {
        vehicleBrandWrapper.classList.remove("input-error");
    }

    // Vehicle Model Validation
    const vehicleModel = document.getElementById("vehicle_model").value;
    const vehicleModelWrapper = document.getElementById("vehicle_model").closest(".input-wrapper");
    if (!vehicleModel) {
        vehicleModelWrapper.classList.add("input-error");
        showNotification("Please select a vehicle model", "error");
        isValid = false;
    } else {
        vehicleModelWrapper.classList.remove("input-error");
    }

    // Operable Validation
    const operable = document.querySelector('input[name="vehicle_operable"]:checked');
    if (!operable) {
        document.querySelector('input[name="vehicle_operable"]').closest(".rowtransporttyp").classList.add("radio-error");
        showNotification("Please select if the vehicle is operable", "error");
        isValid = false;
    } else {
        document.querySelector('input[name="vehicle_operable"]').closest(".rowtransporttyp").classList.remove("radio-error");
    }

    return isValid;
}

// 10. Phone Number Formatting
document.getElementById("phone").addEventListener("input", function() {
    // წაშალე ყველა არა-ციფრული სიმბოლო
    let input = this.value.replace(/\D/g, '');

    // შეზღუდე ნომრის სიგრძე 10 ციფრამდე
    if (input.length > 10) input = input.substring(0, 10);

    // ფორმატირება (XXX) XXX-XXXX
    const formattedPhone = input.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
    this.value = formattedPhone;
});

// 11. Phone Number Validation
function isValidPhoneNumber(phone) {
    // ნომერი უნდა შეესაბამებოდეს ფორმატს (XXX) XXX-XXXX
    const phonePattern = /^\(\d{3}\) \d{3}-\d{4}$/;
    return phonePattern.test(phone);
}

// 12. Step 3 Validation
function isStepThreeValid() {
    let isValid = true;

    // Email Validation
    const email = document.getElementById("email").value.trim();
    const emailWrapper = document.getElementById("email").closest(".input-wrapper");
    if (email === "" || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
        emailWrapper.classList.add("input-error");
        showNotification("Please enter a valid email", "error");
        isValid = false;
    } else {
        emailWrapper.classList.remove("input-error");
    }

    // Available Date Validation
    const availableDate = document.getElementById("available_date").value;
    const dateWrapper = document.getElementById("available_date").closest(".select-custom");
    if (availableDate === "") {
        dateWrapper.classList.add("input-error");
        showNotification("Please select a ship date", "error");
        isValid = false;
    } else {
        dateWrapper.classList.remove("input-error");
    }

    // Name Validation
    const name = document.getElementById("name").value.trim();
    const nameWrapper = document.getElementById("name").closest(".input-wrapper");
    if (name === "") {
        nameWrapper.classList.add("input-error");
        showNotification("Please enter your full name", "error");
        isValid = false;
    } else {
        nameWrapper.classList.remove("input-error");
    }

    // Phone Validation
    const phone = document.getElementById("phone").value.trim();
    const phoneWrapper = document.getElementById("phone").closest(".input-wrapper");
    if (!isValidPhoneNumber(phone)) {
        phoneWrapper.classList.add("input-error");
        showNotification("Please enter a valid phone number", "error");
        isValid = false;
    } else {
        phoneWrapper.classList.remove("input-error");
    }

    return isValid;
}

// 2. Phone Number Formatting
document.getElementById("phone").addEventListener("input", function() {
    // წაშალე ყველა არა-ციფრული სიმბოლო
    let input = this.value.replace(/\D/g, '');

    // შეზღუდე ნომრის სიგრძე 10 ციფრამდე
    if (input.length > 10) input = input.substring(0, 10);

    // ფორმატირება (XXX) XXX-XXXX
    const formattedPhone = input.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
    this.value = formattedPhone;
});

// 3. Phone Number Validation
function isValidPhoneNumber(phone) {
    // ნომერი უნდა შეესაბამებოდეს ფორმატს (XXX) XXX-XXXX
    const phonePattern = /^\(\d{3}\) \d{3}-\d{4}$/;
    return phonePattern.test(phone);
}

// 8. Allow Only Latin Characters
function allowOnlyLatin(event) {
    const charCode = event.charCode || event.keyCode;
    const char = String.fromCharCode(charCode);
    const latinRegex = /^[A-Za-z0-9@.,'"\-() ]+$/; // დასაშვები სიმბოლოები

    // თუ სიმბოლო არ შეესაბამება ლათინურ ასოებს ან რიცხვებს, აღკვეთე შეყვანა
    if (!latinRegex.test(char) && charCode !== 8 && charCode !== 9) { // 8 = Backspace, 9 = Tab
        event.preventDefault();
    }
}

document.querySelectorAll("#transport_from, #transport_to, #email, #name, #vehicle_brand, #vehicle_model").forEach(input => {
    input.addEventListener("keypress", allowOnlyLatin); // ყოველი კლავიშის დაჭერისას შეამოწმე
    input.addEventListener("input", function(event) {
        // წაშალე ყველა არა-ლათინური სიმბოლო
        this.value = this.value.replace(/[^A-Za-z0-9@.,'"\-() ]/g, '');
    });
});

// 4. Step Navigation
function nextStep(step) {
    if (step === 2 && !isStepOneValid()) {
        return; // არ გაგრძელდება, თუ Step 1 არ არის ვალიდური
    }
    if (step === 3 && !isStepTwoValid()) {
        return; // არ გაგრძელდება, თუ Step 2 არ არის ვალიდური
    }

    document.querySelectorAll(".step").forEach(stepElement => {
        stepElement.style.display = "none";
    });
    document.getElementById(`step-${step}`).style.display = "block";
}


// 5. Form Submission
document.getElementById("transportForm").addEventListener("submit", function(event) {
    if (isStepThreeValid()) { 
        this.submit(); // Directly submit the form
    } else {
        showNotification("Please fill in all required fields.", "error");
        event.preventDefault(); // Prevent form submission if validation fails
    }
});

// 6. Show Notification
function showNotification(message, type) {
    const notification = document.getElementById("notification");
    if (notification) {
        notification.innerText = message;
        notification.className = `${type}-message notification`;
        notification.style.display = "block";
        setTimeout(function() {
            notification.style.display = "none";
        }, 5000); // შეტყობინება გაქრება 5 წამის შემდეგ
    }
}