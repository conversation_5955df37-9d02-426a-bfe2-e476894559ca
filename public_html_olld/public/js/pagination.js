let currentPage = 1;
const totalPages = 3;

function showPage(page) {
    for (let i = 1; i <= totalPages; i++) {
        document.getElementById(`page-${i}`).style.display = 'none';
    }
    document.getElementById(`page-${page}`).style.display = 'block';
    document.getElementById('pageNumber').innerText = `Page ${page} of ${totalPages}`;
    document.querySelector('.fix-term-prev').disabled = page === 1;
    document.querySelector('.fix-term-next').disabled = page === totalPages;
}

function nextPage() {
    if (currentPage < totalPages) {
        currentPage++;
        showPage(currentPage);
    }
}

function prevPage() {
    if (currentPage > 1) {
        currentPage--;
        showPage(currentPage);
    }
}

showPage(currentPage);
