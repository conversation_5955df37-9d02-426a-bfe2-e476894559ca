<?php

// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.

namespace Algolia\AlgoliaSearch\Api;

use Algolia\AlgoliaSearch\Algolia;
use Algolia\AlgoliaSearch\Configuration\IngestionConfig;
use Algolia\AlgoliaSearch\Model\Ingestion\AuthenticationCreate;
use Algolia\AlgoliaSearch\Model\Ingestion\AuthenticationSearch;
use Algolia\AlgoliaSearch\Model\Ingestion\AuthenticationUpdate;
use Algolia\AlgoliaSearch\Model\Ingestion\BatchWriteParams;
use Algolia\AlgoliaSearch\Model\Ingestion\DestinationCreate;
use Algolia\AlgoliaSearch\Model\Ingestion\DestinationSearch;
use Algolia\AlgoliaSearch\Model\Ingestion\DestinationUpdate;
use Algolia\AlgoliaSearch\Model\Ingestion\GenerateTransformationCodePayload;
use Algolia\AlgoliaSearch\Model\Ingestion\RunSourcePayload;
use Algolia\AlgoliaSearch\Model\Ingestion\SourceCreate;
use Algolia\AlgoliaSearch\Model\Ingestion\SourceSearch;
use Algolia\AlgoliaSearch\Model\Ingestion\SourceUpdate;
use Algolia\AlgoliaSearch\Model\Ingestion\TaskCreate;
use Algolia\AlgoliaSearch\Model\Ingestion\TaskCreateV1;
use Algolia\AlgoliaSearch\Model\Ingestion\TaskSearch;
use Algolia\AlgoliaSearch\Model\Ingestion\TaskUpdate;
use Algolia\AlgoliaSearch\Model\Ingestion\TaskUpdateV1;
use Algolia\AlgoliaSearch\Model\Ingestion\TransformationCreate;
use Algolia\AlgoliaSearch\Model\Ingestion\TransformationSearch;
use Algolia\AlgoliaSearch\Model\Ingestion\TransformationTry;
use Algolia\AlgoliaSearch\ObjectSerializer;
use Algolia\AlgoliaSearch\RetryStrategy\ApiWrapper;
use Algolia\AlgoliaSearch\RetryStrategy\ApiWrapperInterface;
use Algolia\AlgoliaSearch\RetryStrategy\ClusterHosts;
use Algolia\AlgoliaSearch\Support\Helpers;
use GuzzleHttp\Psr7\Query;

/**
 * IngestionClient Class Doc Comment.
 *
 * @category Class
 */
class IngestionClient
{
    public const VERSION = '4.0.0';

    /**
     * @var ApiWrapperInterface
     */
    protected $api;

    /**
     * @var IngestionConfig
     */
    protected $config;

    public function __construct(ApiWrapperInterface $apiWrapper, IngestionConfig $config)
    {
        $this->config = $config;
        $this->api = $apiWrapper;
    }

    /**
     * Instantiate the client with basic credentials and region.
     *
     * @param string $appId  Application ID
     * @param string $apiKey Algolia API Key
     * @param string $region Region
     */
    public static function create($appId = null, $apiKey = null, $region = null)
    {
        $config = IngestionConfig::create($appId, $apiKey, $region);

        return static::createWithConfig($config);
    }

    /**
     * Instantiate the client with configuration.
     *
     * @param IngestionConfig $config Configuration
     */
    public static function createWithConfig(IngestionConfig $config)
    {
        $config = clone $config;

        $apiWrapper = new ApiWrapper(
            Algolia::getHttpClient(),
            $config,
            self::getClusterHosts($config)
        );

        return new static($apiWrapper, $config);
    }

    /**
     * Gets the cluster hosts depending on the config.
     *
     * @return ClusterHosts
     */
    public static function getClusterHosts(IngestionConfig $config)
    {
        if ($hosts = $config->getHosts()) {
            // If a list of hosts was passed, we ignore the cache
            $clusterHosts = ClusterHosts::create($hosts);
        } else {
            $url = null !== $config->getRegion() && '' !== $config->getRegion() ?
                str_replace('{region}', $config->getRegion(), 'data.{region}.algolia.com') :
                '';
            $clusterHosts = ClusterHosts::create($url);
        }

        return $clusterHosts;
    }

    /**
     * @return IngestionConfig
     */
    public function getClientConfig()
    {
        return $this->config;
    }

    /**
     * Creates a new authentication resource.
     *
     * Required API Key ACLs:
     *  - addObject
     *  - deleteIndex
     *  - editSettings
     *
     * @param array $authenticationCreate (required)
     *                                    - $authenticationCreate['type'] => (array)  (required)
     *                                    - $authenticationCreate['name'] => (string) Descriptive name for the resource. (required)
     *                                    - $authenticationCreate['platform'] => (array)
     *                                    - $authenticationCreate['input'] => (array)  (required)
     *
     * @see AuthenticationCreate
     *
     * @param array $requestOptions the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return \Algolia\AlgoliaSearch\Model\Ingestion\AuthenticationCreateResponse|array<string, mixed>
     */
    public function createAuthentication($authenticationCreate, $requestOptions = [])
    {
        // verify the required parameter 'authenticationCreate' is set
        if (!isset($authenticationCreate)) {
            throw new \InvalidArgumentException(
                'Parameter `authenticationCreate` is required when calling `createAuthentication`.'
            );
        }

        $resourcePath = '/1/authentications';
        $queryParameters = [];
        $headers = [];
        $httpBody = $authenticationCreate;

        return $this->sendRequest('POST', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * Creates a new destination.
     *
     * Required API Key ACLs:
     *  - addObject
     *  - deleteIndex
     *  - editSettings
     *
     * @param array $destinationCreate (required)
     *                                 - $destinationCreate['type'] => (array)  (required)
     *                                 - $destinationCreate['name'] => (string) Descriptive name for the resource. (required)
     *                                 - $destinationCreate['input'] => (array)  (required)
     *                                 - $destinationCreate['authenticationID'] => (string) Universally unique identifier (UUID) of an authentication resource.
     *                                 - $destinationCreate['transformationIDs'] => (array)
     *
     * @see DestinationCreate
     *
     * @param array $requestOptions the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return \Algolia\AlgoliaSearch\Model\Ingestion\DestinationCreateResponse|array<string, mixed>
     */
    public function createDestination($destinationCreate, $requestOptions = [])
    {
        // verify the required parameter 'destinationCreate' is set
        if (!isset($destinationCreate)) {
            throw new \InvalidArgumentException(
                'Parameter `destinationCreate` is required when calling `createDestination`.'
            );
        }

        $resourcePath = '/1/destinations';
        $queryParameters = [];
        $headers = [];
        $httpBody = $destinationCreate;

        return $this->sendRequest('POST', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * Creates a new source.
     *
     * Required API Key ACLs:
     *  - addObject
     *  - deleteIndex
     *  - editSettings
     *
     * @param array $sourceCreate (required)
     *                            - $sourceCreate['type'] => (array)  (required)
     *                            - $sourceCreate['name'] => (string) Descriptive name of the source. (required)
     *                            - $sourceCreate['input'] => (array)  (required)
     *                            - $sourceCreate['authenticationID'] => (string) Universally unique identifier (UUID) of an authentication resource.
     *
     * @see SourceCreate
     *
     * @param array $requestOptions the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return \Algolia\AlgoliaSearch\Model\Ingestion\SourceCreateResponse|array<string, mixed>
     */
    public function createSource($sourceCreate, $requestOptions = [])
    {
        // verify the required parameter 'sourceCreate' is set
        if (!isset($sourceCreate)) {
            throw new \InvalidArgumentException(
                'Parameter `sourceCreate` is required when calling `createSource`.'
            );
        }

        $resourcePath = '/1/sources';
        $queryParameters = [];
        $headers = [];
        $httpBody = $sourceCreate;

        return $this->sendRequest('POST', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * Creates a new task.
     *
     * @param array $taskCreate Request body for creating a task. (required)
     *                          - $taskCreate['sourceID'] => (string) Universally uniqud identifier (UUID) of a source. (required)
     *                          - $taskCreate['destinationID'] => (string) Universally unique identifier (UUID) of a destination resource. (required)
     *                          - $taskCreate['action'] => (array)  (required)
     *                          - $taskCreate['cron'] => (string) Cron expression for the task's schedule.
     *                          - $taskCreate['enabled'] => (bool) Whether the task is enabled.
     *                          - $taskCreate['failureThreshold'] => (int) Maximum accepted percentage of failures for a task run to finish successfully.
     *                          - $taskCreate['input'] => (array)
     *                          - $taskCreate['cursor'] => (string) Date of the last cursor in RFC 3339 format.
     *
     * @see TaskCreate
     *
     * @param array $requestOptions the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return \Algolia\AlgoliaSearch\Model\Ingestion\TaskCreateResponse|array<string, mixed>
     */
    public function createTask($taskCreate, $requestOptions = [])
    {
        // verify the required parameter 'taskCreate' is set
        if (!isset($taskCreate)) {
            throw new \InvalidArgumentException(
                'Parameter `taskCreate` is required when calling `createTask`.'
            );
        }

        $resourcePath = '/2/tasks';
        $queryParameters = [];
        $headers = [];
        $httpBody = $taskCreate;

        return $this->sendRequest('POST', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * Creates a new task using the v1 endpoint, please use `createTask` instead.
     *
     * @param array $taskCreate Request body for creating a task. (required)
     *                          - $taskCreate['sourceID'] => (string) Universally uniqud identifier (UUID) of a source. (required)
     *                          - $taskCreate['destinationID'] => (string) Universally unique identifier (UUID) of a destination resource. (required)
     *                          - $taskCreate['trigger'] => (array)  (required)
     *                          - $taskCreate['action'] => (array)  (required)
     *                          - $taskCreate['enabled'] => (bool) Whether the task is enabled.
     *                          - $taskCreate['failureThreshold'] => (int) Maximum accepted percentage of failures for a task run to finish successfully.
     *                          - $taskCreate['input'] => (array)
     *                          - $taskCreate['cursor'] => (string) Date of the last cursor in RFC 3339 format.
     *
     * @see TaskCreateV1
     *
     * @param array $requestOptions the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return \Algolia\AlgoliaSearch\Model\Ingestion\TaskCreateResponse|array<string, mixed>
     */
    public function createTaskV1($taskCreate, $requestOptions = [])
    {
        // verify the required parameter 'taskCreate' is set
        if (!isset($taskCreate)) {
            throw new \InvalidArgumentException(
                'Parameter `taskCreate` is required when calling `createTaskV1`.'
            );
        }

        $resourcePath = '/1/tasks';
        $queryParameters = [];
        $headers = [];
        $httpBody = $taskCreate;

        return $this->sendRequest('POST', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * Creates a new transformation.
     *
     * @param array $transformationCreate Request body for creating a transformation. (required)
     *                                    - $transformationCreate['code'] => (string) The source code of the transformation. (required)
     *                                    - $transformationCreate['name'] => (string) The uniquely identified name of your transformation. (required)
     *                                    - $transformationCreate['description'] => (string) A descriptive name for your transformation of what it does.
     *                                    - $transformationCreate['authenticationIDs'] => (array) The authentications associated for the current transformation.
     *
     * @see TransformationCreate
     *
     * @param array $requestOptions the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return \Algolia\AlgoliaSearch\Model\Ingestion\TransformationCreateResponse|array<string, mixed>
     */
    public function createTransformation($transformationCreate, $requestOptions = [])
    {
        // verify the required parameter 'transformationCreate' is set
        if (!isset($transformationCreate)) {
            throw new \InvalidArgumentException(
                'Parameter `transformationCreate` is required when calling `createTransformation`.'
            );
        }

        $resourcePath = '/1/transformations';
        $queryParameters = [];
        $headers = [];
        $httpBody = $transformationCreate;

        return $this->sendRequest('POST', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * This method allow you to send requests to the Algolia REST API.
     *
     * @param string $path           Path of the endpoint, anything after \&quot;/1\&quot; must be specified. (required)
     * @param array  $parameters     Query parameters to apply to the current query. (optional)
     * @param array  $requestOptions the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return array<string, mixed>|object
     */
    public function customDelete($path, $parameters = null, $requestOptions = [])
    {
        // verify the required parameter 'path' is set
        if (!isset($path)) {
            throw new \InvalidArgumentException(
                'Parameter `path` is required when calling `customDelete`.'
            );
        }

        $resourcePath = '/{path}';
        $queryParameters = [];
        $headers = [];
        $httpBody = null;

        if (null !== $parameters) {
            $queryParameters = $parameters;
        }

        // path params
        if (null !== $path) {
            $resourcePath = str_replace(
                '{path}',
                $path,
                $resourcePath
            );
        }

        return $this->sendRequest('DELETE', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * This method allow you to send requests to the Algolia REST API.
     *
     * @param string $path           Path of the endpoint, anything after \&quot;/1\&quot; must be specified. (required)
     * @param array  $parameters     Query parameters to apply to the current query. (optional)
     * @param array  $requestOptions the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return array<string, mixed>|object
     */
    public function customGet($path, $parameters = null, $requestOptions = [])
    {
        // verify the required parameter 'path' is set
        if (!isset($path)) {
            throw new \InvalidArgumentException(
                'Parameter `path` is required when calling `customGet`.'
            );
        }

        $resourcePath = '/{path}';
        $queryParameters = [];
        $headers = [];
        $httpBody = null;

        if (null !== $parameters) {
            $queryParameters = $parameters;
        }

        // path params
        if (null !== $path) {
            $resourcePath = str_replace(
                '{path}',
                $path,
                $resourcePath
            );
        }

        return $this->sendRequest('GET', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * This method allow you to send requests to the Algolia REST API.
     *
     * @param string $path           Path of the endpoint, anything after \&quot;/1\&quot; must be specified. (required)
     * @param array  $parameters     Query parameters to apply to the current query. (optional)
     * @param array  $body           Parameters to send with the custom request. (optional)
     * @param array  $requestOptions the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return array<string, mixed>|object
     */
    public function customPost($path, $parameters = null, $body = null, $requestOptions = [])
    {
        // verify the required parameter 'path' is set
        if (!isset($path)) {
            throw new \InvalidArgumentException(
                'Parameter `path` is required when calling `customPost`.'
            );
        }

        $resourcePath = '/{path}';
        $queryParameters = [];
        $headers = [];
        $httpBody = isset($body) ? $body : [];

        if (null !== $parameters) {
            $queryParameters = $parameters;
        }

        // path params
        if (null !== $path) {
            $resourcePath = str_replace(
                '{path}',
                $path,
                $resourcePath
            );
        }

        return $this->sendRequest('POST', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * This method allow you to send requests to the Algolia REST API.
     *
     * @param string $path           Path of the endpoint, anything after \&quot;/1\&quot; must be specified. (required)
     * @param array  $parameters     Query parameters to apply to the current query. (optional)
     * @param array  $body           Parameters to send with the custom request. (optional)
     * @param array  $requestOptions the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return array<string, mixed>|object
     */
    public function customPut($path, $parameters = null, $body = null, $requestOptions = [])
    {
        // verify the required parameter 'path' is set
        if (!isset($path)) {
            throw new \InvalidArgumentException(
                'Parameter `path` is required when calling `customPut`.'
            );
        }

        $resourcePath = '/{path}';
        $queryParameters = [];
        $headers = [];
        $httpBody = isset($body) ? $body : [];

        if (null !== $parameters) {
            $queryParameters = $parameters;
        }

        // path params
        if (null !== $path) {
            $resourcePath = str_replace(
                '{path}',
                $path,
                $resourcePath
            );
        }

        return $this->sendRequest('PUT', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * Deletes an authentication resource. You can't delete authentication resources that are used by a source or a destination.
     *
     * Required API Key ACLs:
     *  - addObject
     *  - deleteIndex
     *  - editSettings
     *
     * @param string $authenticationID Unique identifier of an authentication resource. (required)
     * @param array  $requestOptions   the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return \Algolia\AlgoliaSearch\Model\Ingestion\DeleteResponse|array<string, mixed>
     */
    public function deleteAuthentication($authenticationID, $requestOptions = [])
    {
        // verify the required parameter 'authenticationID' is set
        if (!isset($authenticationID)) {
            throw new \InvalidArgumentException(
                'Parameter `authenticationID` is required when calling `deleteAuthentication`.'
            );
        }

        $resourcePath = '/1/authentications/{authenticationID}';
        $queryParameters = [];
        $headers = [];
        $httpBody = null;

        // path params
        if (null !== $authenticationID) {
            $resourcePath = str_replace(
                '{authenticationID}',
                ObjectSerializer::toPathValue($authenticationID),
                $resourcePath
            );
        }

        return $this->sendRequest('DELETE', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * Deletes a destination by its ID. You can't delete destinations that are referenced in tasks.
     *
     * Required API Key ACLs:
     *  - addObject
     *  - deleteIndex
     *  - editSettings
     *
     * @param string $destinationID  Unique identifier of a destination. (required)
     * @param array  $requestOptions the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return \Algolia\AlgoliaSearch\Model\Ingestion\DeleteResponse|array<string, mixed>
     */
    public function deleteDestination($destinationID, $requestOptions = [])
    {
        // verify the required parameter 'destinationID' is set
        if (!isset($destinationID)) {
            throw new \InvalidArgumentException(
                'Parameter `destinationID` is required when calling `deleteDestination`.'
            );
        }

        $resourcePath = '/1/destinations/{destinationID}';
        $queryParameters = [];
        $headers = [];
        $httpBody = null;

        // path params
        if (null !== $destinationID) {
            $resourcePath = str_replace(
                '{destinationID}',
                ObjectSerializer::toPathValue($destinationID),
                $resourcePath
            );
        }

        return $this->sendRequest('DELETE', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * Deletes a source by its ID. You can't delete sources that are referenced in tasks.
     *
     * Required API Key ACLs:
     *  - addObject
     *  - deleteIndex
     *  - editSettings
     *
     * @param string $sourceID       Unique identifier of a source. (required)
     * @param array  $requestOptions the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return \Algolia\AlgoliaSearch\Model\Ingestion\DeleteResponse|array<string, mixed>
     */
    public function deleteSource($sourceID, $requestOptions = [])
    {
        // verify the required parameter 'sourceID' is set
        if (!isset($sourceID)) {
            throw new \InvalidArgumentException(
                'Parameter `sourceID` is required when calling `deleteSource`.'
            );
        }

        $resourcePath = '/1/sources/{sourceID}';
        $queryParameters = [];
        $headers = [];
        $httpBody = null;

        // path params
        if (null !== $sourceID) {
            $resourcePath = str_replace(
                '{sourceID}',
                ObjectSerializer::toPathValue($sourceID),
                $resourcePath
            );
        }

        return $this->sendRequest('DELETE', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * Deletes a task by its ID.
     *
     * @param string $taskID         Unique identifier of a task. (required)
     * @param array  $requestOptions the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return \Algolia\AlgoliaSearch\Model\Ingestion\DeleteResponse|array<string, mixed>
     */
    public function deleteTask($taskID, $requestOptions = [])
    {
        // verify the required parameter 'taskID' is set
        if (!isset($taskID)) {
            throw new \InvalidArgumentException(
                'Parameter `taskID` is required when calling `deleteTask`.'
            );
        }

        $resourcePath = '/2/tasks/{taskID}';
        $queryParameters = [];
        $headers = [];
        $httpBody = null;

        // path params
        if (null !== $taskID) {
            $resourcePath = str_replace(
                '{taskID}',
                ObjectSerializer::toPathValue($taskID),
                $resourcePath
            );
        }

        return $this->sendRequest('DELETE', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * Deletes a task by its ID using the v1 endpoint, please use `deleteTask` instead.
     *
     * @param string $taskID         Unique identifier of a task. (required)
     * @param array  $requestOptions the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return \Algolia\AlgoliaSearch\Model\Ingestion\DeleteResponse|array<string, mixed>
     */
    public function deleteTaskV1($taskID, $requestOptions = [])
    {
        // verify the required parameter 'taskID' is set
        if (!isset($taskID)) {
            throw new \InvalidArgumentException(
                'Parameter `taskID` is required when calling `deleteTaskV1`.'
            );
        }

        $resourcePath = '/1/tasks/{taskID}';
        $queryParameters = [];
        $headers = [];
        $httpBody = null;

        // path params
        if (null !== $taskID) {
            $resourcePath = str_replace(
                '{taskID}',
                ObjectSerializer::toPathValue($taskID),
                $resourcePath
            );
        }

        return $this->sendRequest('DELETE', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * Deletes a transformation by its ID.
     *
     * @param string $transformationID Unique identifier of a transformation. (required)
     * @param array  $requestOptions   the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return \Algolia\AlgoliaSearch\Model\Ingestion\DeleteResponse|array<string, mixed>
     */
    public function deleteTransformation($transformationID, $requestOptions = [])
    {
        // verify the required parameter 'transformationID' is set
        if (!isset($transformationID)) {
            throw new \InvalidArgumentException(
                'Parameter `transformationID` is required when calling `deleteTransformation`.'
            );
        }

        $resourcePath = '/1/transformations/{transformationID}';
        $queryParameters = [];
        $headers = [];
        $httpBody = null;

        // path params
        if (null !== $transformationID) {
            $resourcePath = str_replace(
                '{transformationID}',
                ObjectSerializer::toPathValue($transformationID),
                $resourcePath
            );
        }

        return $this->sendRequest('DELETE', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * Disables a task.
     *
     * Required API Key ACLs:
     *  - addObject
     *  - deleteIndex
     *  - editSettings
     *
     * @param string $taskID         Unique identifier of a task. (required)
     * @param array  $requestOptions the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return \Algolia\AlgoliaSearch\Model\Ingestion\TaskUpdateResponse|array<string, mixed>
     */
    public function disableTask($taskID, $requestOptions = [])
    {
        // verify the required parameter 'taskID' is set
        if (!isset($taskID)) {
            throw new \InvalidArgumentException(
                'Parameter `taskID` is required when calling `disableTask`.'
            );
        }

        $resourcePath = '/2/tasks/{taskID}/disable';
        $queryParameters = [];
        $headers = [];
        $httpBody = null;

        // path params
        if (null !== $taskID) {
            $resourcePath = str_replace(
                '{taskID}',
                ObjectSerializer::toPathValue($taskID),
                $resourcePath
            );
        }

        return $this->sendRequest('PUT', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * Disables a task using the v1 endpoint, please use `disableTask` instead.
     *
     * Required API Key ACLs:
     *  - addObject
     *  - deleteIndex
     *  - editSettings
     *
     * @param string $taskID         Unique identifier of a task. (required)
     * @param array  $requestOptions the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return \Algolia\AlgoliaSearch\Model\Ingestion\TaskUpdateResponse|array<string, mixed>
     *
     * @deprecated
     */
    public function disableTaskV1($taskID, $requestOptions = [])
    {
        // verify the required parameter 'taskID' is set
        if (!isset($taskID)) {
            throw new \InvalidArgumentException(
                'Parameter `taskID` is required when calling `disableTaskV1`.'
            );
        }

        $resourcePath = '/1/tasks/{taskID}/disable';
        $queryParameters = [];
        $headers = [];
        $httpBody = null;

        // path params
        if (null !== $taskID) {
            $resourcePath = str_replace(
                '{taskID}',
                ObjectSerializer::toPathValue($taskID),
                $resourcePath
            );
        }

        return $this->sendRequest('PUT', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * Enables a task.
     *
     * Required API Key ACLs:
     *  - addObject
     *  - deleteIndex
     *  - editSettings
     *
     * @param string $taskID         Unique identifier of a task. (required)
     * @param array  $requestOptions the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return \Algolia\AlgoliaSearch\Model\Ingestion\TaskUpdateResponse|array<string, mixed>
     */
    public function enableTask($taskID, $requestOptions = [])
    {
        // verify the required parameter 'taskID' is set
        if (!isset($taskID)) {
            throw new \InvalidArgumentException(
                'Parameter `taskID` is required when calling `enableTask`.'
            );
        }

        $resourcePath = '/2/tasks/{taskID}/enable';
        $queryParameters = [];
        $headers = [];
        $httpBody = null;

        // path params
        if (null !== $taskID) {
            $resourcePath = str_replace(
                '{taskID}',
                ObjectSerializer::toPathValue($taskID),
                $resourcePath
            );
        }

        return $this->sendRequest('PUT', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * Enables a task using the v1 endpoint, please use `enableTask` instead.
     *
     * Required API Key ACLs:
     *  - addObject
     *  - deleteIndex
     *  - editSettings
     *
     * @param string $taskID         Unique identifier of a task. (required)
     * @param array  $requestOptions the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return \Algolia\AlgoliaSearch\Model\Ingestion\TaskUpdateResponse|array<string, mixed>
     */
    public function enableTaskV1($taskID, $requestOptions = [])
    {
        // verify the required parameter 'taskID' is set
        if (!isset($taskID)) {
            throw new \InvalidArgumentException(
                'Parameter `taskID` is required when calling `enableTaskV1`.'
            );
        }

        $resourcePath = '/1/tasks/{taskID}/enable';
        $queryParameters = [];
        $headers = [];
        $httpBody = null;

        // path params
        if (null !== $taskID) {
            $resourcePath = str_replace(
                '{taskID}',
                ObjectSerializer::toPathValue($taskID),
                $resourcePath
            );
        }

        return $this->sendRequest('PUT', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * Generates code for the selected model based on the given prompt.
     *
     * Required API Key ACLs:
     *  - addObject
     *  - deleteIndex
     *  - editSettings
     *
     * @param array $generateTransformationCodePayload generateTransformationCodePayload (required)
     *                                                 - $generateTransformationCodePayload['id'] => (string)  (required)
     *                                                 - $generateTransformationCodePayload['systemPrompt'] => (string)
     *                                                 - $generateTransformationCodePayload['userPrompt'] => (string)  (required)
     *
     * @see GenerateTransformationCodePayload
     *
     * @param array $requestOptions the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return \Algolia\AlgoliaSearch\Model\Ingestion\GenerateTransformationCodeResponse|array<string, mixed>
     */
    public function generateTransformationCode($generateTransformationCodePayload, $requestOptions = [])
    {
        // verify the required parameter 'generateTransformationCodePayload' is set
        if (!isset($generateTransformationCodePayload)) {
            throw new \InvalidArgumentException(
                'Parameter `generateTransformationCodePayload` is required when calling `generateTransformationCode`.'
            );
        }

        $resourcePath = '/1/transformations/models';
        $queryParameters = [];
        $headers = [];
        $httpBody = $generateTransformationCodePayload;

        return $this->sendRequest('POST', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * Retrieves an authentication resource by its ID.
     *
     * Required API Key ACLs:
     *  - addObject
     *  - deleteIndex
     *  - editSettings
     *
     * @param string $authenticationID Unique identifier of an authentication resource. (required)
     * @param array  $requestOptions   the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return \Algolia\AlgoliaSearch\Model\Ingestion\Authentication|array<string, mixed>
     */
    public function getAuthentication($authenticationID, $requestOptions = [])
    {
        // verify the required parameter 'authenticationID' is set
        if (!isset($authenticationID)) {
            throw new \InvalidArgumentException(
                'Parameter `authenticationID` is required when calling `getAuthentication`.'
            );
        }

        $resourcePath = '/1/authentications/{authenticationID}';
        $queryParameters = [];
        $headers = [];
        $httpBody = null;

        // path params
        if (null !== $authenticationID) {
            $resourcePath = str_replace(
                '{authenticationID}',
                ObjectSerializer::toPathValue($authenticationID),
                $resourcePath
            );
        }

        return $this->sendRequest('GET', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * Retrieves a destination by its ID.
     *
     * Required API Key ACLs:
     *  - addObject
     *  - deleteIndex
     *  - editSettings
     *
     * @param string $destinationID  Unique identifier of a destination. (required)
     * @param array  $requestOptions the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return \Algolia\AlgoliaSearch\Model\Ingestion\Destination|array<string, mixed>
     */
    public function getDestination($destinationID, $requestOptions = [])
    {
        // verify the required parameter 'destinationID' is set
        if (!isset($destinationID)) {
            throw new \InvalidArgumentException(
                'Parameter `destinationID` is required when calling `getDestination`.'
            );
        }

        $resourcePath = '/1/destinations/{destinationID}';
        $queryParameters = [];
        $headers = [];
        $httpBody = null;

        // path params
        if (null !== $destinationID) {
            $resourcePath = str_replace(
                '{destinationID}',
                ObjectSerializer::toPathValue($destinationID),
                $resourcePath
            );
        }

        return $this->sendRequest('GET', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * Retrieves a single task run event by its ID.
     *
     * Required API Key ACLs:
     *  - addObject
     *  - deleteIndex
     *  - editSettings
     *
     * @param string $runID          Unique identifier of a task run. (required)
     * @param string $eventID        Unique identifier of an event. (required)
     * @param array  $requestOptions the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return \Algolia\AlgoliaSearch\Model\Ingestion\Event|array<string, mixed>
     */
    public function getEvent($runID, $eventID, $requestOptions = [])
    {
        // verify the required parameter 'runID' is set
        if (!isset($runID)) {
            throw new \InvalidArgumentException(
                'Parameter `runID` is required when calling `getEvent`.'
            );
        }
        // verify the required parameter 'eventID' is set
        if (!isset($eventID)) {
            throw new \InvalidArgumentException(
                'Parameter `eventID` is required when calling `getEvent`.'
            );
        }

        $resourcePath = '/1/runs/{runID}/events/{eventID}';
        $queryParameters = [];
        $headers = [];
        $httpBody = null;

        // path params
        if (null !== $runID) {
            $resourcePath = str_replace(
                '{runID}',
                ObjectSerializer::toPathValue($runID),
                $resourcePath
            );
        }

        // path params
        if (null !== $eventID) {
            $resourcePath = str_replace(
                '{eventID}',
                ObjectSerializer::toPathValue($eventID),
                $resourcePath
            );
        }

        return $this->sendRequest('GET', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * Retrieve a single task run by its ID.
     *
     * Required API Key ACLs:
     *  - addObject
     *  - deleteIndex
     *  - editSettings
     *
     * @param string $runID          Unique identifier of a task run. (required)
     * @param array  $requestOptions the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return \Algolia\AlgoliaSearch\Model\Ingestion\Run|array<string, mixed>
     */
    public function getRun($runID, $requestOptions = [])
    {
        // verify the required parameter 'runID' is set
        if (!isset($runID)) {
            throw new \InvalidArgumentException(
                'Parameter `runID` is required when calling `getRun`.'
            );
        }

        $resourcePath = '/1/runs/{runID}';
        $queryParameters = [];
        $headers = [];
        $httpBody = null;

        // path params
        if (null !== $runID) {
            $resourcePath = str_replace(
                '{runID}',
                ObjectSerializer::toPathValue($runID),
                $resourcePath
            );
        }

        return $this->sendRequest('GET', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * Retrieve a source by its ID.
     *
     * Required API Key ACLs:
     *  - addObject
     *  - deleteIndex
     *  - editSettings
     *
     * @param string $sourceID       Unique identifier of a source. (required)
     * @param array  $requestOptions the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return \Algolia\AlgoliaSearch\Model\Ingestion\Source|array<string, mixed>
     */
    public function getSource($sourceID, $requestOptions = [])
    {
        // verify the required parameter 'sourceID' is set
        if (!isset($sourceID)) {
            throw new \InvalidArgumentException(
                'Parameter `sourceID` is required when calling `getSource`.'
            );
        }

        $resourcePath = '/1/sources/{sourceID}';
        $queryParameters = [];
        $headers = [];
        $httpBody = null;

        // path params
        if (null !== $sourceID) {
            $resourcePath = str_replace(
                '{sourceID}',
                ObjectSerializer::toPathValue($sourceID),
                $resourcePath
            );
        }

        return $this->sendRequest('GET', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * Retrieves a task by its ID.
     *
     * Required API Key ACLs:
     *  - addObject
     *  - deleteIndex
     *  - editSettings
     *
     * @param string $taskID         Unique identifier of a task. (required)
     * @param array  $requestOptions the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return \Algolia\AlgoliaSearch\Model\Ingestion\Task|array<string, mixed>
     */
    public function getTask($taskID, $requestOptions = [])
    {
        // verify the required parameter 'taskID' is set
        if (!isset($taskID)) {
            throw new \InvalidArgumentException(
                'Parameter `taskID` is required when calling `getTask`.'
            );
        }

        $resourcePath = '/2/tasks/{taskID}';
        $queryParameters = [];
        $headers = [];
        $httpBody = null;

        // path params
        if (null !== $taskID) {
            $resourcePath = str_replace(
                '{taskID}',
                ObjectSerializer::toPathValue($taskID),
                $resourcePath
            );
        }

        return $this->sendRequest('GET', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * Retrieves a task by its ID using the v1 endpoint, please use `getTask` instead.
     *
     * Required API Key ACLs:
     *  - addObject
     *  - deleteIndex
     *  - editSettings
     *
     * @param string $taskID         Unique identifier of a task. (required)
     * @param array  $requestOptions the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return \Algolia\AlgoliaSearch\Model\Ingestion\TaskV1|array<string, mixed>
     */
    public function getTaskV1($taskID, $requestOptions = [])
    {
        // verify the required parameter 'taskID' is set
        if (!isset($taskID)) {
            throw new \InvalidArgumentException(
                'Parameter `taskID` is required when calling `getTaskV1`.'
            );
        }

        $resourcePath = '/1/tasks/{taskID}';
        $queryParameters = [];
        $headers = [];
        $httpBody = null;

        // path params
        if (null !== $taskID) {
            $resourcePath = str_replace(
                '{taskID}',
                ObjectSerializer::toPathValue($taskID),
                $resourcePath
            );
        }

        return $this->sendRequest('GET', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * Retrieves a transformation by its ID.
     *
     * Required API Key ACLs:
     *  - addObject
     *  - deleteIndex
     *  - editSettings
     *
     * @param string $transformationID Unique identifier of a transformation. (required)
     * @param array  $requestOptions   the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return \Algolia\AlgoliaSearch\Model\Ingestion\Transformation|array<string, mixed>
     */
    public function getTransformation($transformationID, $requestOptions = [])
    {
        // verify the required parameter 'transformationID' is set
        if (!isset($transformationID)) {
            throw new \InvalidArgumentException(
                'Parameter `transformationID` is required when calling `getTransformation`.'
            );
        }

        $resourcePath = '/1/transformations/{transformationID}';
        $queryParameters = [];
        $headers = [];
        $httpBody = null;

        // path params
        if (null !== $transformationID) {
            $resourcePath = str_replace(
                '{transformationID}',
                ObjectSerializer::toPathValue($transformationID),
                $resourcePath
            );
        }

        return $this->sendRequest('GET', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * Retrieves a list of all authentication resources.
     *
     * Required API Key ACLs:
     *  - addObject
     *  - deleteIndex
     *  - editSettings
     *
     * @param int   $itemsPerPage   Number of items per page. (optional, default to 10)
     * @param int   $page           Page number of the paginated API response. (optional)
     * @param array $type           Type of authentication resource to retrieve. (optional)
     * @param array $platform       Ecommerce platform for which to retrieve authentication resources. (optional)
     * @param array $sort           Property by which to sort the list of authentication resources. (optional)
     * @param array $order          Sort order of the response, ascending or descending. (optional)
     * @param array $requestOptions the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return \Algolia\AlgoliaSearch\Model\Ingestion\ListAuthenticationsResponse|array<string, mixed>
     */
    public function listAuthentications($itemsPerPage = null, $page = null, $type = null, $platform = null, $sort = null, $order = null, $requestOptions = [])
    {
        if (null !== $itemsPerPage && $itemsPerPage > 100) {
            throw new \InvalidArgumentException('invalid value for "$itemsPerPage" when calling IngestionClient.listAuthentications, must be smaller than or equal to 100.');
        }
        if (null !== $itemsPerPage && $itemsPerPage < 1) {
            throw new \InvalidArgumentException('invalid value for "$itemsPerPage" when calling IngestionClient.listAuthentications, must be bigger than or equal to 1.');
        }

        if (null !== $page && $page < 1) {
            throw new \InvalidArgumentException('invalid value for "$page" when calling IngestionClient.listAuthentications, must be bigger than or equal to 1.');
        }

        $resourcePath = '/1/authentications';
        $queryParameters = [];
        $headers = [];
        $httpBody = null;

        if (null !== $itemsPerPage) {
            $queryParameters['itemsPerPage'] = $itemsPerPage;
        }

        if (null !== $page) {
            $queryParameters['page'] = $page;
        }

        if (is_array($type)) {
            $type = ObjectSerializer::serializeCollection($type, 'form', true);
        }
        if (null !== $type) {
            $queryParameters['type'] = $type;
        }

        if (is_array($platform)) {
            $platform = ObjectSerializer::serializeCollection($platform, 'form', true);
        }
        if (null !== $platform) {
            $queryParameters['platform'] = $platform;
        }

        if (null !== $sort) {
            $queryParameters['sort'] = $sort;
        }

        if (null !== $order) {
            $queryParameters['order'] = $order;
        }

        return $this->sendRequest('GET', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * Retrieves a list of destinations.
     *
     * Required API Key ACLs:
     *  - addObject
     *  - deleteIndex
     *  - editSettings
     *
     * @param int   $itemsPerPage     Number of items per page. (optional, default to 10)
     * @param int   $page             Page number of the paginated API response. (optional)
     * @param array $type             Destination type. (optional)
     * @param array $authenticationID Authentication ID used by destinations. (optional)
     * @param array $sort             Property by which to sort the destinations. (optional)
     * @param array $order            Sort order of the response, ascending or descending. (optional)
     * @param array $requestOptions   the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return \Algolia\AlgoliaSearch\Model\Ingestion\ListDestinationsResponse|array<string, mixed>
     */
    public function listDestinations($itemsPerPage = null, $page = null, $type = null, $authenticationID = null, $sort = null, $order = null, $requestOptions = [])
    {
        if (null !== $itemsPerPage && $itemsPerPage > 100) {
            throw new \InvalidArgumentException('invalid value for "$itemsPerPage" when calling IngestionClient.listDestinations, must be smaller than or equal to 100.');
        }
        if (null !== $itemsPerPage && $itemsPerPage < 1) {
            throw new \InvalidArgumentException('invalid value for "$itemsPerPage" when calling IngestionClient.listDestinations, must be bigger than or equal to 1.');
        }

        if (null !== $page && $page < 1) {
            throw new \InvalidArgumentException('invalid value for "$page" when calling IngestionClient.listDestinations, must be bigger than or equal to 1.');
        }

        $resourcePath = '/1/destinations';
        $queryParameters = [];
        $headers = [];
        $httpBody = null;

        if (null !== $itemsPerPage) {
            $queryParameters['itemsPerPage'] = $itemsPerPage;
        }

        if (null !== $page) {
            $queryParameters['page'] = $page;
        }

        if (is_array($type)) {
            $type = ObjectSerializer::serializeCollection($type, 'form', true);
        }
        if (null !== $type) {
            $queryParameters['type'] = $type;
        }

        if (is_array($authenticationID)) {
            $authenticationID = ObjectSerializer::serializeCollection($authenticationID, 'form', true);
        }
        if (null !== $authenticationID) {
            $queryParameters['authenticationID'] = $authenticationID;
        }

        if (null !== $sort) {
            $queryParameters['sort'] = $sort;
        }

        if (null !== $order) {
            $queryParameters['order'] = $order;
        }

        return $this->sendRequest('GET', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * Retrieves a list of events for a task run, identified by it's ID.
     *
     * Required API Key ACLs:
     *  - addObject
     *  - deleteIndex
     *  - editSettings
     *
     * @param string $runID          Unique identifier of a task run. (required)
     * @param int    $itemsPerPage   Number of items per page. (optional, default to 10)
     * @param int    $page           Page number of the paginated API response. (optional)
     * @param array  $status         Event status for filtering the list of task runs. (optional)
     * @param array  $type           Event type for filtering the list of task runs. (optional)
     * @param array  $sort           Property by which to sort the list of task run events. (optional)
     * @param array  $order          Sort order of the response, ascending or descending. (optional)
     * @param string $startDate      Date and time in RFC 3339 format for the earliest events to retrieve. By default, the current time minus three hours is used. (optional)
     * @param string $endDate        Date and time in RFC 3339 format for the latest events to retrieve. By default, the current time is used. (optional)
     * @param array  $requestOptions the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return \Algolia\AlgoliaSearch\Model\Ingestion\ListEventsResponse|array<string, mixed>
     */
    public function listEvents($runID, $itemsPerPage = null, $page = null, $status = null, $type = null, $sort = null, $order = null, $startDate = null, $endDate = null, $requestOptions = [])
    {
        // verify the required parameter 'runID' is set
        if (!isset($runID)) {
            throw new \InvalidArgumentException(
                'Parameter `runID` is required when calling `listEvents`.'
            );
        }
        if (null !== $itemsPerPage && $itemsPerPage > 100) {
            throw new \InvalidArgumentException('invalid value for "$itemsPerPage" when calling IngestionClient.listEvents, must be smaller than or equal to 100.');
        }
        if (null !== $itemsPerPage && $itemsPerPage < 1) {
            throw new \InvalidArgumentException('invalid value for "$itemsPerPage" when calling IngestionClient.listEvents, must be bigger than or equal to 1.');
        }

        if (null !== $page && $page < 1) {
            throw new \InvalidArgumentException('invalid value for "$page" when calling IngestionClient.listEvents, must be bigger than or equal to 1.');
        }

        $resourcePath = '/1/runs/{runID}/events';
        $queryParameters = [];
        $headers = [];
        $httpBody = null;

        if (null !== $itemsPerPage) {
            $queryParameters['itemsPerPage'] = $itemsPerPage;
        }

        if (null !== $page) {
            $queryParameters['page'] = $page;
        }

        if (null !== $status) {
            $queryParameters['status'] = $status;
        }

        if (null !== $type) {
            $queryParameters['type'] = $type;
        }

        if (null !== $sort) {
            $queryParameters['sort'] = $sort;
        }

        if (null !== $order) {
            $queryParameters['order'] = $order;
        }

        if (null !== $startDate) {
            $queryParameters['startDate'] = $startDate;
        }

        if (null !== $endDate) {
            $queryParameters['endDate'] = $endDate;
        }

        // path params
        if (null !== $runID) {
            $resourcePath = str_replace(
                '{runID}',
                ObjectSerializer::toPathValue($runID),
                $resourcePath
            );
        }

        return $this->sendRequest('GET', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * Retrieve a list of task runs.
     *
     * Required API Key ACLs:
     *  - addObject
     *  - deleteIndex
     *  - editSettings
     *
     * @param int    $itemsPerPage   Number of items per page. (optional, default to 10)
     * @param int    $page           Page number of the paginated API response. (optional)
     * @param array  $status         Run status for filtering the list of task runs. (optional)
     * @param string $taskID         Task ID for filtering the list of task runs. (optional)
     * @param array  $sort           Property by which to sort the list of task runs. (optional)
     * @param array  $order          Sort order of the response, ascending or descending. (optional)
     * @param string $startDate      Date in RFC 3339 format for the earliest run to retrieve. By default, the current day minus seven days is used. (optional)
     * @param string $endDate        Date in RFC 3339 format for the latest run to retrieve. By default, the current day is used. (optional)
     * @param array  $requestOptions the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return \Algolia\AlgoliaSearch\Model\Ingestion\RunListResponse|array<string, mixed>
     */
    public function listRuns($itemsPerPage = null, $page = null, $status = null, $taskID = null, $sort = null, $order = null, $startDate = null, $endDate = null, $requestOptions = [])
    {
        if (null !== $itemsPerPage && $itemsPerPage > 100) {
            throw new \InvalidArgumentException('invalid value for "$itemsPerPage" when calling IngestionClient.listRuns, must be smaller than or equal to 100.');
        }
        if (null !== $itemsPerPage && $itemsPerPage < 1) {
            throw new \InvalidArgumentException('invalid value for "$itemsPerPage" when calling IngestionClient.listRuns, must be bigger than or equal to 1.');
        }

        if (null !== $page && $page < 1) {
            throw new \InvalidArgumentException('invalid value for "$page" when calling IngestionClient.listRuns, must be bigger than or equal to 1.');
        }

        $resourcePath = '/1/runs';
        $queryParameters = [];
        $headers = [];
        $httpBody = null;

        if (null !== $itemsPerPage) {
            $queryParameters['itemsPerPage'] = $itemsPerPage;
        }

        if (null !== $page) {
            $queryParameters['page'] = $page;
        }

        if (null !== $status) {
            $queryParameters['status'] = $status;
        }

        if (null !== $taskID) {
            $queryParameters['taskID'] = $taskID;
        }

        if (null !== $sort) {
            $queryParameters['sort'] = $sort;
        }

        if (null !== $order) {
            $queryParameters['order'] = $order;
        }

        if (null !== $startDate) {
            $queryParameters['startDate'] = $startDate;
        }

        if (null !== $endDate) {
            $queryParameters['endDate'] = $endDate;
        }

        return $this->sendRequest('GET', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * Retrieves a list of sources.
     *
     * Required API Key ACLs:
     *  - addObject
     *  - deleteIndex
     *  - editSettings
     *
     * @param int   $itemsPerPage     Number of items per page. (optional, default to 10)
     * @param int   $page             Page number of the paginated API response. (optional)
     * @param array $type             Source type. Some sources require authentication. (optional)
     * @param array $authenticationID Authentication IDs of the sources to retrieve. &#39;none&#39; returns sources that doesn&#39;t have an authentication resource. (optional)
     * @param array $sort             Property by which to sort the list of sources. (optional)
     * @param array $order            Sort order of the response, ascending or descending. (optional)
     * @param array $requestOptions   the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return \Algolia\AlgoliaSearch\Model\Ingestion\ListSourcesResponse|array<string, mixed>
     */
    public function listSources($itemsPerPage = null, $page = null, $type = null, $authenticationID = null, $sort = null, $order = null, $requestOptions = [])
    {
        if (null !== $itemsPerPage && $itemsPerPage > 100) {
            throw new \InvalidArgumentException('invalid value for "$itemsPerPage" when calling IngestionClient.listSources, must be smaller than or equal to 100.');
        }
        if (null !== $itemsPerPage && $itemsPerPage < 1) {
            throw new \InvalidArgumentException('invalid value for "$itemsPerPage" when calling IngestionClient.listSources, must be bigger than or equal to 1.');
        }

        if (null !== $page && $page < 1) {
            throw new \InvalidArgumentException('invalid value for "$page" when calling IngestionClient.listSources, must be bigger than or equal to 1.');
        }

        $resourcePath = '/1/sources';
        $queryParameters = [];
        $headers = [];
        $httpBody = null;

        if (null !== $itemsPerPage) {
            $queryParameters['itemsPerPage'] = $itemsPerPage;
        }

        if (null !== $page) {
            $queryParameters['page'] = $page;
        }

        if (is_array($type)) {
            $type = ObjectSerializer::serializeCollection($type, 'form', true);
        }
        if (null !== $type) {
            $queryParameters['type'] = $type;
        }

        if (is_array($authenticationID)) {
            $authenticationID = ObjectSerializer::serializeCollection($authenticationID, 'form', true);
        }
        if (null !== $authenticationID) {
            $queryParameters['authenticationID'] = $authenticationID;
        }

        if (null !== $sort) {
            $queryParameters['sort'] = $sort;
        }

        if (null !== $order) {
            $queryParameters['order'] = $order;
        }

        return $this->sendRequest('GET', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * Retrieves a list of tasks.
     *
     * Required API Key ACLs:
     *  - addObject
     *  - deleteIndex
     *  - editSettings
     *
     * @param int   $itemsPerPage   Number of items per page. (optional, default to 10)
     * @param int   $page           Page number of the paginated API response. (optional)
     * @param array $action         Actions for filtering the list of tasks. (optional)
     * @param bool  $enabled        Whether to filter the list of tasks by the &#x60;enabled&#x60; status. (optional)
     * @param array $sourceID       Source IDs for filtering the list of tasks. (optional)
     * @param array $destinationID  Destination IDs for filtering the list of tasks. (optional)
     * @param array $triggerType    Type of task trigger for filtering the list of tasks. (optional)
     * @param array $sort           Property by which to sort the list of tasks. (optional)
     * @param array $order          Sort order of the response, ascending or descending. (optional)
     * @param array $requestOptions the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return \Algolia\AlgoliaSearch\Model\Ingestion\ListTasksResponse|array<string, mixed>
     */
    public function listTasks($itemsPerPage = null, $page = null, $action = null, $enabled = null, $sourceID = null, $destinationID = null, $triggerType = null, $sort = null, $order = null, $requestOptions = [])
    {
        if (null !== $itemsPerPage && $itemsPerPage > 100) {
            throw new \InvalidArgumentException('invalid value for "$itemsPerPage" when calling IngestionClient.listTasks, must be smaller than or equal to 100.');
        }
        if (null !== $itemsPerPage && $itemsPerPage < 1) {
            throw new \InvalidArgumentException('invalid value for "$itemsPerPage" when calling IngestionClient.listTasks, must be bigger than or equal to 1.');
        }

        if (null !== $page && $page < 1) {
            throw new \InvalidArgumentException('invalid value for "$page" when calling IngestionClient.listTasks, must be bigger than or equal to 1.');
        }

        $resourcePath = '/2/tasks';
        $queryParameters = [];
        $headers = [];
        $httpBody = null;

        if (null !== $itemsPerPage) {
            $queryParameters['itemsPerPage'] = $itemsPerPage;
        }

        if (null !== $page) {
            $queryParameters['page'] = $page;
        }

        if (is_array($action)) {
            $action = ObjectSerializer::serializeCollection($action, 'form', true);
        }
        if (null !== $action) {
            $queryParameters['action'] = $action;
        }

        if (null !== $enabled) {
            $queryParameters['enabled'] = $enabled;
        }

        if (is_array($sourceID)) {
            $sourceID = ObjectSerializer::serializeCollection($sourceID, 'form', true);
        }
        if (null !== $sourceID) {
            $queryParameters['sourceID'] = $sourceID;
        }

        if (is_array($destinationID)) {
            $destinationID = ObjectSerializer::serializeCollection($destinationID, 'form', true);
        }
        if (null !== $destinationID) {
            $queryParameters['destinationID'] = $destinationID;
        }

        if (is_array($triggerType)) {
            $triggerType = ObjectSerializer::serializeCollection($triggerType, 'form', true);
        }
        if (null !== $triggerType) {
            $queryParameters['triggerType'] = $triggerType;
        }

        if (null !== $sort) {
            $queryParameters['sort'] = $sort;
        }

        if (null !== $order) {
            $queryParameters['order'] = $order;
        }

        return $this->sendRequest('GET', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * Retrieves a list of tasks using the v1 endpoint, please use `getTasks` instead.
     *
     * Required API Key ACLs:
     *  - addObject
     *  - deleteIndex
     *  - editSettings
     *
     * @param int   $itemsPerPage   Number of items per page. (optional, default to 10)
     * @param int   $page           Page number of the paginated API response. (optional)
     * @param array $action         Actions for filtering the list of tasks. (optional)
     * @param bool  $enabled        Whether to filter the list of tasks by the &#x60;enabled&#x60; status. (optional)
     * @param array $sourceID       Source IDs for filtering the list of tasks. (optional)
     * @param array $destinationID  Destination IDs for filtering the list of tasks. (optional)
     * @param array $triggerType    Type of task trigger for filtering the list of tasks. (optional)
     * @param array $sort           Property by which to sort the list of tasks. (optional)
     * @param array $order          Sort order of the response, ascending or descending. (optional)
     * @param array $requestOptions the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return \Algolia\AlgoliaSearch\Model\Ingestion\ListTasksResponseV1|array<string, mixed>
     */
    public function listTasksV1($itemsPerPage = null, $page = null, $action = null, $enabled = null, $sourceID = null, $destinationID = null, $triggerType = null, $sort = null, $order = null, $requestOptions = [])
    {
        if (null !== $itemsPerPage && $itemsPerPage > 100) {
            throw new \InvalidArgumentException('invalid value for "$itemsPerPage" when calling IngestionClient.listTasksV1, must be smaller than or equal to 100.');
        }
        if (null !== $itemsPerPage && $itemsPerPage < 1) {
            throw new \InvalidArgumentException('invalid value for "$itemsPerPage" when calling IngestionClient.listTasksV1, must be bigger than or equal to 1.');
        }

        if (null !== $page && $page < 1) {
            throw new \InvalidArgumentException('invalid value for "$page" when calling IngestionClient.listTasksV1, must be bigger than or equal to 1.');
        }

        $resourcePath = '/1/tasks';
        $queryParameters = [];
        $headers = [];
        $httpBody = null;

        if (null !== $itemsPerPage) {
            $queryParameters['itemsPerPage'] = $itemsPerPage;
        }

        if (null !== $page) {
            $queryParameters['page'] = $page;
        }

        if (is_array($action)) {
            $action = ObjectSerializer::serializeCollection($action, 'form', true);
        }
        if (null !== $action) {
            $queryParameters['action'] = $action;
        }

        if (null !== $enabled) {
            $queryParameters['enabled'] = $enabled;
        }

        if (is_array($sourceID)) {
            $sourceID = ObjectSerializer::serializeCollection($sourceID, 'form', true);
        }
        if (null !== $sourceID) {
            $queryParameters['sourceID'] = $sourceID;
        }

        if (is_array($destinationID)) {
            $destinationID = ObjectSerializer::serializeCollection($destinationID, 'form', true);
        }
        if (null !== $destinationID) {
            $queryParameters['destinationID'] = $destinationID;
        }

        if (is_array($triggerType)) {
            $triggerType = ObjectSerializer::serializeCollection($triggerType, 'form', true);
        }
        if (null !== $triggerType) {
            $queryParameters['triggerType'] = $triggerType;
        }

        if (null !== $sort) {
            $queryParameters['sort'] = $sort;
        }

        if (null !== $order) {
            $queryParameters['order'] = $order;
        }

        return $this->sendRequest('GET', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * Retrieves a list of existing LLM transformation helpers.
     *
     * Required API Key ACLs:
     *  - addObject
     *  - deleteIndex
     *  - editSettings
     *
     * @param array $requestOptions the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return \Algolia\AlgoliaSearch\Model\Ingestion\TransformationModels|array<string, mixed>
     */
    public function listTransformationModels($requestOptions = [])
    {
        $resourcePath = '/1/transformations/models';
        $queryParameters = [];
        $headers = [];
        $httpBody = null;

        return $this->sendRequest('GET', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * Retrieves a list of transformations.
     *
     * Required API Key ACLs:
     *  - addObject
     *  - deleteIndex
     *  - editSettings
     *
     * @param int   $itemsPerPage   Number of items per page. (optional, default to 10)
     * @param int   $page           Page number of the paginated API response. (optional)
     * @param array $sort           Property by which to sort the list. (optional)
     * @param array $order          Sort order of the response, ascending or descending. (optional)
     * @param array $requestOptions the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return \Algolia\AlgoliaSearch\Model\Ingestion\ListTransformationsResponse|array<string, mixed>
     */
    public function listTransformations($itemsPerPage = null, $page = null, $sort = null, $order = null, $requestOptions = [])
    {
        if (null !== $itemsPerPage && $itemsPerPage > 100) {
            throw new \InvalidArgumentException('invalid value for "$itemsPerPage" when calling IngestionClient.listTransformations, must be smaller than or equal to 100.');
        }
        if (null !== $itemsPerPage && $itemsPerPage < 1) {
            throw new \InvalidArgumentException('invalid value for "$itemsPerPage" when calling IngestionClient.listTransformations, must be bigger than or equal to 1.');
        }

        if (null !== $page && $page < 1) {
            throw new \InvalidArgumentException('invalid value for "$page" when calling IngestionClient.listTransformations, must be bigger than or equal to 1.');
        }

        $resourcePath = '/1/transformations';
        $queryParameters = [];
        $headers = [];
        $httpBody = null;

        if (null !== $itemsPerPage) {
            $queryParameters['itemsPerPage'] = $itemsPerPage;
        }

        if (null !== $page) {
            $queryParameters['page'] = $page;
        }

        if (null !== $sort) {
            $queryParameters['sort'] = $sort;
        }

        if (null !== $order) {
            $queryParameters['order'] = $order;
        }

        return $this->sendRequest('GET', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * Push a `batch` request payload through the Pipeline. You can check the status of task pushes with the observability endpoints.
     *
     * Required API Key ACLs:
     *  - addObject
     *  - deleteIndex
     *  - editSettings
     *
     * @param string $taskID           Unique identifier of a task. (required)
     * @param array  $batchWriteParams Request body of a Search API &#x60;batch&#x60; request that will be pushed in the Connectors pipeline. (required)
     *                                 - $batchWriteParams['requests'] => (array)  (required)
     *
     * @see BatchWriteParams
     *
     * @param array $requestOptions the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return \Algolia\AlgoliaSearch\Model\Ingestion\RunResponse|array<string, mixed>
     */
    public function pushTask($taskID, $batchWriteParams, $requestOptions = [])
    {
        // verify the required parameter 'taskID' is set
        if (!isset($taskID)) {
            throw new \InvalidArgumentException(
                'Parameter `taskID` is required when calling `pushTask`.'
            );
        }
        // verify the required parameter 'batchWriteParams' is set
        if (!isset($batchWriteParams)) {
            throw new \InvalidArgumentException(
                'Parameter `batchWriteParams` is required when calling `pushTask`.'
            );
        }

        $resourcePath = '/2/tasks/{taskID}/push';
        $queryParameters = [];
        $headers = [];
        $httpBody = $batchWriteParams;

        // path params
        if (null !== $taskID) {
            $resourcePath = str_replace(
                '{taskID}',
                ObjectSerializer::toPathValue($taskID),
                $resourcePath
            );
        }

        return $this->sendRequest('POST', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * Runs all tasks linked to a source, only available for Shopify sources. It will create 1 run per task.
     *
     * Required API Key ACLs:
     *  - addObject
     *  - deleteIndex
     *  - editSettings
     *
     * @param string $sourceID         Unique identifier of a source. (required)
     * @param array  $runSourcePayload (optional)
     *                                 - $runSourcePayload['indexToInclude'] => (array) List of index names to include in reidexing/update.
     *                                 - $runSourcePayload['indexToExclude'] => (array) List of index names to exclude in reidexing/update.
     *                                 - $runSourcePayload['entityIDs'] => (array) List of entityID to update.
     *                                 - $runSourcePayload['entityType'] => (array)
     *
     * @see RunSourcePayload
     *
     * @param array $requestOptions the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return \Algolia\AlgoliaSearch\Model\Ingestion\RunSourceResponse|array<string, mixed>
     */
    public function runSource($sourceID, $runSourcePayload = null, $requestOptions = [])
    {
        // verify the required parameter 'sourceID' is set
        if (!isset($sourceID)) {
            throw new \InvalidArgumentException(
                'Parameter `sourceID` is required when calling `runSource`.'
            );
        }

        $resourcePath = '/1/sources/{sourceID}/run';
        $queryParameters = [];
        $headers = [];
        $httpBody = isset($runSourcePayload) ? $runSourcePayload : [];

        // path params
        if (null !== $sourceID) {
            $resourcePath = str_replace(
                '{sourceID}',
                ObjectSerializer::toPathValue($sourceID),
                $resourcePath
            );
        }

        return $this->sendRequest('POST', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * Runs a task. You can check the status of task runs with the observability endpoints.
     *
     * Required API Key ACLs:
     *  - addObject
     *  - deleteIndex
     *  - editSettings
     *
     * @param string $taskID         Unique identifier of a task. (required)
     * @param array  $requestOptions the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return \Algolia\AlgoliaSearch\Model\Ingestion\RunResponse|array<string, mixed>
     */
    public function runTask($taskID, $requestOptions = [])
    {
        // verify the required parameter 'taskID' is set
        if (!isset($taskID)) {
            throw new \InvalidArgumentException(
                'Parameter `taskID` is required when calling `runTask`.'
            );
        }

        $resourcePath = '/2/tasks/{taskID}/run';
        $queryParameters = [];
        $headers = [];
        $httpBody = null;

        // path params
        if (null !== $taskID) {
            $resourcePath = str_replace(
                '{taskID}',
                ObjectSerializer::toPathValue($taskID),
                $resourcePath
            );
        }

        return $this->sendRequest('POST', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * Runs a task using the v1 endpoint, please use `runTask` instead. You can check the status of task runs with the observability endpoints.
     *
     * Required API Key ACLs:
     *  - addObject
     *  - deleteIndex
     *  - editSettings
     *
     * @param string $taskID         Unique identifier of a task. (required)
     * @param array  $requestOptions the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return \Algolia\AlgoliaSearch\Model\Ingestion\RunResponse|array<string, mixed>
     */
    public function runTaskV1($taskID, $requestOptions = [])
    {
        // verify the required parameter 'taskID' is set
        if (!isset($taskID)) {
            throw new \InvalidArgumentException(
                'Parameter `taskID` is required when calling `runTaskV1`.'
            );
        }

        $resourcePath = '/1/tasks/{taskID}/run';
        $queryParameters = [];
        $headers = [];
        $httpBody = null;

        // path params
        if (null !== $taskID) {
            $resourcePath = str_replace(
                '{taskID}',
                ObjectSerializer::toPathValue($taskID),
                $resourcePath
            );
        }

        return $this->sendRequest('POST', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * Searches for authentication resources.
     *
     * Required API Key ACLs:
     *  - addObject
     *  - deleteIndex
     *  - editSettings
     *
     * @param array $authenticationSearch authenticationSearch (required)
     *                                    - $authenticationSearch['authenticationIDs'] => (array)  (required)
     *
     * @see AuthenticationSearch
     *
     * @param array $requestOptions the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return \Algolia\AlgoliaSearch\Model\Ingestion\Authentication[]|array<string, mixed>
     */
    public function searchAuthentications($authenticationSearch, $requestOptions = [])
    {
        // verify the required parameter 'authenticationSearch' is set
        if (!isset($authenticationSearch)) {
            throw new \InvalidArgumentException(
                'Parameter `authenticationSearch` is required when calling `searchAuthentications`.'
            );
        }

        $resourcePath = '/1/authentications/search';
        $queryParameters = [];
        $headers = [];
        $httpBody = $authenticationSearch;

        return $this->sendRequest('POST', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * Searches for destinations.
     *
     * Required API Key ACLs:
     *  - addObject
     *  - deleteIndex
     *  - editSettings
     *
     * @param array $destinationSearch destinationSearch (required)
     *                                 - $destinationSearch['destinationIDs'] => (array)  (required)
     *
     * @see DestinationSearch
     *
     * @param array $requestOptions the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return \Algolia\AlgoliaSearch\Model\Ingestion\Destination[]|array<string, mixed>
     */
    public function searchDestinations($destinationSearch, $requestOptions = [])
    {
        // verify the required parameter 'destinationSearch' is set
        if (!isset($destinationSearch)) {
            throw new \InvalidArgumentException(
                'Parameter `destinationSearch` is required when calling `searchDestinations`.'
            );
        }

        $resourcePath = '/1/destinations/search';
        $queryParameters = [];
        $headers = [];
        $httpBody = $destinationSearch;

        return $this->sendRequest('POST', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * Searches for sources.
     *
     * Required API Key ACLs:
     *  - addObject
     *  - deleteIndex
     *  - editSettings
     *
     * @param array $sourceSearch sourceSearch (required)
     *                            - $sourceSearch['sourceIDs'] => (array)  (required)
     *
     * @see SourceSearch
     *
     * @param array $requestOptions the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return \Algolia\AlgoliaSearch\Model\Ingestion\Source[]|array<string, mixed>
     */
    public function searchSources($sourceSearch, $requestOptions = [])
    {
        // verify the required parameter 'sourceSearch' is set
        if (!isset($sourceSearch)) {
            throw new \InvalidArgumentException(
                'Parameter `sourceSearch` is required when calling `searchSources`.'
            );
        }

        $resourcePath = '/1/sources/search';
        $queryParameters = [];
        $headers = [];
        $httpBody = $sourceSearch;

        return $this->sendRequest('POST', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * Searches for tasks.
     *
     * Required API Key ACLs:
     *  - addObject
     *  - deleteIndex
     *  - editSettings
     *
     * @param array $taskSearch taskSearch (required)
     *                          - $taskSearch['taskIDs'] => (array)  (required)
     *
     * @see TaskSearch
     *
     * @param array $requestOptions the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return \Algolia\AlgoliaSearch\Model\Ingestion\Task[]|array<string, mixed>
     */
    public function searchTasks($taskSearch, $requestOptions = [])
    {
        // verify the required parameter 'taskSearch' is set
        if (!isset($taskSearch)) {
            throw new \InvalidArgumentException(
                'Parameter `taskSearch` is required when calling `searchTasks`.'
            );
        }

        $resourcePath = '/2/tasks/search';
        $queryParameters = [];
        $headers = [];
        $httpBody = $taskSearch;

        return $this->sendRequest('POST', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * Searches for tasks using the v1 endpoint, please use `searchTasks` instead.
     *
     * Required API Key ACLs:
     *  - addObject
     *  - deleteIndex
     *  - editSettings
     *
     * @param array $taskSearch taskSearch (required)
     *                          - $taskSearch['taskIDs'] => (array)  (required)
     *
     * @see TaskSearch
     *
     * @param array $requestOptions the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return \Algolia\AlgoliaSearch\Model\Ingestion\TaskV1[]|array<string, mixed>
     */
    public function searchTasksV1($taskSearch, $requestOptions = [])
    {
        // verify the required parameter 'taskSearch' is set
        if (!isset($taskSearch)) {
            throw new \InvalidArgumentException(
                'Parameter `taskSearch` is required when calling `searchTasksV1`.'
            );
        }

        $resourcePath = '/1/tasks/search';
        $queryParameters = [];
        $headers = [];
        $httpBody = $taskSearch;

        return $this->sendRequest('POST', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * Searches for transformations.
     *
     * Required API Key ACLs:
     *  - addObject
     *  - deleteIndex
     *  - editSettings
     *
     * @param array $transformationSearch transformationSearch (required)
     *                                    - $transformationSearch['transformationIDs'] => (array)  (required)
     *
     * @see TransformationSearch
     *
     * @param array $requestOptions the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return \Algolia\AlgoliaSearch\Model\Ingestion\Transformation[]|array<string, mixed>
     */
    public function searchTransformations($transformationSearch, $requestOptions = [])
    {
        // verify the required parameter 'transformationSearch' is set
        if (!isset($transformationSearch)) {
            throw new \InvalidArgumentException(
                'Parameter `transformationSearch` is required when calling `searchTransformations`.'
            );
        }

        $resourcePath = '/1/transformations/search';
        $queryParameters = [];
        $headers = [];
        $httpBody = $transformationSearch;

        return $this->sendRequest('POST', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * Triggers a stream-listing request for a source. Triggering stream-listing requests only works with sources with `type: docker` and `imageType: singer`.
     *
     * Required API Key ACLs:
     *  - addObject
     *  - deleteIndex
     *  - editSettings
     *
     * @param string $sourceID       Unique identifier of a source. (required)
     * @param array  $requestOptions the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return \Algolia\AlgoliaSearch\Model\Ingestion\SourceWatchResponse|array<string, mixed>
     */
    public function triggerDockerSourceDiscover($sourceID, $requestOptions = [])
    {
        // verify the required parameter 'sourceID' is set
        if (!isset($sourceID)) {
            throw new \InvalidArgumentException(
                'Parameter `sourceID` is required when calling `triggerDockerSourceDiscover`.'
            );
        }

        $resourcePath = '/1/sources/{sourceID}/discover';
        $queryParameters = [];
        $headers = [];
        $httpBody = null;

        // path params
        if (null !== $sourceID) {
            $resourcePath = str_replace(
                '{sourceID}',
                ObjectSerializer::toPathValue($sourceID),
                $resourcePath
            );
        }

        return $this->sendRequest('POST', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * Try a transformation before creating it.
     *
     * Required API Key ACLs:
     *  - addObject
     *  - deleteIndex
     *  - editSettings
     *
     * @param array $transformationTry transformationTry (required)
     *                                 - $transformationTry['code'] => (string) The source code of the transformation. (required)
     *                                 - $transformationTry['sampleRecord'] => (array) The record to apply the given code to. (required)
     *                                 - $transformationTry['authentications'] => (array)
     *
     * @see TransformationTry
     *
     * @param array $requestOptions the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return \Algolia\AlgoliaSearch\Model\Ingestion\TransformationTryResponse|array<string, mixed>
     */
    public function tryTransformation($transformationTry, $requestOptions = [])
    {
        // verify the required parameter 'transformationTry' is set
        if (!isset($transformationTry)) {
            throw new \InvalidArgumentException(
                'Parameter `transformationTry` is required when calling `tryTransformation`.'
            );
        }

        $resourcePath = '/1/transformations/try';
        $queryParameters = [];
        $headers = [];
        $httpBody = $transformationTry;

        return $this->sendRequest('POST', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * Try a transformation before updating it.
     *
     * Required API Key ACLs:
     *  - addObject
     *  - deleteIndex
     *  - editSettings
     *
     * @param string $transformationID  Unique identifier of a transformation. (required)
     * @param array  $transformationTry transformationTry (required)
     *                                  - $transformationTry['code'] => (string) The source code of the transformation. (required)
     *                                  - $transformationTry['sampleRecord'] => (array) The record to apply the given code to. (required)
     *                                  - $transformationTry['authentications'] => (array)
     *
     * @see TransformationTry
     *
     * @param array $requestOptions the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return \Algolia\AlgoliaSearch\Model\Ingestion\TransformationTryResponse|array<string, mixed>
     */
    public function tryTransformationBeforeUpdate($transformationID, $transformationTry, $requestOptions = [])
    {
        // verify the required parameter 'transformationID' is set
        if (!isset($transformationID)) {
            throw new \InvalidArgumentException(
                'Parameter `transformationID` is required when calling `tryTransformationBeforeUpdate`.'
            );
        }
        // verify the required parameter 'transformationTry' is set
        if (!isset($transformationTry)) {
            throw new \InvalidArgumentException(
                'Parameter `transformationTry` is required when calling `tryTransformationBeforeUpdate`.'
            );
        }

        $resourcePath = '/1/transformations/{transformationID}/try';
        $queryParameters = [];
        $headers = [];
        $httpBody = $transformationTry;

        // path params
        if (null !== $transformationID) {
            $resourcePath = str_replace(
                '{transformationID}',
                ObjectSerializer::toPathValue($transformationID),
                $resourcePath
            );
        }

        return $this->sendRequest('POST', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * Updates an authentication resource.
     *
     * Required API Key ACLs:
     *  - addObject
     *  - deleteIndex
     *  - editSettings
     *
     * @param string $authenticationID     Unique identifier of an authentication resource. (required)
     * @param array  $authenticationUpdate authenticationUpdate (required)
     *                                     - $authenticationUpdate['type'] => (array)
     *                                     - $authenticationUpdate['name'] => (string) Descriptive name for the resource.
     *                                     - $authenticationUpdate['platform'] => (array)
     *                                     - $authenticationUpdate['input'] => (array)
     *
     * @see AuthenticationUpdate
     *
     * @param array $requestOptions the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return \Algolia\AlgoliaSearch\Model\Ingestion\AuthenticationUpdateResponse|array<string, mixed>
     */
    public function updateAuthentication($authenticationID, $authenticationUpdate, $requestOptions = [])
    {
        // verify the required parameter 'authenticationID' is set
        if (!isset($authenticationID)) {
            throw new \InvalidArgumentException(
                'Parameter `authenticationID` is required when calling `updateAuthentication`.'
            );
        }
        // verify the required parameter 'authenticationUpdate' is set
        if (!isset($authenticationUpdate)) {
            throw new \InvalidArgumentException(
                'Parameter `authenticationUpdate` is required when calling `updateAuthentication`.'
            );
        }

        $resourcePath = '/1/authentications/{authenticationID}';
        $queryParameters = [];
        $headers = [];
        $httpBody = $authenticationUpdate;

        // path params
        if (null !== $authenticationID) {
            $resourcePath = str_replace(
                '{authenticationID}',
                ObjectSerializer::toPathValue($authenticationID),
                $resourcePath
            );
        }

        return $this->sendRequest('PATCH', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * Updates the destination by its ID.
     *
     * Required API Key ACLs:
     *  - addObject
     *  - deleteIndex
     *  - editSettings
     *
     * @param string $destinationID     Unique identifier of a destination. (required)
     * @param array  $destinationUpdate destinationUpdate (required)
     *                                  - $destinationUpdate['type'] => (array)
     *                                  - $destinationUpdate['name'] => (string) Descriptive name for the resource.
     *                                  - $destinationUpdate['input'] => (array)
     *                                  - $destinationUpdate['authenticationID'] => (string) Universally unique identifier (UUID) of an authentication resource.
     *                                  - $destinationUpdate['transformationIDs'] => (array)
     *
     * @see DestinationUpdate
     *
     * @param array $requestOptions the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return \Algolia\AlgoliaSearch\Model\Ingestion\DestinationUpdateResponse|array<string, mixed>
     */
    public function updateDestination($destinationID, $destinationUpdate, $requestOptions = [])
    {
        // verify the required parameter 'destinationID' is set
        if (!isset($destinationID)) {
            throw new \InvalidArgumentException(
                'Parameter `destinationID` is required when calling `updateDestination`.'
            );
        }
        // verify the required parameter 'destinationUpdate' is set
        if (!isset($destinationUpdate)) {
            throw new \InvalidArgumentException(
                'Parameter `destinationUpdate` is required when calling `updateDestination`.'
            );
        }

        $resourcePath = '/1/destinations/{destinationID}';
        $queryParameters = [];
        $headers = [];
        $httpBody = $destinationUpdate;

        // path params
        if (null !== $destinationID) {
            $resourcePath = str_replace(
                '{destinationID}',
                ObjectSerializer::toPathValue($destinationID),
                $resourcePath
            );
        }

        return $this->sendRequest('PATCH', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * Updates a source by its ID.
     *
     * Required API Key ACLs:
     *  - addObject
     *  - deleteIndex
     *  - editSettings
     *
     * @param string $sourceID     Unique identifier of a source. (required)
     * @param array  $sourceUpdate sourceUpdate (required)
     *                             - $sourceUpdate['name'] => (string) Descriptive name of the source.
     *                             - $sourceUpdate['input'] => (array)
     *                             - $sourceUpdate['authenticationID'] => (string) Universally unique identifier (UUID) of an authentication resource.
     *
     * @see SourceUpdate
     *
     * @param array $requestOptions the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return \Algolia\AlgoliaSearch\Model\Ingestion\SourceUpdateResponse|array<string, mixed>
     */
    public function updateSource($sourceID, $sourceUpdate, $requestOptions = [])
    {
        // verify the required parameter 'sourceID' is set
        if (!isset($sourceID)) {
            throw new \InvalidArgumentException(
                'Parameter `sourceID` is required when calling `updateSource`.'
            );
        }
        // verify the required parameter 'sourceUpdate' is set
        if (!isset($sourceUpdate)) {
            throw new \InvalidArgumentException(
                'Parameter `sourceUpdate` is required when calling `updateSource`.'
            );
        }

        $resourcePath = '/1/sources/{sourceID}';
        $queryParameters = [];
        $headers = [];
        $httpBody = $sourceUpdate;

        // path params
        if (null !== $sourceID) {
            $resourcePath = str_replace(
                '{sourceID}',
                ObjectSerializer::toPathValue($sourceID),
                $resourcePath
            );
        }

        return $this->sendRequest('PATCH', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * Updates a task by its ID.
     *
     * @param string $taskID     Unique identifier of a task. (required)
     * @param array  $taskUpdate taskUpdate (required)
     *                           - $taskUpdate['destinationID'] => (string) Universally unique identifier (UUID) of a destination resource.
     *                           - $taskUpdate['cron'] => (string) Cron expression for the task's schedule.
     *                           - $taskUpdate['input'] => (array)
     *                           - $taskUpdate['enabled'] => (bool) Whether the task is enabled.
     *                           - $taskUpdate['failureThreshold'] => (int) Maximum accepted percentage of failures for a task run to finish successfully.
     *
     * @see TaskUpdate
     *
     * @param array $requestOptions the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return \Algolia\AlgoliaSearch\Model\Ingestion\TaskUpdateResponse|array<string, mixed>
     */
    public function updateTask($taskID, $taskUpdate, $requestOptions = [])
    {
        // verify the required parameter 'taskID' is set
        if (!isset($taskID)) {
            throw new \InvalidArgumentException(
                'Parameter `taskID` is required when calling `updateTask`.'
            );
        }
        // verify the required parameter 'taskUpdate' is set
        if (!isset($taskUpdate)) {
            throw new \InvalidArgumentException(
                'Parameter `taskUpdate` is required when calling `updateTask`.'
            );
        }

        $resourcePath = '/2/tasks/{taskID}';
        $queryParameters = [];
        $headers = [];
        $httpBody = $taskUpdate;

        // path params
        if (null !== $taskID) {
            $resourcePath = str_replace(
                '{taskID}',
                ObjectSerializer::toPathValue($taskID),
                $resourcePath
            );
        }

        return $this->sendRequest('PATCH', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * Updates a task by its ID using the v1 endpoint, please use `updateTask` instead.
     *
     * @param string $taskID     Unique identifier of a task. (required)
     * @param array  $taskUpdate taskUpdate (required)
     *                           - $taskUpdate['destinationID'] => (string) Universally unique identifier (UUID) of a destination resource.
     *                           - $taskUpdate['trigger'] => (array)
     *                           - $taskUpdate['input'] => (array)
     *                           - $taskUpdate['enabled'] => (bool) Whether the task is enabled.
     *                           - $taskUpdate['failureThreshold'] => (int) Maximum accepted percentage of failures for a task run to finish successfully.
     *
     * @see TaskUpdateV1
     *
     * @param array $requestOptions the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return \Algolia\AlgoliaSearch\Model\Ingestion\TaskUpdateResponse|array<string, mixed>
     */
    public function updateTaskV1($taskID, $taskUpdate, $requestOptions = [])
    {
        // verify the required parameter 'taskID' is set
        if (!isset($taskID)) {
            throw new \InvalidArgumentException(
                'Parameter `taskID` is required when calling `updateTaskV1`.'
            );
        }
        // verify the required parameter 'taskUpdate' is set
        if (!isset($taskUpdate)) {
            throw new \InvalidArgumentException(
                'Parameter `taskUpdate` is required when calling `updateTaskV1`.'
            );
        }

        $resourcePath = '/1/tasks/{taskID}';
        $queryParameters = [];
        $headers = [];
        $httpBody = $taskUpdate;

        // path params
        if (null !== $taskID) {
            $resourcePath = str_replace(
                '{taskID}',
                ObjectSerializer::toPathValue($taskID),
                $resourcePath
            );
        }

        return $this->sendRequest('PATCH', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * Updates a transformation by its ID.
     *
     * @param string $transformationID     Unique identifier of a transformation. (required)
     * @param array  $transformationCreate transformationCreate (required)
     *                                     - $transformationCreate['code'] => (string) The source code of the transformation. (required)
     *                                     - $transformationCreate['name'] => (string) The uniquely identified name of your transformation. (required)
     *                                     - $transformationCreate['description'] => (string) A descriptive name for your transformation of what it does.
     *                                     - $transformationCreate['authenticationIDs'] => (array) The authentications associated for the current transformation.
     *
     * @see TransformationCreate
     *
     * @param array $requestOptions the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return \Algolia\AlgoliaSearch\Model\Ingestion\TransformationUpdateResponse|array<string, mixed>
     */
    public function updateTransformation($transformationID, $transformationCreate, $requestOptions = [])
    {
        // verify the required parameter 'transformationID' is set
        if (!isset($transformationID)) {
            throw new \InvalidArgumentException(
                'Parameter `transformationID` is required when calling `updateTransformation`.'
            );
        }
        // verify the required parameter 'transformationCreate' is set
        if (!isset($transformationCreate)) {
            throw new \InvalidArgumentException(
                'Parameter `transformationCreate` is required when calling `updateTransformation`.'
            );
        }

        $resourcePath = '/1/transformations/{transformationID}';
        $queryParameters = [];
        $headers = [];
        $httpBody = $transformationCreate;

        // path params
        if (null !== $transformationID) {
            $resourcePath = str_replace(
                '{transformationID}',
                ObjectSerializer::toPathValue($transformationID),
                $resourcePath
            );
        }

        return $this->sendRequest('PUT', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * Validates a source payload to ensure it can be created and that the data source can be reached by Algolia.
     *
     * Required API Key ACLs:
     *  - addObject
     *  - deleteIndex
     *  - editSettings
     *
     * @param array $sourceCreate (optional)
     *                            - $sourceCreate['type'] => (array)  (required)
     *                            - $sourceCreate['name'] => (string) Descriptive name of the source. (required)
     *                            - $sourceCreate['input'] => (array)  (required)
     *                            - $sourceCreate['authenticationID'] => (string) Universally unique identifier (UUID) of an authentication resource.
     *
     * @see SourceCreate
     *
     * @param array $requestOptions the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return \Algolia\AlgoliaSearch\Model\Ingestion\SourceWatchResponse|array<string, mixed>
     */
    public function validateSource($sourceCreate = null, $requestOptions = [])
    {
        $resourcePath = '/1/sources/validate';
        $queryParameters = [];
        $headers = [];
        $httpBody = isset($sourceCreate) ? $sourceCreate : [];

        return $this->sendRequest('POST', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    /**
     * Validates an update of a source payload to ensure it can be created and that the data source can be reached by Algolia.
     *
     * Required API Key ACLs:
     *  - addObject
     *  - deleteIndex
     *  - editSettings
     *
     * @param string $sourceID     Unique identifier of a source. (required)
     * @param array  $sourceUpdate sourceUpdate (required)
     *                             - $sourceUpdate['name'] => (string) Descriptive name of the source.
     *                             - $sourceUpdate['input'] => (array)
     *                             - $sourceUpdate['authenticationID'] => (string) Universally unique identifier (UUID) of an authentication resource.
     *
     * @see SourceUpdate
     *
     * @param array $requestOptions the requestOptions to send along with the query, they will be merged with the transporter requestOptions
     *
     * @return \Algolia\AlgoliaSearch\Model\Ingestion\SourceWatchResponse|array<string, mixed>
     */
    public function validateSourceBeforeUpdate($sourceID, $sourceUpdate, $requestOptions = [])
    {
        // verify the required parameter 'sourceID' is set
        if (!isset($sourceID)) {
            throw new \InvalidArgumentException(
                'Parameter `sourceID` is required when calling `validateSourceBeforeUpdate`.'
            );
        }
        // verify the required parameter 'sourceUpdate' is set
        if (!isset($sourceUpdate)) {
            throw new \InvalidArgumentException(
                'Parameter `sourceUpdate` is required when calling `validateSourceBeforeUpdate`.'
            );
        }

        $resourcePath = '/1/sources/{sourceID}/validate';
        $queryParameters = [];
        $headers = [];
        $httpBody = $sourceUpdate;

        // path params
        if (null !== $sourceID) {
            $resourcePath = str_replace(
                '{sourceID}',
                ObjectSerializer::toPathValue($sourceID),
                $resourcePath
            );
        }

        return $this->sendRequest('POST', $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions);
    }

    private function sendRequest($method, $resourcePath, $headers, $queryParameters, $httpBody, $requestOptions, $useReadTransporter = false)
    {
        if (!isset($requestOptions['headers'])) {
            $requestOptions['headers'] = [];
        }
        if (!isset($requestOptions['queryParameters'])) {
            $requestOptions['queryParameters'] = [];
        }

        $requestOptions['headers'] = array_merge($headers, $requestOptions['headers']);
        $requestOptions['queryParameters'] = array_merge($queryParameters, $requestOptions['queryParameters']);
        $query = Query::build($requestOptions['queryParameters']);

        return $this->api->sendRequest(
            $method,
            $resourcePath.($query ? "?{$query}" : ''),
            $httpBody,
            $requestOptions,
            $useReadTransporter
        );
    }
}
