<?php

// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.

namespace Algolia\AlgoliaSearch\Model\Abtesting;

use Algolia\AlgoliaSearch\Model\AbstractModel;

/**
 * MinimumDetectableEffect Class Doc Comment.
 *
 * @category Class
 *
 * @description Configuration for the smallest difference between test variants you want to detect.
 */
class MinimumDetectableEffect extends AbstractModel implements ModelInterface, \ArrayAccess, \JsonSerializable
{
    /**
     * Array of property to type mappings. Used for (de)serialization.
     *
     * @var string[]
     */
    protected static $modelTypes = [
        'size' => 'float',
        'effect' => '\Algolia\AlgoliaSearch\Model\Abtesting\Effect',
    ];

    /**
     * Array of property to format mappings. Used for (de)serialization.
     *
     * @var string[]
     */
    protected static $modelFormats = [
        'size' => 'double',
        'effect' => null,
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name.
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'size' => 'size',
        'effect' => 'effect',
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses).
     *
     * @var string[]
     */
    protected static $setters = [
        'size' => 'setSize',
        'effect' => 'setEffect',
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests).
     *
     * @var string[]
     */
    protected static $getters = [
        'size' => 'getSize',
        'effect' => 'getEffect',
    ];

    /**
     * Associative array for storing property values.
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor.
     *
     * @param mixed[] $data Associated array of property values
     */
    public function __construct(?array $data = null)
    {
        if (isset($data['size'])) {
            $this->container['size'] = $data['size'];
        }
        if (isset($data['effect'])) {
            $this->container['effect'] = $data['effect'];
        }
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name.
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of property to type mappings. Used for (de)serialization.
     *
     * @return array
     */
    public static function modelTypes()
    {
        return self::$modelTypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization.
     *
     * @return array
     */
    public static function modelFormats()
    {
        return self::$modelFormats;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses).
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests).
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        if (isset($this->container['size']) && ($this->container['size'] > 1)) {
            $invalidProperties[] = "invalid value for 'size', must be smaller than or equal to 1.";
        }

        if (isset($this->container['size']) && ($this->container['size'] < 0)) {
            $invalidProperties[] = "invalid value for 'size', must be bigger than or equal to 0.";
        }

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed.
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return 0 === count($this->listInvalidProperties());
    }

    /**
     * Gets size.
     *
     * @return null|float
     */
    public function getSize()
    {
        return $this->container['size'] ?? null;
    }

    /**
     * Sets size.
     *
     * @param null|float $size Smallest difference in an observable metric between variants. For example, to detect a 10% difference between variants, set this value to 0.1.
     *
     * @return self
     */
    public function setSize($size)
    {
        if (!is_null($size) && ($size > 1)) {
            throw new \InvalidArgumentException('invalid value for $size when calling MinimumDetectableEffect., must be smaller than or equal to 1.');
        }
        if (!is_null($size) && ($size < 0)) {
            throw new \InvalidArgumentException('invalid value for $size when calling MinimumDetectableEffect., must be bigger than or equal to 0.');
        }

        $this->container['size'] = $size;

        return $this;
    }

    /**
     * Gets effect.
     *
     * @return null|Effect
     */
    public function getEffect()
    {
        return $this->container['effect'] ?? null;
    }

    /**
     * Sets effect.
     *
     * @param null|Effect $effect effect
     *
     * @return self
     */
    public function setEffect($effect)
    {
        $this->container['effect'] = $effect;

        return $this;
    }

    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param int $offset Offset
     *
     * @return bool
     */
    public function offsetExists($offset)
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param int $offset Offset
     *
     * @return null|mixed
     */
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param null|int $offset Offset
     * @param mixed    $value  Value to be set
     */
    public function offsetSet($offset, $value)
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param int $offset Offset
     */
    public function offsetUnset($offset)
    {
        unset($this->container[$offset]);
    }
}
