<?php

// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.

namespace Algolia\AlgoliaSearch\Model\Abtesting;

use Algolia\AlgoliaSearch\Model\AbstractModel;

/**
 * ABTestConfiguration Class Doc Comment.
 *
 * @category Class
 *
 * @description A/B test configuration.
 */
class ABTestConfiguration extends AbstractModel implements ModelInterface, \ArrayAccess, \JsonSerializable
{
    /**
     * Array of property to type mappings. Used for (de)serialization.
     *
     * @var string[]
     */
    protected static $modelTypes = [
        'outliers' => '\Algolia\AlgoliaSearch\Model\Abtesting\Outliers',
        'emptySearch' => '\Algolia\AlgoliaSearch\Model\Abtesting\EmptySearch',
        'minimumDetectableEffect' => '\Algolia\AlgoliaSearch\Model\Abtesting\MinimumDetectableEffect',
    ];

    /**
     * Array of property to format mappings. Used for (de)serialization.
     *
     * @var string[]
     */
    protected static $modelFormats = [
        'outliers' => null,
        'emptySearch' => null,
        'minimumDetectableEffect' => null,
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name.
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'outliers' => 'outliers',
        'emptySearch' => 'emptySearch',
        'minimumDetectableEffect' => 'minimumDetectableEffect',
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses).
     *
     * @var string[]
     */
    protected static $setters = [
        'outliers' => 'setOutliers',
        'emptySearch' => 'setEmptySearch',
        'minimumDetectableEffect' => 'setMinimumDetectableEffect',
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests).
     *
     * @var string[]
     */
    protected static $getters = [
        'outliers' => 'getOutliers',
        'emptySearch' => 'getEmptySearch',
        'minimumDetectableEffect' => 'getMinimumDetectableEffect',
    ];

    /**
     * Associative array for storing property values.
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor.
     *
     * @param mixed[] $data Associated array of property values
     */
    public function __construct(?array $data = null)
    {
        if (isset($data['outliers'])) {
            $this->container['outliers'] = $data['outliers'];
        }
        if (isset($data['emptySearch'])) {
            $this->container['emptySearch'] = $data['emptySearch'];
        }
        if (isset($data['minimumDetectableEffect'])) {
            $this->container['minimumDetectableEffect'] = $data['minimumDetectableEffect'];
        }
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name.
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of property to type mappings. Used for (de)serialization.
     *
     * @return array
     */
    public static function modelTypes()
    {
        return self::$modelTypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization.
     *
     * @return array
     */
    public static function modelFormats()
    {
        return self::$modelFormats;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses).
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests).
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        if (!isset($this->container['outliers']) || null === $this->container['outliers']) {
            $invalidProperties[] = "'outliers' can't be null";
        }

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed.
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return 0 === count($this->listInvalidProperties());
    }

    /**
     * Gets outliers.
     *
     * @return Outliers
     */
    public function getOutliers()
    {
        return $this->container['outliers'] ?? null;
    }

    /**
     * Sets outliers.
     *
     * @param Outliers $outliers outliers
     *
     * @return self
     */
    public function setOutliers($outliers)
    {
        $this->container['outliers'] = $outliers;

        return $this;
    }

    /**
     * Gets emptySearch.
     *
     * @return null|EmptySearch
     */
    public function getEmptySearch()
    {
        return $this->container['emptySearch'] ?? null;
    }

    /**
     * Sets emptySearch.
     *
     * @param null|EmptySearch $emptySearch emptySearch
     *
     * @return self
     */
    public function setEmptySearch($emptySearch)
    {
        $this->container['emptySearch'] = $emptySearch;

        return $this;
    }

    /**
     * Gets minimumDetectableEffect.
     *
     * @return null|MinimumDetectableEffect
     */
    public function getMinimumDetectableEffect()
    {
        return $this->container['minimumDetectableEffect'] ?? null;
    }

    /**
     * Sets minimumDetectableEffect.
     *
     * @param null|MinimumDetectableEffect $minimumDetectableEffect minimumDetectableEffect
     *
     * @return self
     */
    public function setMinimumDetectableEffect($minimumDetectableEffect)
    {
        $this->container['minimumDetectableEffect'] = $minimumDetectableEffect;

        return $this;
    }

    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param int $offset Offset
     *
     * @return bool
     */
    public function offsetExists($offset)
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param int $offset Offset
     *
     * @return null|mixed
     */
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param null|int $offset Offset
     * @param mixed    $value  Value to be set
     */
    public function offsetSet($offset, $value)
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param int $offset Offset
     */
    public function offsetUnset($offset)
    {
        unset($this->container[$offset]);
    }
}
