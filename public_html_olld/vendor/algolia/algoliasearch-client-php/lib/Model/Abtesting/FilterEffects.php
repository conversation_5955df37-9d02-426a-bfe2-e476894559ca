<?php

// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.

namespace Algolia\AlgoliaSearch\Model\Abtesting;

use Algolia\AlgoliaSearch\Model\AbstractModel;

/**
 * FilterEffects Class Doc Comment.
 *
 * @category Class
 *
 * @description A/B test filter effects resulting from configuration settings.
 */
class FilterEffects extends AbstractModel implements ModelInterface, \ArrayAccess, \JsonSerializable
{
    /**
     * Array of property to type mappings. Used for (de)serialization.
     *
     * @var string[]
     */
    protected static $modelTypes = [
        'outliers' => '\Algolia\AlgoliaSearch\Model\Abtesting\OutliersFilter',
        'emptySearch' => '\Algolia\AlgoliaSearch\Model\Abtesting\EmptySearchFilter',
    ];

    /**
     * Array of property to format mappings. Used for (de)serialization.
     *
     * @var string[]
     */
    protected static $modelFormats = [
        'outliers' => null,
        'emptySearch' => null,
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name.
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'outliers' => 'outliers',
        'emptySearch' => 'emptySearch',
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses).
     *
     * @var string[]
     */
    protected static $setters = [
        'outliers' => 'setOutliers',
        'emptySearch' => 'setEmptySearch',
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests).
     *
     * @var string[]
     */
    protected static $getters = [
        'outliers' => 'getOutliers',
        'emptySearch' => 'getEmptySearch',
    ];

    /**
     * Associative array for storing property values.
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor.
     *
     * @param mixed[] $data Associated array of property values
     */
    public function __construct(?array $data = null)
    {
        if (isset($data['outliers'])) {
            $this->container['outliers'] = $data['outliers'];
        }
        if (isset($data['emptySearch'])) {
            $this->container['emptySearch'] = $data['emptySearch'];
        }
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name.
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of property to type mappings. Used for (de)serialization.
     *
     * @return array
     */
    public static function modelTypes()
    {
        return self::$modelTypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization.
     *
     * @return array
     */
    public static function modelFormats()
    {
        return self::$modelFormats;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses).
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests).
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        return [];
    }

    /**
     * Validate all the properties in the model
     * return true if all passed.
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return 0 === count($this->listInvalidProperties());
    }

    /**
     * Gets outliers.
     *
     * @return null|OutliersFilter
     */
    public function getOutliers()
    {
        return $this->container['outliers'] ?? null;
    }

    /**
     * Sets outliers.
     *
     * @param null|OutliersFilter $outliers outliers
     *
     * @return self
     */
    public function setOutliers($outliers)
    {
        $this->container['outliers'] = $outliers;

        return $this;
    }

    /**
     * Gets emptySearch.
     *
     * @return null|EmptySearchFilter
     */
    public function getEmptySearch()
    {
        return $this->container['emptySearch'] ?? null;
    }

    /**
     * Sets emptySearch.
     *
     * @param null|EmptySearchFilter $emptySearch emptySearch
     *
     * @return self
     */
    public function setEmptySearch($emptySearch)
    {
        $this->container['emptySearch'] = $emptySearch;

        return $this;
    }

    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param int $offset Offset
     *
     * @return bool
     */
    public function offsetExists($offset)
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param int $offset Offset
     *
     * @return null|mixed
     */
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param null|int $offset Offset
     * @param mixed    $value  Value to be set
     */
    public function offsetSet($offset, $value)
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param int $offset Offset
     */
    public function offsetUnset($offset)
    {
        unset($this->container[$offset]);
    }
}
