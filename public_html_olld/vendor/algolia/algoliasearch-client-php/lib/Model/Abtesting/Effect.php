<?php

// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.

namespace Algolia\AlgoliaSearch\Model\Abtesting;

/**
 * Effect Class Doc Comment.
 *
 * @category Class
 *
 * @description Metric for which you want to detect the smallest relative difference.
 */
class Effect
{
    /**
     * Possible values of this enum.
     */
    public const ADD_TO_CART_RATE = 'addToCartRate';

    public const CLICK_THROUGH_RATE = 'clickThroughRate';

    public const CONVERSION_RATE = 'conversionRate';

    public const PURCHASE_RATE = 'purchaseRate';

    /**
     * Gets allowable values of the enum.
     *
     * @return string[]
     */
    public static function getAllowableEnumValues()
    {
        return [
            self::ADD_TO_CART_RATE,
            self::CLICK_THROUGH_RATE,
            self::CONVERSION_RATE,
            self::PURCHASE_RATE,
        ];
    }
}
