<?php

// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.

namespace Algolia\AlgoliaSearch\Model\Insights;

/**
 * ViewEvent Class Doc Comment.
 *
 * @category Class
 */
class ViewEvent
{
    /**
     * Possible values of this enum.
     */
    public const VIEW = 'view';

    /**
     * Gets allowable values of the enum.
     *
     * @return string[]
     */
    public static function getAllowableEnumValues()
    {
        return [
            self::VIEW,
        ];
    }
}
