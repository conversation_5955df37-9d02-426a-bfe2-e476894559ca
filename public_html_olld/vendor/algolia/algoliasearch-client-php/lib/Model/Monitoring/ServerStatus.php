<?php

// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.

namespace Algolia\AlgoliaSearch\Model\Monitoring;

/**
 * ServerStatus Class Doc Comment.
 *
 * @category Class
 */
class ServerStatus
{
    /**
     * Possible values of this enum.
     */
    public const PRODUCTION = 'PRODUCTION';

    /**
     * Gets allowable values of the enum.
     *
     * @return string[]
     */
    public static function getAllowableEnumValues()
    {
        return [
            self::PRODUCTION,
        ];
    }
}
