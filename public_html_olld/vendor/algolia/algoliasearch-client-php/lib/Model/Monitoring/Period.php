<?php

// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.

namespace Algolia\AlgoliaSearch\Model\Monitoring;

/**
 * Period Class Doc Comment.
 *
 * @category Class
 */
class Period
{
    /**
     * Possible values of this enum.
     */
    public const MINUTE = 'minute';

    public const HOUR = 'hour';

    public const DAY = 'day';

    public const WEEK = 'week';

    public const MONTH = 'month';

    /**
     * Gets allowable values of the enum.
     *
     * @return string[]
     */
    public static function getAllowableEnumValues()
    {
        return [
            self::MINUTE,
            self::HOUR,
            self::DAY,
            self::WEEK,
            self::MONTH,
        ];
    }
}
