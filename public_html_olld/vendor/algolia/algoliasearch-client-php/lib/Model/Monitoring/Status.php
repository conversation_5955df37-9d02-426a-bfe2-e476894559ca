<?php

// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.

namespace Algolia\AlgoliaSearch\Model\Monitoring;

/**
 * Status Class Doc Comment.
 *
 * @category Class
 *
 * @description Status of the cluster.
 */
class Status
{
    /**
     * Possible values of this enum.
     */
    public const OPERATIONAL = 'operational';

    public const DEGRADED_PERFORMANCE = 'degraded_performance';

    public const PARTIAL_OUTAGE = 'partial_outage';

    public const MAJOR_OUTAGE = 'major_outage';

    /**
     * Gets allowable values of the enum.
     *
     * @return string[]
     */
    public static function getAllowableEnumValues()
    {
        return [
            self::OPERATIONAL,
            self::DEGRADED_PERFORMANCE,
            self::PARTIAL_OUTAGE,
            self::MAJOR_OUTAGE,
        ];
    }
}
