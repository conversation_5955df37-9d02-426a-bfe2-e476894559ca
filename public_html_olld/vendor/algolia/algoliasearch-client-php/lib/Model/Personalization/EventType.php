<?php

// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.

namespace Algolia\AlgoliaSearch\Model\Personalization;

/**
 * EventType Class Doc Comment.
 *
 * @category Class
 *
 * @description Event type.
 */
class EventType
{
    /**
     * Possible values of this enum.
     */
    public const CLICK = 'click';

    public const CONVERSION = 'conversion';

    public const VIEW = 'view';

    /**
     * Gets allowable values of the enum.
     *
     * @return string[]
     */
    public static function getAllowableEnumValues()
    {
        return [
            self::CLICK,
            self::CONVERSION,
            self::VIEW,
        ];
    }
}
