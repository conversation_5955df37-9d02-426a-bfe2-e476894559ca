<?php

// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.

namespace Algolia\AlgoliaSearch\Model\Search;

/**
 * LogType Class Doc Comment.
 *
 * @category Class
 */
class LogType
{
    /**
     * Possible values of this enum.
     */
    public const ALL = 'all';

    public const QUERY = 'query';

    public const BUILD = 'build';

    public const ERROR = 'error';

    /**
     * Gets allowable values of the enum.
     *
     * @return string[]
     */
    public static function getAllowableEnumValues()
    {
        return [
            self::ALL,
            self::QUERY,
            self::BUILD,
            self::ERROR,
        ];
    }
}
