<?php

// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.

namespace Algolia\AlgoliaSearch\Model\Search;

/**
 * RemoveWordsIfNoResults Class Doc Comment.
 *
 * @category Class
 *
 * @description Strategy for removing words from the query when it doesn&#39;t return any results. This helps to avoid returning empty search results.  - &#x60;none&#x60;.   No words are removed when a query doesn&#39;t return results.  - &#x60;lastWords&#x60;.   Treat the last (then second to last, then third to last) word as optional,   until there are results or at most 5 words have been removed.  - &#x60;firstWords&#x60;.   Treat the first (then second, then third) word as optional,   until there are results or at most 5 words have been removed.  - &#x60;allOptional&#x60;.   Treat all words as optional.  For more information, see [Remove words to improve results](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/empty-or-insufficient-results/in-depth/why-use-remove-words-if-no-results/).
 */
class RemoveWordsIfNoResults
{
    /**
     * Possible values of this enum.
     */
    public const NONE = 'none';

    public const LAST_WORDS = 'lastWords';

    public const FIRST_WORDS = 'firstWords';

    public const ALL_OPTIONAL = 'allOptional';

    /**
     * Gets allowable values of the enum.
     *
     * @return string[]
     */
    public static function getAllowableEnumValues()
    {
        return [
            self::NONE,
            self::LAST_WORDS,
            self::FIRST_WORDS,
            self::ALL_OPTIONAL,
        ];
    }
}
