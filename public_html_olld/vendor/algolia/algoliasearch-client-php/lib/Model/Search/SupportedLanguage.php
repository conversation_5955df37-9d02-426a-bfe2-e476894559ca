<?php

// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.

namespace Algolia\AlgoliaSearch\Model\Search;

/**
 * SupportedLanguage Class Doc Comment.
 *
 * @category Class
 *
 * @description ISO code for a supported language.
 */
class SupportedLanguage
{
    /**
     * Possible values of this enum.
     */
    public const AF = 'af';

    public const AR = 'ar';

    public const AZ = 'az';

    public const BG = 'bg';

    public const BN = 'bn';

    public const CA = 'ca';

    public const CS = 'cs';

    public const CY = 'cy';

    public const DA = 'da';

    public const DE = 'de';

    public const EL = 'el';

    public const EN = 'en';

    public const EO = 'eo';

    public const ES = 'es';

    public const ET = 'et';

    public const EU = 'eu';

    public const FA = 'fa';

    public const FI = 'fi';

    public const FO = 'fo';

    public const FR = 'fr';

    public const GA = 'ga';

    public const GL = 'gl';

    public const HE = 'he';

    public const HI = 'hi';

    public const HU = 'hu';

    public const HY = 'hy';

    public const ID = 'id';

    public const IS = 'is';

    public const IT = 'it';

    public const JA = 'ja';

    public const KA = 'ka';

    public const KK = 'kk';

    public const KO = 'ko';

    public const KU = 'ku';

    public const KY = 'ky';

    public const LT = 'lt';

    public const LV = 'lv';

    public const MI = 'mi';

    public const MN = 'mn';

    public const MR = 'mr';

    public const MS = 'ms';

    public const MT = 'mt';

    public const NB = 'nb';

    public const NL = 'nl';

    public const NO = 'no';

    public const NS = 'ns';

    public const PL = 'pl';

    public const PS = 'ps';

    public const PT = 'pt';

    public const PT_BR = 'pt-br';

    public const QU = 'qu';

    public const RO = 'ro';

    public const RU = 'ru';

    public const SK = 'sk';

    public const SQ = 'sq';

    public const SV = 'sv';

    public const SW = 'sw';

    public const TA = 'ta';

    public const TE = 'te';

    public const TH = 'th';

    public const TL = 'tl';

    public const TN = 'tn';

    public const TR = 'tr';

    public const TT = 'tt';

    public const UK = 'uk';

    public const UR = 'ur';

    public const UZ = 'uz';

    public const ZH = 'zh';

    /**
     * Gets allowable values of the enum.
     *
     * @return string[]
     */
    public static function getAllowableEnumValues()
    {
        return [
            self::AF,
            self::AR,
            self::AZ,
            self::BG,
            self::BN,
            self::CA,
            self::CS,
            self::CY,
            self::DA,
            self::DE,
            self::EL,
            self::EN,
            self::EO,
            self::ES,
            self::ET,
            self::EU,
            self::FA,
            self::FI,
            self::FO,
            self::FR,
            self::GA,
            self::GL,
            self::HE,
            self::HI,
            self::HU,
            self::HY,
            self::ID,
            self::IS,
            self::IT,
            self::JA,
            self::KA,
            self::KK,
            self::KO,
            self::KU,
            self::KY,
            self::LT,
            self::LV,
            self::MI,
            self::MN,
            self::MR,
            self::MS,
            self::MT,
            self::NB,
            self::NL,
            self::NO,
            self::NS,
            self::PL,
            self::PS,
            self::PT,
            self::PT_BR,
            self::QU,
            self::RO,
            self::RU,
            self::SK,
            self::SQ,
            self::SV,
            self::SW,
            self::TA,
            self::TE,
            self::TH,
            self::TL,
            self::TN,
            self::TR,
            self::TT,
            self::UK,
            self::UR,
            self::UZ,
            self::ZH,
        ];
    }
}
