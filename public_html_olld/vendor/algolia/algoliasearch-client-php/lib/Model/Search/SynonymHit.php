<?php

// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.

namespace Algolia\AlgoliaSearch\Model\Search;

use Algolia\AlgoliaSearch\Model\AbstractModel;

/**
 * SynonymHit Class Doc Comment.
 *
 * @category Class
 *
 * @description Synonym object.
 */
class SynonymHit extends AbstractModel implements ModelInterface, \ArrayAccess, \JsonSerializable
{
    /**
     * Array of property to type mappings. Used for (de)serialization.
     *
     * @var string[]
     */
    protected static $modelTypes = [
        'objectID' => 'string',
        'type' => '\Algolia\AlgoliaSearch\Model\Search\SynonymType',
        'synonyms' => 'string[]',
        'input' => 'string',
        'word' => 'string',
        'corrections' => 'string[]',
        'placeholder' => 'string',
        'replacements' => 'string[]',
    ];

    /**
     * Array of property to format mappings. Used for (de)serialization.
     *
     * @var string[]
     */
    protected static $modelFormats = [
        'objectID' => null,
        'type' => null,
        'synonyms' => null,
        'input' => null,
        'word' => null,
        'corrections' => null,
        'placeholder' => null,
        'replacements' => null,
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name.
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'objectID' => 'objectID',
        'type' => 'type',
        'synonyms' => 'synonyms',
        'input' => 'input',
        'word' => 'word',
        'corrections' => 'corrections',
        'placeholder' => 'placeholder',
        'replacements' => 'replacements',
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses).
     *
     * @var string[]
     */
    protected static $setters = [
        'objectID' => 'setObjectID',
        'type' => 'setType',
        'synonyms' => 'setSynonyms',
        'input' => 'setInput',
        'word' => 'setWord',
        'corrections' => 'setCorrections',
        'placeholder' => 'setPlaceholder',
        'replacements' => 'setReplacements',
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests).
     *
     * @var string[]
     */
    protected static $getters = [
        'objectID' => 'getObjectID',
        'type' => 'getType',
        'synonyms' => 'getSynonyms',
        'input' => 'getInput',
        'word' => 'getWord',
        'corrections' => 'getCorrections',
        'placeholder' => 'getPlaceholder',
        'replacements' => 'getReplacements',
    ];

    /**
     * Associative array for storing property values.
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor.
     *
     * @param mixed[] $data Associated array of property values
     */
    public function __construct(?array $data = null)
    {
        if (isset($data['objectID'])) {
            $this->container['objectID'] = $data['objectID'];
        }
        if (isset($data['type'])) {
            $this->container['type'] = $data['type'];
        }
        if (isset($data['synonyms'])) {
            $this->container['synonyms'] = $data['synonyms'];
        }
        if (isset($data['input'])) {
            $this->container['input'] = $data['input'];
        }
        if (isset($data['word'])) {
            $this->container['word'] = $data['word'];
        }
        if (isset($data['corrections'])) {
            $this->container['corrections'] = $data['corrections'];
        }
        if (isset($data['placeholder'])) {
            $this->container['placeholder'] = $data['placeholder'];
        }
        if (isset($data['replacements'])) {
            $this->container['replacements'] = $data['replacements'];
        }
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name.
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of property to type mappings. Used for (de)serialization.
     *
     * @return array
     */
    public static function modelTypes()
    {
        return self::$modelTypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization.
     *
     * @return array
     */
    public static function modelFormats()
    {
        return self::$modelFormats;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses).
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests).
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        if (!isset($this->container['objectID']) || null === $this->container['objectID']) {
            $invalidProperties[] = "'objectID' can't be null";
        }
        if (!isset($this->container['type']) || null === $this->container['type']) {
            $invalidProperties[] = "'type' can't be null";
        }

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed.
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return 0 === count($this->listInvalidProperties());
    }

    /**
     * Gets objectID.
     *
     * @return string
     */
    public function getObjectID()
    {
        return $this->container['objectID'] ?? null;
    }

    /**
     * Sets objectID.
     *
     * @param string $objectID unique identifier of a synonym object
     *
     * @return self
     */
    public function setObjectID($objectID)
    {
        $this->container['objectID'] = $objectID;

        return $this;
    }

    /**
     * Gets type.
     *
     * @return SynonymType
     */
    public function getType()
    {
        return $this->container['type'] ?? null;
    }

    /**
     * Sets type.
     *
     * @param SynonymType $type type
     *
     * @return self
     */
    public function setType($type)
    {
        $this->container['type'] = $type;

        return $this;
    }

    /**
     * Gets synonyms.
     *
     * @return null|string[]
     */
    public function getSynonyms()
    {
        return $this->container['synonyms'] ?? null;
    }

    /**
     * Sets synonyms.
     *
     * @param null|string[] $synonyms words or phrases considered equivalent
     *
     * @return self
     */
    public function setSynonyms($synonyms)
    {
        $this->container['synonyms'] = $synonyms;

        return $this;
    }

    /**
     * Gets input.
     *
     * @return null|string
     */
    public function getInput()
    {
        return $this->container['input'] ?? null;
    }

    /**
     * Sets input.
     *
     * @param null|string $input Word or phrase to appear in query strings (for [`onewaysynonym`s](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/adding-synonyms/in-depth/one-way-synonyms/)).
     *
     * @return self
     */
    public function setInput($input)
    {
        $this->container['input'] = $input;

        return $this;
    }

    /**
     * Gets word.
     *
     * @return null|string
     */
    public function getWord()
    {
        return $this->container['word'] ?? null;
    }

    /**
     * Sets word.
     *
     * @param null|string $word Word or phrase to appear in query strings (for [`altcorrection1` and `altcorrection2`](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/adding-synonyms/in-depth/synonyms-alternative-corrections/)).
     *
     * @return self
     */
    public function setWord($word)
    {
        $this->container['word'] = $word;

        return $this;
    }

    /**
     * Gets corrections.
     *
     * @return null|string[]
     */
    public function getCorrections()
    {
        return $this->container['corrections'] ?? null;
    }

    /**
     * Sets corrections.
     *
     * @param null|string[] $corrections words to be matched in records
     *
     * @return self
     */
    public function setCorrections($corrections)
    {
        $this->container['corrections'] = $corrections;

        return $this;
    }

    /**
     * Gets placeholder.
     *
     * @return null|string
     */
    public function getPlaceholder()
    {
        return $this->container['placeholder'] ?? null;
    }

    /**
     * Sets placeholder.
     *
     * @param null|string $placeholder [Placeholder token](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/adding-synonyms/in-depth/synonyms-placeholders/) to be put inside records.
     *
     * @return self
     */
    public function setPlaceholder($placeholder)
    {
        $this->container['placeholder'] = $placeholder;

        return $this;
    }

    /**
     * Gets replacements.
     *
     * @return null|string[]
     */
    public function getReplacements()
    {
        return $this->container['replacements'] ?? null;
    }

    /**
     * Sets replacements.
     *
     * @param null|string[] $replacements Query words that will match the [placeholder token](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/adding-synonyms/in-depth/synonyms-placeholders/).
     *
     * @return self
     */
    public function setReplacements($replacements)
    {
        $this->container['replacements'] = $replacements;

        return $this;
    }

    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param int $offset Offset
     *
     * @return bool
     */
    public function offsetExists($offset)
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param int $offset Offset
     *
     * @return null|mixed
     */
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param null|int $offset Offset
     * @param mixed    $value  Value to be set
     */
    public function offsetSet($offset, $value)
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param int $offset Offset
     */
    public function offsetUnset($offset)
    {
        unset($this->container[$offset]);
    }
}
