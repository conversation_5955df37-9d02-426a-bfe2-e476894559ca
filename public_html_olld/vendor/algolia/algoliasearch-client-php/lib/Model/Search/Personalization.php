<?php

// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.

namespace Algolia\AlgoliaSearch\Model\Search;

use Algolia\AlgoliaSearch\Model\AbstractModel;

/**
 * Personalization Class Doc Comment.
 *
 * @category Class
 */
class Personalization extends AbstractModel implements ModelInterface, \ArrayAccess, \JsonSerializable
{
    /**
     * Array of property to type mappings. Used for (de)serialization.
     *
     * @var string[]
     */
    protected static $modelTypes = [
        'filtersScore' => 'int',
        'rankingScore' => 'int',
        'score' => 'int',
    ];

    /**
     * Array of property to format mappings. Used for (de)serialization.
     *
     * @var string[]
     */
    protected static $modelFormats = [
        'filtersScore' => null,
        'rankingScore' => null,
        'score' => null,
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name.
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'filtersScore' => 'filtersScore',
        'rankingScore' => 'rankingScore',
        'score' => 'score',
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses).
     *
     * @var string[]
     */
    protected static $setters = [
        'filtersScore' => 'setFiltersScore',
        'rankingScore' => 'setRankingScore',
        'score' => 'setScore',
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests).
     *
     * @var string[]
     */
    protected static $getters = [
        'filtersScore' => 'getFiltersScore',
        'rankingScore' => 'getRankingScore',
        'score' => 'getScore',
    ];

    /**
     * Associative array for storing property values.
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor.
     *
     * @param mixed[] $data Associated array of property values
     */
    public function __construct(?array $data = null)
    {
        if (isset($data['filtersScore'])) {
            $this->container['filtersScore'] = $data['filtersScore'];
        }
        if (isset($data['rankingScore'])) {
            $this->container['rankingScore'] = $data['rankingScore'];
        }
        if (isset($data['score'])) {
            $this->container['score'] = $data['score'];
        }
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name.
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of property to type mappings. Used for (de)serialization.
     *
     * @return array
     */
    public static function modelTypes()
    {
        return self::$modelTypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization.
     *
     * @return array
     */
    public static function modelFormats()
    {
        return self::$modelFormats;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses).
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests).
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        return [];
    }

    /**
     * Validate all the properties in the model
     * return true if all passed.
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return 0 === count($this->listInvalidProperties());
    }

    /**
     * Gets filtersScore.
     *
     * @return null|int
     */
    public function getFiltersScore()
    {
        return $this->container['filtersScore'] ?? null;
    }

    /**
     * Sets filtersScore.
     *
     * @param null|int $filtersScore the score of the filters
     *
     * @return self
     */
    public function setFiltersScore($filtersScore)
    {
        $this->container['filtersScore'] = $filtersScore;

        return $this;
    }

    /**
     * Gets rankingScore.
     *
     * @return null|int
     */
    public function getRankingScore()
    {
        return $this->container['rankingScore'] ?? null;
    }

    /**
     * Sets rankingScore.
     *
     * @param null|int $rankingScore the score of the ranking
     *
     * @return self
     */
    public function setRankingScore($rankingScore)
    {
        $this->container['rankingScore'] = $rankingScore;

        return $this;
    }

    /**
     * Gets score.
     *
     * @return null|int
     */
    public function getScore()
    {
        return $this->container['score'] ?? null;
    }

    /**
     * Sets score.
     *
     * @param null|int $score the score of the event
     *
     * @return self
     */
    public function setScore($score)
    {
        $this->container['score'] = $score;

        return $this;
    }

    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param int $offset Offset
     *
     * @return bool
     */
    public function offsetExists($offset)
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param int $offset Offset
     *
     * @return null|mixed
     */
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param null|int $offset Offset
     * @param mixed    $value  Value to be set
     */
    public function offsetSet($offset, $value)
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param int $offset Offset
     */
    public function offsetUnset($offset)
    {
        unset($this->container[$offset]);
    }
}
