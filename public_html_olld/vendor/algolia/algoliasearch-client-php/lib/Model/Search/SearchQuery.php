<?php

// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.

namespace Algolia\AlgoliaSearch\Model\Search;

use Algolia\AlgoliaSearch\Model\AbstractModel;

/**
 * SearchQuery Class Doc Comment.
 *
 * @category Class
 */
class SearchQuery extends AbstractModel implements ModelInterface, \ArrayAccess, \JsonSerializable
{
    /**
     * Array of property to type mappings. Used for (de)serialization.
     *
     * @var string[]
     */
    protected static $modelTypes = [
        'params' => 'string',
        'query' => 'string',
        'similarQuery' => 'string',
        'filters' => 'string',
        'facetFilters' => '\Algolia\AlgoliaSearch\Model\Search\FacetFilters',
        'optionalFilters' => '\Algolia\AlgoliaSearch\Model\Search\OptionalFilters',
        'numericFilters' => '\Algolia\AlgoliaSearch\Model\Search\NumericFilters',
        'tagFilters' => '\Algolia\AlgoliaSearch\Model\Search\TagFilters',
        'sumOrFiltersScores' => 'bool',
        'restrictSearchableAttributes' => 'string[]',
        'facets' => 'string[]',
        'facetingAfterDistinct' => 'bool',
        'page' => 'int',
        'offset' => 'int',
        'length' => 'int',
        'aroundLatLng' => 'string',
        'aroundLatLngViaIP' => 'bool',
        'aroundRadius' => '\Algolia\AlgoliaSearch\Model\Search\AroundRadius',
        'aroundPrecision' => '\Algolia\AlgoliaSearch\Model\Search\AroundPrecision',
        'minimumAroundRadius' => 'int',
        'insideBoundingBox' => 'float[][]',
        'insidePolygon' => 'float[][]',
        'naturalLanguages' => '\Algolia\AlgoliaSearch\Model\Search\SupportedLanguage[]',
        'ruleContexts' => 'string[]',
        'personalizationImpact' => 'int',
        'userToken' => 'string',
        'getRankingInfo' => 'bool',
        'synonyms' => 'bool',
        'clickAnalytics' => 'bool',
        'analytics' => 'bool',
        'analyticsTags' => 'string[]',
        'percentileComputation' => 'bool',
        'enableABTest' => 'bool',
        'attributesToRetrieve' => 'string[]',
        'ranking' => 'string[]',
        'customRanking' => 'string[]',
        'relevancyStrictness' => 'int',
        'attributesToHighlight' => 'string[]',
        'attributesToSnippet' => 'string[]',
        'highlightPreTag' => 'string',
        'highlightPostTag' => 'string',
        'snippetEllipsisText' => 'string',
        'restrictHighlightAndSnippetArrays' => 'bool',
        'hitsPerPage' => 'int',
        'minWordSizefor1Typo' => 'int',
        'minWordSizefor2Typos' => 'int',
        'typoTolerance' => '\Algolia\AlgoliaSearch\Model\Search\TypoTolerance',
        'allowTyposOnNumericTokens' => 'bool',
        'disableTypoToleranceOnAttributes' => 'string[]',
        'ignorePlurals' => '\Algolia\AlgoliaSearch\Model\Search\IgnorePlurals',
        'removeStopWords' => '\Algolia\AlgoliaSearch\Model\Search\RemoveStopWords',
        'keepDiacriticsOnCharacters' => 'string',
        'queryLanguages' => '\Algolia\AlgoliaSearch\Model\Search\SupportedLanguage[]',
        'decompoundQuery' => 'bool',
        'enableRules' => 'bool',
        'enablePersonalization' => 'bool',
        'queryType' => '\Algolia\AlgoliaSearch\Model\Search\QueryType',
        'removeWordsIfNoResults' => '\Algolia\AlgoliaSearch\Model\Search\RemoveWordsIfNoResults',
        'mode' => '\Algolia\AlgoliaSearch\Model\Search\Mode',
        'semanticSearch' => '\Algolia\AlgoliaSearch\Model\Search\SemanticSearch',
        'advancedSyntax' => 'bool',
        'optionalWords' => 'string[]',
        'disableExactOnAttributes' => 'string[]',
        'exactOnSingleWordQuery' => '\Algolia\AlgoliaSearch\Model\Search\ExactOnSingleWordQuery',
        'alternativesAsExact' => '\Algolia\AlgoliaSearch\Model\Search\AlternativesAsExact[]',
        'advancedSyntaxFeatures' => '\Algolia\AlgoliaSearch\Model\Search\AdvancedSyntaxFeatures[]',
        'distinct' => '\Algolia\AlgoliaSearch\Model\Search\Distinct',
        'replaceSynonymsInHighlight' => 'bool',
        'minProximity' => 'int',
        'responseFields' => 'string[]',
        'maxFacetHits' => 'int',
        'maxValuesPerFacet' => 'int',
        'sortFacetValuesBy' => 'string',
        'attributeCriteriaComputedByMinProximity' => 'bool',
        'renderingContent' => '\Algolia\AlgoliaSearch\Model\Search\RenderingContent',
        'enableReRanking' => 'bool',
        'reRankingApplyFilter' => '\Algolia\AlgoliaSearch\Model\Search\ReRankingApplyFilter',
        'indexName' => 'string',
        'type' => '\Algolia\AlgoliaSearch\Model\Search\SearchTypeFacet',
        'facet' => 'string',
        'facetQuery' => 'string',
    ];

    /**
     * Array of property to format mappings. Used for (de)serialization.
     *
     * @var string[]
     */
    protected static $modelFormats = [
        'params' => null,
        'query' => null,
        'similarQuery' => null,
        'filters' => null,
        'facetFilters' => null,
        'optionalFilters' => null,
        'numericFilters' => null,
        'tagFilters' => null,
        'sumOrFiltersScores' => null,
        'restrictSearchableAttributes' => null,
        'facets' => null,
        'facetingAfterDistinct' => null,
        'page' => null,
        'offset' => null,
        'length' => null,
        'aroundLatLng' => null,
        'aroundLatLngViaIP' => null,
        'aroundRadius' => null,
        'aroundPrecision' => null,
        'minimumAroundRadius' => null,
        'insideBoundingBox' => 'double',
        'insidePolygon' => 'double',
        'naturalLanguages' => null,
        'ruleContexts' => null,
        'personalizationImpact' => null,
        'userToken' => null,
        'getRankingInfo' => null,
        'synonyms' => null,
        'clickAnalytics' => null,
        'analytics' => null,
        'analyticsTags' => null,
        'percentileComputation' => null,
        'enableABTest' => null,
        'attributesToRetrieve' => null,
        'ranking' => null,
        'customRanking' => null,
        'relevancyStrictness' => null,
        'attributesToHighlight' => null,
        'attributesToSnippet' => null,
        'highlightPreTag' => null,
        'highlightPostTag' => null,
        'snippetEllipsisText' => null,
        'restrictHighlightAndSnippetArrays' => null,
        'hitsPerPage' => null,
        'minWordSizefor1Typo' => null,
        'minWordSizefor2Typos' => null,
        'typoTolerance' => null,
        'allowTyposOnNumericTokens' => null,
        'disableTypoToleranceOnAttributes' => null,
        'ignorePlurals' => null,
        'removeStopWords' => null,
        'keepDiacriticsOnCharacters' => null,
        'queryLanguages' => null,
        'decompoundQuery' => null,
        'enableRules' => null,
        'enablePersonalization' => null,
        'queryType' => null,
        'removeWordsIfNoResults' => null,
        'mode' => null,
        'semanticSearch' => null,
        'advancedSyntax' => null,
        'optionalWords' => null,
        'disableExactOnAttributes' => null,
        'exactOnSingleWordQuery' => null,
        'alternativesAsExact' => null,
        'advancedSyntaxFeatures' => null,
        'distinct' => null,
        'replaceSynonymsInHighlight' => null,
        'minProximity' => null,
        'responseFields' => null,
        'maxFacetHits' => null,
        'maxValuesPerFacet' => null,
        'sortFacetValuesBy' => null,
        'attributeCriteriaComputedByMinProximity' => null,
        'renderingContent' => null,
        'enableReRanking' => null,
        'reRankingApplyFilter' => null,
        'indexName' => null,
        'type' => null,
        'facet' => null,
        'facetQuery' => null,
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name.
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'params' => 'params',
        'query' => 'query',
        'similarQuery' => 'similarQuery',
        'filters' => 'filters',
        'facetFilters' => 'facetFilters',
        'optionalFilters' => 'optionalFilters',
        'numericFilters' => 'numericFilters',
        'tagFilters' => 'tagFilters',
        'sumOrFiltersScores' => 'sumOrFiltersScores',
        'restrictSearchableAttributes' => 'restrictSearchableAttributes',
        'facets' => 'facets',
        'facetingAfterDistinct' => 'facetingAfterDistinct',
        'page' => 'page',
        'offset' => 'offset',
        'length' => 'length',
        'aroundLatLng' => 'aroundLatLng',
        'aroundLatLngViaIP' => 'aroundLatLngViaIP',
        'aroundRadius' => 'aroundRadius',
        'aroundPrecision' => 'aroundPrecision',
        'minimumAroundRadius' => 'minimumAroundRadius',
        'insideBoundingBox' => 'insideBoundingBox',
        'insidePolygon' => 'insidePolygon',
        'naturalLanguages' => 'naturalLanguages',
        'ruleContexts' => 'ruleContexts',
        'personalizationImpact' => 'personalizationImpact',
        'userToken' => 'userToken',
        'getRankingInfo' => 'getRankingInfo',
        'synonyms' => 'synonyms',
        'clickAnalytics' => 'clickAnalytics',
        'analytics' => 'analytics',
        'analyticsTags' => 'analyticsTags',
        'percentileComputation' => 'percentileComputation',
        'enableABTest' => 'enableABTest',
        'attributesToRetrieve' => 'attributesToRetrieve',
        'ranking' => 'ranking',
        'customRanking' => 'customRanking',
        'relevancyStrictness' => 'relevancyStrictness',
        'attributesToHighlight' => 'attributesToHighlight',
        'attributesToSnippet' => 'attributesToSnippet',
        'highlightPreTag' => 'highlightPreTag',
        'highlightPostTag' => 'highlightPostTag',
        'snippetEllipsisText' => 'snippetEllipsisText',
        'restrictHighlightAndSnippetArrays' => 'restrictHighlightAndSnippetArrays',
        'hitsPerPage' => 'hitsPerPage',
        'minWordSizefor1Typo' => 'minWordSizefor1Typo',
        'minWordSizefor2Typos' => 'minWordSizefor2Typos',
        'typoTolerance' => 'typoTolerance',
        'allowTyposOnNumericTokens' => 'allowTyposOnNumericTokens',
        'disableTypoToleranceOnAttributes' => 'disableTypoToleranceOnAttributes',
        'ignorePlurals' => 'ignorePlurals',
        'removeStopWords' => 'removeStopWords',
        'keepDiacriticsOnCharacters' => 'keepDiacriticsOnCharacters',
        'queryLanguages' => 'queryLanguages',
        'decompoundQuery' => 'decompoundQuery',
        'enableRules' => 'enableRules',
        'enablePersonalization' => 'enablePersonalization',
        'queryType' => 'queryType',
        'removeWordsIfNoResults' => 'removeWordsIfNoResults',
        'mode' => 'mode',
        'semanticSearch' => 'semanticSearch',
        'advancedSyntax' => 'advancedSyntax',
        'optionalWords' => 'optionalWords',
        'disableExactOnAttributes' => 'disableExactOnAttributes',
        'exactOnSingleWordQuery' => 'exactOnSingleWordQuery',
        'alternativesAsExact' => 'alternativesAsExact',
        'advancedSyntaxFeatures' => 'advancedSyntaxFeatures',
        'distinct' => 'distinct',
        'replaceSynonymsInHighlight' => 'replaceSynonymsInHighlight',
        'minProximity' => 'minProximity',
        'responseFields' => 'responseFields',
        'maxFacetHits' => 'maxFacetHits',
        'maxValuesPerFacet' => 'maxValuesPerFacet',
        'sortFacetValuesBy' => 'sortFacetValuesBy',
        'attributeCriteriaComputedByMinProximity' => 'attributeCriteriaComputedByMinProximity',
        'renderingContent' => 'renderingContent',
        'enableReRanking' => 'enableReRanking',
        'reRankingApplyFilter' => 'reRankingApplyFilter',
        'indexName' => 'indexName',
        'type' => 'type',
        'facet' => 'facet',
        'facetQuery' => 'facetQuery',
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses).
     *
     * @var string[]
     */
    protected static $setters = [
        'params' => 'setParams',
        'query' => 'setQuery',
        'similarQuery' => 'setSimilarQuery',
        'filters' => 'setFilters',
        'facetFilters' => 'setFacetFilters',
        'optionalFilters' => 'setOptionalFilters',
        'numericFilters' => 'setNumericFilters',
        'tagFilters' => 'setTagFilters',
        'sumOrFiltersScores' => 'setSumOrFiltersScores',
        'restrictSearchableAttributes' => 'setRestrictSearchableAttributes',
        'facets' => 'setFacets',
        'facetingAfterDistinct' => 'setFacetingAfterDistinct',
        'page' => 'setPage',
        'offset' => 'setOffset',
        'length' => 'setLength',
        'aroundLatLng' => 'setAroundLatLng',
        'aroundLatLngViaIP' => 'setAroundLatLngViaIP',
        'aroundRadius' => 'setAroundRadius',
        'aroundPrecision' => 'setAroundPrecision',
        'minimumAroundRadius' => 'setMinimumAroundRadius',
        'insideBoundingBox' => 'setInsideBoundingBox',
        'insidePolygon' => 'setInsidePolygon',
        'naturalLanguages' => 'setNaturalLanguages',
        'ruleContexts' => 'setRuleContexts',
        'personalizationImpact' => 'setPersonalizationImpact',
        'userToken' => 'setUserToken',
        'getRankingInfo' => 'setGetRankingInfo',
        'synonyms' => 'setSynonyms',
        'clickAnalytics' => 'setClickAnalytics',
        'analytics' => 'setAnalytics',
        'analyticsTags' => 'setAnalyticsTags',
        'percentileComputation' => 'setPercentileComputation',
        'enableABTest' => 'setEnableABTest',
        'attributesToRetrieve' => 'setAttributesToRetrieve',
        'ranking' => 'setRanking',
        'customRanking' => 'setCustomRanking',
        'relevancyStrictness' => 'setRelevancyStrictness',
        'attributesToHighlight' => 'setAttributesToHighlight',
        'attributesToSnippet' => 'setAttributesToSnippet',
        'highlightPreTag' => 'setHighlightPreTag',
        'highlightPostTag' => 'setHighlightPostTag',
        'snippetEllipsisText' => 'setSnippetEllipsisText',
        'restrictHighlightAndSnippetArrays' => 'setRestrictHighlightAndSnippetArrays',
        'hitsPerPage' => 'setHitsPerPage',
        'minWordSizefor1Typo' => 'setMinWordSizefor1Typo',
        'minWordSizefor2Typos' => 'setMinWordSizefor2Typos',
        'typoTolerance' => 'setTypoTolerance',
        'allowTyposOnNumericTokens' => 'setAllowTyposOnNumericTokens',
        'disableTypoToleranceOnAttributes' => 'setDisableTypoToleranceOnAttributes',
        'ignorePlurals' => 'setIgnorePlurals',
        'removeStopWords' => 'setRemoveStopWords',
        'keepDiacriticsOnCharacters' => 'setKeepDiacriticsOnCharacters',
        'queryLanguages' => 'setQueryLanguages',
        'decompoundQuery' => 'setDecompoundQuery',
        'enableRules' => 'setEnableRules',
        'enablePersonalization' => 'setEnablePersonalization',
        'queryType' => 'setQueryType',
        'removeWordsIfNoResults' => 'setRemoveWordsIfNoResults',
        'mode' => 'setMode',
        'semanticSearch' => 'setSemanticSearch',
        'advancedSyntax' => 'setAdvancedSyntax',
        'optionalWords' => 'setOptionalWords',
        'disableExactOnAttributes' => 'setDisableExactOnAttributes',
        'exactOnSingleWordQuery' => 'setExactOnSingleWordQuery',
        'alternativesAsExact' => 'setAlternativesAsExact',
        'advancedSyntaxFeatures' => 'setAdvancedSyntaxFeatures',
        'distinct' => 'setDistinct',
        'replaceSynonymsInHighlight' => 'setReplaceSynonymsInHighlight',
        'minProximity' => 'setMinProximity',
        'responseFields' => 'setResponseFields',
        'maxFacetHits' => 'setMaxFacetHits',
        'maxValuesPerFacet' => 'setMaxValuesPerFacet',
        'sortFacetValuesBy' => 'setSortFacetValuesBy',
        'attributeCriteriaComputedByMinProximity' => 'setAttributeCriteriaComputedByMinProximity',
        'renderingContent' => 'setRenderingContent',
        'enableReRanking' => 'setEnableReRanking',
        'reRankingApplyFilter' => 'setReRankingApplyFilter',
        'indexName' => 'setIndexName',
        'type' => 'setType',
        'facet' => 'setFacet',
        'facetQuery' => 'setFacetQuery',
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests).
     *
     * @var string[]
     */
    protected static $getters = [
        'params' => 'getParams',
        'query' => 'getQuery',
        'similarQuery' => 'getSimilarQuery',
        'filters' => 'getFilters',
        'facetFilters' => 'getFacetFilters',
        'optionalFilters' => 'getOptionalFilters',
        'numericFilters' => 'getNumericFilters',
        'tagFilters' => 'getTagFilters',
        'sumOrFiltersScores' => 'getSumOrFiltersScores',
        'restrictSearchableAttributes' => 'getRestrictSearchableAttributes',
        'facets' => 'getFacets',
        'facetingAfterDistinct' => 'getFacetingAfterDistinct',
        'page' => 'getPage',
        'offset' => 'getOffset',
        'length' => 'getLength',
        'aroundLatLng' => 'getAroundLatLng',
        'aroundLatLngViaIP' => 'getAroundLatLngViaIP',
        'aroundRadius' => 'getAroundRadius',
        'aroundPrecision' => 'getAroundPrecision',
        'minimumAroundRadius' => 'getMinimumAroundRadius',
        'insideBoundingBox' => 'getInsideBoundingBox',
        'insidePolygon' => 'getInsidePolygon',
        'naturalLanguages' => 'getNaturalLanguages',
        'ruleContexts' => 'getRuleContexts',
        'personalizationImpact' => 'getPersonalizationImpact',
        'userToken' => 'getUserToken',
        'getRankingInfo' => 'getGetRankingInfo',
        'synonyms' => 'getSynonyms',
        'clickAnalytics' => 'getClickAnalytics',
        'analytics' => 'getAnalytics',
        'analyticsTags' => 'getAnalyticsTags',
        'percentileComputation' => 'getPercentileComputation',
        'enableABTest' => 'getEnableABTest',
        'attributesToRetrieve' => 'getAttributesToRetrieve',
        'ranking' => 'getRanking',
        'customRanking' => 'getCustomRanking',
        'relevancyStrictness' => 'getRelevancyStrictness',
        'attributesToHighlight' => 'getAttributesToHighlight',
        'attributesToSnippet' => 'getAttributesToSnippet',
        'highlightPreTag' => 'getHighlightPreTag',
        'highlightPostTag' => 'getHighlightPostTag',
        'snippetEllipsisText' => 'getSnippetEllipsisText',
        'restrictHighlightAndSnippetArrays' => 'getRestrictHighlightAndSnippetArrays',
        'hitsPerPage' => 'getHitsPerPage',
        'minWordSizefor1Typo' => 'getMinWordSizefor1Typo',
        'minWordSizefor2Typos' => 'getMinWordSizefor2Typos',
        'typoTolerance' => 'getTypoTolerance',
        'allowTyposOnNumericTokens' => 'getAllowTyposOnNumericTokens',
        'disableTypoToleranceOnAttributes' => 'getDisableTypoToleranceOnAttributes',
        'ignorePlurals' => 'getIgnorePlurals',
        'removeStopWords' => 'getRemoveStopWords',
        'keepDiacriticsOnCharacters' => 'getKeepDiacriticsOnCharacters',
        'queryLanguages' => 'getQueryLanguages',
        'decompoundQuery' => 'getDecompoundQuery',
        'enableRules' => 'getEnableRules',
        'enablePersonalization' => 'getEnablePersonalization',
        'queryType' => 'getQueryType',
        'removeWordsIfNoResults' => 'getRemoveWordsIfNoResults',
        'mode' => 'getMode',
        'semanticSearch' => 'getSemanticSearch',
        'advancedSyntax' => 'getAdvancedSyntax',
        'optionalWords' => 'getOptionalWords',
        'disableExactOnAttributes' => 'getDisableExactOnAttributes',
        'exactOnSingleWordQuery' => 'getExactOnSingleWordQuery',
        'alternativesAsExact' => 'getAlternativesAsExact',
        'advancedSyntaxFeatures' => 'getAdvancedSyntaxFeatures',
        'distinct' => 'getDistinct',
        'replaceSynonymsInHighlight' => 'getReplaceSynonymsInHighlight',
        'minProximity' => 'getMinProximity',
        'responseFields' => 'getResponseFields',
        'maxFacetHits' => 'getMaxFacetHits',
        'maxValuesPerFacet' => 'getMaxValuesPerFacet',
        'sortFacetValuesBy' => 'getSortFacetValuesBy',
        'attributeCriteriaComputedByMinProximity' => 'getAttributeCriteriaComputedByMinProximity',
        'renderingContent' => 'getRenderingContent',
        'enableReRanking' => 'getEnableReRanking',
        'reRankingApplyFilter' => 'getReRankingApplyFilter',
        'indexName' => 'getIndexName',
        'type' => 'getType',
        'facet' => 'getFacet',
        'facetQuery' => 'getFacetQuery',
    ];

    /**
     * Associative array for storing property values.
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor.
     *
     * @param mixed[] $data Associated array of property values
     */
    public function __construct(?array $data = null)
    {
        if (isset($data['params'])) {
            $this->container['params'] = $data['params'];
        }
        if (isset($data['query'])) {
            $this->container['query'] = $data['query'];
        }
        if (isset($data['similarQuery'])) {
            $this->container['similarQuery'] = $data['similarQuery'];
        }
        if (isset($data['filters'])) {
            $this->container['filters'] = $data['filters'];
        }
        if (isset($data['facetFilters'])) {
            $this->container['facetFilters'] = $data['facetFilters'];
        }
        if (isset($data['optionalFilters'])) {
            $this->container['optionalFilters'] = $data['optionalFilters'];
        }
        if (isset($data['numericFilters'])) {
            $this->container['numericFilters'] = $data['numericFilters'];
        }
        if (isset($data['tagFilters'])) {
            $this->container['tagFilters'] = $data['tagFilters'];
        }
        if (isset($data['sumOrFiltersScores'])) {
            $this->container['sumOrFiltersScores'] = $data['sumOrFiltersScores'];
        }
        if (isset($data['restrictSearchableAttributes'])) {
            $this->container['restrictSearchableAttributes'] = $data['restrictSearchableAttributes'];
        }
        if (isset($data['facets'])) {
            $this->container['facets'] = $data['facets'];
        }
        if (isset($data['facetingAfterDistinct'])) {
            $this->container['facetingAfterDistinct'] = $data['facetingAfterDistinct'];
        }
        if (isset($data['page'])) {
            $this->container['page'] = $data['page'];
        }
        if (isset($data['offset'])) {
            $this->container['offset'] = $data['offset'];
        }
        if (isset($data['length'])) {
            $this->container['length'] = $data['length'];
        }
        if (isset($data['aroundLatLng'])) {
            $this->container['aroundLatLng'] = $data['aroundLatLng'];
        }
        if (isset($data['aroundLatLngViaIP'])) {
            $this->container['aroundLatLngViaIP'] = $data['aroundLatLngViaIP'];
        }
        if (isset($data['aroundRadius'])) {
            $this->container['aroundRadius'] = $data['aroundRadius'];
        }
        if (isset($data['aroundPrecision'])) {
            $this->container['aroundPrecision'] = $data['aroundPrecision'];
        }
        if (isset($data['minimumAroundRadius'])) {
            $this->container['minimumAroundRadius'] = $data['minimumAroundRadius'];
        }
        if (isset($data['insideBoundingBox'])) {
            $this->container['insideBoundingBox'] = $data['insideBoundingBox'];
        }
        if (isset($data['insidePolygon'])) {
            $this->container['insidePolygon'] = $data['insidePolygon'];
        }
        if (isset($data['naturalLanguages'])) {
            $this->container['naturalLanguages'] = $data['naturalLanguages'];
        }
        if (isset($data['ruleContexts'])) {
            $this->container['ruleContexts'] = $data['ruleContexts'];
        }
        if (isset($data['personalizationImpact'])) {
            $this->container['personalizationImpact'] = $data['personalizationImpact'];
        }
        if (isset($data['userToken'])) {
            $this->container['userToken'] = $data['userToken'];
        }
        if (isset($data['getRankingInfo'])) {
            $this->container['getRankingInfo'] = $data['getRankingInfo'];
        }
        if (isset($data['synonyms'])) {
            $this->container['synonyms'] = $data['synonyms'];
        }
        if (isset($data['clickAnalytics'])) {
            $this->container['clickAnalytics'] = $data['clickAnalytics'];
        }
        if (isset($data['analytics'])) {
            $this->container['analytics'] = $data['analytics'];
        }
        if (isset($data['analyticsTags'])) {
            $this->container['analyticsTags'] = $data['analyticsTags'];
        }
        if (isset($data['percentileComputation'])) {
            $this->container['percentileComputation'] = $data['percentileComputation'];
        }
        if (isset($data['enableABTest'])) {
            $this->container['enableABTest'] = $data['enableABTest'];
        }
        if (isset($data['attributesToRetrieve'])) {
            $this->container['attributesToRetrieve'] = $data['attributesToRetrieve'];
        }
        if (isset($data['ranking'])) {
            $this->container['ranking'] = $data['ranking'];
        }
        if (isset($data['customRanking'])) {
            $this->container['customRanking'] = $data['customRanking'];
        }
        if (isset($data['relevancyStrictness'])) {
            $this->container['relevancyStrictness'] = $data['relevancyStrictness'];
        }
        if (isset($data['attributesToHighlight'])) {
            $this->container['attributesToHighlight'] = $data['attributesToHighlight'];
        }
        if (isset($data['attributesToSnippet'])) {
            $this->container['attributesToSnippet'] = $data['attributesToSnippet'];
        }
        if (isset($data['highlightPreTag'])) {
            $this->container['highlightPreTag'] = $data['highlightPreTag'];
        }
        if (isset($data['highlightPostTag'])) {
            $this->container['highlightPostTag'] = $data['highlightPostTag'];
        }
        if (isset($data['snippetEllipsisText'])) {
            $this->container['snippetEllipsisText'] = $data['snippetEllipsisText'];
        }
        if (isset($data['restrictHighlightAndSnippetArrays'])) {
            $this->container['restrictHighlightAndSnippetArrays'] = $data['restrictHighlightAndSnippetArrays'];
        }
        if (isset($data['hitsPerPage'])) {
            $this->container['hitsPerPage'] = $data['hitsPerPage'];
        }
        if (isset($data['minWordSizefor1Typo'])) {
            $this->container['minWordSizefor1Typo'] = $data['minWordSizefor1Typo'];
        }
        if (isset($data['minWordSizefor2Typos'])) {
            $this->container['minWordSizefor2Typos'] = $data['minWordSizefor2Typos'];
        }
        if (isset($data['typoTolerance'])) {
            $this->container['typoTolerance'] = $data['typoTolerance'];
        }
        if (isset($data['allowTyposOnNumericTokens'])) {
            $this->container['allowTyposOnNumericTokens'] = $data['allowTyposOnNumericTokens'];
        }
        if (isset($data['disableTypoToleranceOnAttributes'])) {
            $this->container['disableTypoToleranceOnAttributes'] = $data['disableTypoToleranceOnAttributes'];
        }
        if (isset($data['ignorePlurals'])) {
            $this->container['ignorePlurals'] = $data['ignorePlurals'];
        }
        if (isset($data['removeStopWords'])) {
            $this->container['removeStopWords'] = $data['removeStopWords'];
        }
        if (isset($data['keepDiacriticsOnCharacters'])) {
            $this->container['keepDiacriticsOnCharacters'] = $data['keepDiacriticsOnCharacters'];
        }
        if (isset($data['queryLanguages'])) {
            $this->container['queryLanguages'] = $data['queryLanguages'];
        }
        if (isset($data['decompoundQuery'])) {
            $this->container['decompoundQuery'] = $data['decompoundQuery'];
        }
        if (isset($data['enableRules'])) {
            $this->container['enableRules'] = $data['enableRules'];
        }
        if (isset($data['enablePersonalization'])) {
            $this->container['enablePersonalization'] = $data['enablePersonalization'];
        }
        if (isset($data['queryType'])) {
            $this->container['queryType'] = $data['queryType'];
        }
        if (isset($data['removeWordsIfNoResults'])) {
            $this->container['removeWordsIfNoResults'] = $data['removeWordsIfNoResults'];
        }
        if (isset($data['mode'])) {
            $this->container['mode'] = $data['mode'];
        }
        if (isset($data['semanticSearch'])) {
            $this->container['semanticSearch'] = $data['semanticSearch'];
        }
        if (isset($data['advancedSyntax'])) {
            $this->container['advancedSyntax'] = $data['advancedSyntax'];
        }
        if (isset($data['optionalWords'])) {
            $this->container['optionalWords'] = $data['optionalWords'];
        }
        if (isset($data['disableExactOnAttributes'])) {
            $this->container['disableExactOnAttributes'] = $data['disableExactOnAttributes'];
        }
        if (isset($data['exactOnSingleWordQuery'])) {
            $this->container['exactOnSingleWordQuery'] = $data['exactOnSingleWordQuery'];
        }
        if (isset($data['alternativesAsExact'])) {
            $this->container['alternativesAsExact'] = $data['alternativesAsExact'];
        }
        if (isset($data['advancedSyntaxFeatures'])) {
            $this->container['advancedSyntaxFeatures'] = $data['advancedSyntaxFeatures'];
        }
        if (isset($data['distinct'])) {
            $this->container['distinct'] = $data['distinct'];
        }
        if (isset($data['replaceSynonymsInHighlight'])) {
            $this->container['replaceSynonymsInHighlight'] = $data['replaceSynonymsInHighlight'];
        }
        if (isset($data['minProximity'])) {
            $this->container['minProximity'] = $data['minProximity'];
        }
        if (isset($data['responseFields'])) {
            $this->container['responseFields'] = $data['responseFields'];
        }
        if (isset($data['maxFacetHits'])) {
            $this->container['maxFacetHits'] = $data['maxFacetHits'];
        }
        if (isset($data['maxValuesPerFacet'])) {
            $this->container['maxValuesPerFacet'] = $data['maxValuesPerFacet'];
        }
        if (isset($data['sortFacetValuesBy'])) {
            $this->container['sortFacetValuesBy'] = $data['sortFacetValuesBy'];
        }
        if (isset($data['attributeCriteriaComputedByMinProximity'])) {
            $this->container['attributeCriteriaComputedByMinProximity'] = $data['attributeCriteriaComputedByMinProximity'];
        }
        if (isset($data['renderingContent'])) {
            $this->container['renderingContent'] = $data['renderingContent'];
        }
        if (isset($data['enableReRanking'])) {
            $this->container['enableReRanking'] = $data['enableReRanking'];
        }
        if (isset($data['reRankingApplyFilter'])) {
            $this->container['reRankingApplyFilter'] = $data['reRankingApplyFilter'];
        }
        if (isset($data['indexName'])) {
            $this->container['indexName'] = $data['indexName'];
        }
        if (isset($data['type'])) {
            $this->container['type'] = $data['type'];
        }
        if (isset($data['facet'])) {
            $this->container['facet'] = $data['facet'];
        }
        if (isset($data['facetQuery'])) {
            $this->container['facetQuery'] = $data['facetQuery'];
        }
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name.
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of property to type mappings. Used for (de)serialization.
     *
     * @return array
     */
    public static function modelTypes()
    {
        return self::$modelTypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization.
     *
     * @return array
     */
    public static function modelFormats()
    {
        return self::$modelFormats;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses).
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests).
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        if (isset($this->container['page']) && ($this->container['page'] < 0)) {
            $invalidProperties[] = "invalid value for 'page', must be bigger than or equal to 0.";
        }

        if (isset($this->container['length']) && ($this->container['length'] > 1000)) {
            $invalidProperties[] = "invalid value for 'length', must be smaller than or equal to 1000.";
        }

        if (isset($this->container['length']) && ($this->container['length'] < 1)) {
            $invalidProperties[] = "invalid value for 'length', must be bigger than or equal to 1.";
        }

        if (isset($this->container['minimumAroundRadius']) && ($this->container['minimumAroundRadius'] < 1)) {
            $invalidProperties[] = "invalid value for 'minimumAroundRadius', must be bigger than or equal to 1.";
        }

        if (isset($this->container['personalizationImpact']) && ($this->container['personalizationImpact'] > 100)) {
            $invalidProperties[] = "invalid value for 'personalizationImpact', must be smaller than or equal to 100.";
        }

        if (isset($this->container['personalizationImpact']) && ($this->container['personalizationImpact'] < 0)) {
            $invalidProperties[] = "invalid value for 'personalizationImpact', must be bigger than or equal to 0.";
        }

        if (isset($this->container['hitsPerPage']) && ($this->container['hitsPerPage'] > 1000)) {
            $invalidProperties[] = "invalid value for 'hitsPerPage', must be smaller than or equal to 1000.";
        }

        if (isset($this->container['hitsPerPage']) && ($this->container['hitsPerPage'] < 1)) {
            $invalidProperties[] = "invalid value for 'hitsPerPage', must be bigger than or equal to 1.";
        }

        if (isset($this->container['minProximity']) && ($this->container['minProximity'] > 7)) {
            $invalidProperties[] = "invalid value for 'minProximity', must be smaller than or equal to 7.";
        }

        if (isset($this->container['minProximity']) && ($this->container['minProximity'] < 1)) {
            $invalidProperties[] = "invalid value for 'minProximity', must be bigger than or equal to 1.";
        }

        if (isset($this->container['maxFacetHits']) && ($this->container['maxFacetHits'] > 100)) {
            $invalidProperties[] = "invalid value for 'maxFacetHits', must be smaller than or equal to 100.";
        }

        if (isset($this->container['maxValuesPerFacet']) && ($this->container['maxValuesPerFacet'] > 1000)) {
            $invalidProperties[] = "invalid value for 'maxValuesPerFacet', must be smaller than or equal to 1000.";
        }

        if (!isset($this->container['indexName']) || null === $this->container['indexName']) {
            $invalidProperties[] = "'indexName' can't be null";
        }
        if (!isset($this->container['type']) || null === $this->container['type']) {
            $invalidProperties[] = "'type' can't be null";
        }
        if (!isset($this->container['facet']) || null === $this->container['facet']) {
            $invalidProperties[] = "'facet' can't be null";
        }

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed.
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return 0 === count($this->listInvalidProperties());
    }

    /**
     * Gets params.
     *
     * @return null|string
     */
    public function getParams()
    {
        return $this->container['params'] ?? null;
    }

    /**
     * Sets params.
     *
     * @param null|string $params search parameters as a URL-encoded query string
     *
     * @return self
     */
    public function setParams($params)
    {
        $this->container['params'] = $params;

        return $this;
    }

    /**
     * Gets query.
     *
     * @return null|string
     */
    public function getQuery()
    {
        return $this->container['query'] ?? null;
    }

    /**
     * Sets query.
     *
     * @param null|string $query search query
     *
     * @return self
     */
    public function setQuery($query)
    {
        $this->container['query'] = $query;

        return $this;
    }

    /**
     * Gets similarQuery.
     *
     * @return null|string
     */
    public function getSimilarQuery()
    {
        return $this->container['similarQuery'] ?? null;
    }

    /**
     * Sets similarQuery.
     *
     * @param null|string $similarQuery Keywords to be used instead of the search query to conduct a more broader search.  Using the `similarQuery` parameter changes other settings:  - `queryType` is set to `prefixNone`. - `removeStopWords` is set to true. - `words` is set as the first ranking criterion. - All remaining words are treated as `optionalWords`.  Since the `similarQuery` is supposed to do a broad search, they usually return many results. Combine it with `filters` to narrow down the list of results.
     *
     * @return self
     */
    public function setSimilarQuery($similarQuery)
    {
        $this->container['similarQuery'] = $similarQuery;

        return $this;
    }

    /**
     * Gets filters.
     *
     * @return null|string
     */
    public function getFilters()
    {
        return $this->container['filters'] ?? null;
    }

    /**
     * Sets filters.
     *
     * @param null|string $filters Filter expression to only include items that match the filter criteria in the response.  You can use these filter expressions:  - **Numeric filters.** `<facet> <op> <number>`, where `<op>` is one of `<`, `<=`, `=`, `!=`, `>`, `>=`. - **Ranges.** `<facet>:<lower> TO <upper>` where `<lower>` and `<upper>` are the lower and upper limits of the range (inclusive). - **Facet filters.** `<facet>:<value>` where `<facet>` is a facet attribute (case-sensitive) and `<value>` a facet value. - **Tag filters.** `_tags:<value>` or just `<value>` (case-sensitive). - **Boolean filters.** `<facet>: true | false`.  You can combine filters with `AND`, `OR`, and `NOT` operators with the following restrictions:  - You can only combine filters of the same type with `OR`.   **Not supported:** `facet:value OR num > 3`. - You can't use `NOT` with combinations of filters.   **Not supported:** `NOT(facet:value OR facet:value)` - You can't combine conjunctions (`AND`) with `OR`.   **Not supported:** `facet:value OR (facet:value AND facet:value)`  Use quotes around your filters, if the facet attribute name or facet value has spaces, keywords (`OR`, `AND`, `NOT`), or quotes. If a facet attribute is an array, the filter matches if it matches at least one element of the array.  For more information, see [Filters](https://www.algolia.com/doc/guides/managing-results/refine-results/filtering/).
     *
     * @return self
     */
    public function setFilters($filters)
    {
        $this->container['filters'] = $filters;

        return $this;
    }

    /**
     * Gets facetFilters.
     *
     * @return null|FacetFilters
     */
    public function getFacetFilters()
    {
        return $this->container['facetFilters'] ?? null;
    }

    /**
     * Sets facetFilters.
     *
     * @param null|FacetFilters $facetFilters facetFilters
     *
     * @return self
     */
    public function setFacetFilters($facetFilters)
    {
        $this->container['facetFilters'] = $facetFilters;

        return $this;
    }

    /**
     * Gets optionalFilters.
     *
     * @return null|OptionalFilters
     */
    public function getOptionalFilters()
    {
        return $this->container['optionalFilters'] ?? null;
    }

    /**
     * Sets optionalFilters.
     *
     * @param null|OptionalFilters $optionalFilters optionalFilters
     *
     * @return self
     */
    public function setOptionalFilters($optionalFilters)
    {
        $this->container['optionalFilters'] = $optionalFilters;

        return $this;
    }

    /**
     * Gets numericFilters.
     *
     * @return null|NumericFilters
     */
    public function getNumericFilters()
    {
        return $this->container['numericFilters'] ?? null;
    }

    /**
     * Sets numericFilters.
     *
     * @param null|NumericFilters $numericFilters numericFilters
     *
     * @return self
     */
    public function setNumericFilters($numericFilters)
    {
        $this->container['numericFilters'] = $numericFilters;

        return $this;
    }

    /**
     * Gets tagFilters.
     *
     * @return null|TagFilters
     */
    public function getTagFilters()
    {
        return $this->container['tagFilters'] ?? null;
    }

    /**
     * Sets tagFilters.
     *
     * @param null|TagFilters $tagFilters tagFilters
     *
     * @return self
     */
    public function setTagFilters($tagFilters)
    {
        $this->container['tagFilters'] = $tagFilters;

        return $this;
    }

    /**
     * Gets sumOrFiltersScores.
     *
     * @return null|bool
     */
    public function getSumOrFiltersScores()
    {
        return $this->container['sumOrFiltersScores'] ?? null;
    }

    /**
     * Sets sumOrFiltersScores.
     *
     * @param null|bool $sumOrFiltersScores Whether to sum all filter scores.  If true, all filter scores are summed. Otherwise, the maximum filter score is kept. For more information, see [filter scores](https://www.algolia.com/doc/guides/managing-results/refine-results/filtering/in-depth/filter-scoring/#accumulating-scores-with-sumorfiltersscores).
     *
     * @return self
     */
    public function setSumOrFiltersScores($sumOrFiltersScores)
    {
        $this->container['sumOrFiltersScores'] = $sumOrFiltersScores;

        return $this;
    }

    /**
     * Gets restrictSearchableAttributes.
     *
     * @return null|string[]
     */
    public function getRestrictSearchableAttributes()
    {
        return $this->container['restrictSearchableAttributes'] ?? null;
    }

    /**
     * Sets restrictSearchableAttributes.
     *
     * @param null|string[] $restrictSearchableAttributes Restricts a search to a subset of your searchable attributes. Attribute names are case-sensitive.
     *
     * @return self
     */
    public function setRestrictSearchableAttributes($restrictSearchableAttributes)
    {
        $this->container['restrictSearchableAttributes'] = $restrictSearchableAttributes;

        return $this;
    }

    /**
     * Gets facets.
     *
     * @return null|string[]
     */
    public function getFacets()
    {
        return $this->container['facets'] ?? null;
    }

    /**
     * Sets facets.
     *
     * @param null|string[] $facets Facets for which to retrieve facet values that match the search criteria and the number of matching facet values.  To retrieve all facets, use the wildcard character `*`. For more information, see [facets](https://www.algolia.com/doc/guides/managing-results/refine-results/faceting/#contextual-facet-values-and-counts).
     *
     * @return self
     */
    public function setFacets($facets)
    {
        $this->container['facets'] = $facets;

        return $this;
    }

    /**
     * Gets facetingAfterDistinct.
     *
     * @return null|bool
     */
    public function getFacetingAfterDistinct()
    {
        return $this->container['facetingAfterDistinct'] ?? null;
    }

    /**
     * Sets facetingAfterDistinct.
     *
     * @param null|bool $facetingAfterDistinct Whether faceting should be applied after deduplication with `distinct`.  This leads to accurate facet counts when using faceting in combination with `distinct`. It's usually better to use `afterDistinct` modifiers in the `attributesForFaceting` setting, as `facetingAfterDistinct` only computes correct facet counts if all records have the same facet values for the `attributeForDistinct`.
     *
     * @return self
     */
    public function setFacetingAfterDistinct($facetingAfterDistinct)
    {
        $this->container['facetingAfterDistinct'] = $facetingAfterDistinct;

        return $this;
    }

    /**
     * Gets page.
     *
     * @return null|int
     */
    public function getPage()
    {
        return $this->container['page'] ?? null;
    }

    /**
     * Sets page.
     *
     * @param null|int $page page of search results to retrieve
     *
     * @return self
     */
    public function setPage($page)
    {
        if (!is_null($page) && ($page < 0)) {
            throw new \InvalidArgumentException('invalid value for $page when calling SearchQuery., must be bigger than or equal to 0.');
        }

        $this->container['page'] = $page;

        return $this;
    }

    /**
     * Gets offset.
     *
     * @return null|int
     */
    public function getOffset()
    {
        return $this->container['offset'] ?? null;
    }

    /**
     * Sets offset.
     *
     * @param null|int $offset position of the first hit to retrieve
     *
     * @return self
     */
    public function setOffset($offset)
    {
        $this->container['offset'] = $offset;

        return $this;
    }

    /**
     * Gets length.
     *
     * @return null|int
     */
    public function getLength()
    {
        return $this->container['length'] ?? null;
    }

    /**
     * Sets length.
     *
     * @param null|int $length number of hits to retrieve (used in combination with `offset`)
     *
     * @return self
     */
    public function setLength($length)
    {
        if (!is_null($length) && ($length > 1000)) {
            throw new \InvalidArgumentException('invalid value for $length when calling SearchQuery., must be smaller than or equal to 1000.');
        }
        if (!is_null($length) && ($length < 1)) {
            throw new \InvalidArgumentException('invalid value for $length when calling SearchQuery., must be bigger than or equal to 1.');
        }

        $this->container['length'] = $length;

        return $this;
    }

    /**
     * Gets aroundLatLng.
     *
     * @return null|string
     */
    public function getAroundLatLng()
    {
        return $this->container['aroundLatLng'] ?? null;
    }

    /**
     * Sets aroundLatLng.
     *
     * @param null|string $aroundLatLng Coordinates for the center of a circle, expressed as a comma-separated string of latitude and longitude.  Only records included within circle around this central location are included in the results. The radius of the circle is determined by the `aroundRadius` and `minimumAroundRadius` settings. This parameter is ignored if you also specify `insidePolygon` or `insideBoundingBox`.
     *
     * @return self
     */
    public function setAroundLatLng($aroundLatLng)
    {
        $this->container['aroundLatLng'] = $aroundLatLng;

        return $this;
    }

    /**
     * Gets aroundLatLngViaIP.
     *
     * @return null|bool
     */
    public function getAroundLatLngViaIP()
    {
        return $this->container['aroundLatLngViaIP'] ?? null;
    }

    /**
     * Sets aroundLatLngViaIP.
     *
     * @param null|bool $aroundLatLngViaIP whether to obtain the coordinates from the request's IP address
     *
     * @return self
     */
    public function setAroundLatLngViaIP($aroundLatLngViaIP)
    {
        $this->container['aroundLatLngViaIP'] = $aroundLatLngViaIP;

        return $this;
    }

    /**
     * Gets aroundRadius.
     *
     * @return null|AroundRadius
     */
    public function getAroundRadius()
    {
        return $this->container['aroundRadius'] ?? null;
    }

    /**
     * Sets aroundRadius.
     *
     * @param null|AroundRadius $aroundRadius aroundRadius
     *
     * @return self
     */
    public function setAroundRadius($aroundRadius)
    {
        $this->container['aroundRadius'] = $aroundRadius;

        return $this;
    }

    /**
     * Gets aroundPrecision.
     *
     * @return null|AroundPrecision
     */
    public function getAroundPrecision()
    {
        return $this->container['aroundPrecision'] ?? null;
    }

    /**
     * Sets aroundPrecision.
     *
     * @param null|AroundPrecision $aroundPrecision aroundPrecision
     *
     * @return self
     */
    public function setAroundPrecision($aroundPrecision)
    {
        $this->container['aroundPrecision'] = $aroundPrecision;

        return $this;
    }

    /**
     * Gets minimumAroundRadius.
     *
     * @return null|int
     */
    public function getMinimumAroundRadius()
    {
        return $this->container['minimumAroundRadius'] ?? null;
    }

    /**
     * Sets minimumAroundRadius.
     *
     * @param null|int $minimumAroundRadius minimum radius (in meters) for a search around a location when `aroundRadius` isn't set
     *
     * @return self
     */
    public function setMinimumAroundRadius($minimumAroundRadius)
    {
        if (!is_null($minimumAroundRadius) && ($minimumAroundRadius < 1)) {
            throw new \InvalidArgumentException('invalid value for $minimumAroundRadius when calling SearchQuery., must be bigger than or equal to 1.');
        }

        $this->container['minimumAroundRadius'] = $minimumAroundRadius;

        return $this;
    }

    /**
     * Gets insideBoundingBox.
     *
     * @return null|float[][]
     */
    public function getInsideBoundingBox()
    {
        return $this->container['insideBoundingBox'] ?? null;
    }

    /**
     * Sets insideBoundingBox.
     *
     * @param null|float[][] $insideBoundingBox Coordinates for a rectangular area in which to search.  Each bounding box is defined by the two opposite points of its diagonal, and expressed as latitude and longitude pair: `[p1 lat, p1 long, p2 lat, p2 long]`. Provide multiple bounding boxes as nested arrays. For more information, see [rectangular area](https://www.algolia.com/doc/guides/managing-results/refine-results/geolocation/#filtering-inside-rectangular-or-polygonal-areas).
     *
     * @return self
     */
    public function setInsideBoundingBox($insideBoundingBox)
    {
        $this->container['insideBoundingBox'] = $insideBoundingBox;

        return $this;
    }

    /**
     * Gets insidePolygon.
     *
     * @return null|float[][]
     */
    public function getInsidePolygon()
    {
        return $this->container['insidePolygon'] ?? null;
    }

    /**
     * Sets insidePolygon.
     *
     * @param null|float[][] $insidePolygon Coordinates of a polygon in which to search.  Polygons are defined by 3 to 10,000 points. Each point is represented by its latitude and longitude. Provide multiple polygons as nested arrays. For more information, see [filtering inside polygons](https://www.algolia.com/doc/guides/managing-results/refine-results/geolocation/#filtering-inside-rectangular-or-polygonal-areas). This parameter is ignored if you also specify `insideBoundingBox`.
     *
     * @return self
     */
    public function setInsidePolygon($insidePolygon)
    {
        $this->container['insidePolygon'] = $insidePolygon;

        return $this;
    }

    /**
     * Gets naturalLanguages.
     *
     * @return null|\Algolia\AlgoliaSearch\Model\Search\SupportedLanguage[]
     */
    public function getNaturalLanguages()
    {
        return $this->container['naturalLanguages'] ?? null;
    }

    /**
     * Sets naturalLanguages.
     *
     * @param null|\Algolia\AlgoliaSearch\Model\Search\SupportedLanguage[] $naturalLanguages ISO language codes that adjust settings that are useful for processing natural language queries (as opposed to keyword searches):  - Sets `removeStopWords` and `ignorePlurals` to the list of provided languages. - Sets `removeWordsIfNoResults` to `allOptional`. - Adds a `natural_language` attribute to `ruleContexts` and `analyticsTags`.
     *
     * @return self
     */
    public function setNaturalLanguages($naturalLanguages)
    {
        $this->container['naturalLanguages'] = $naturalLanguages;

        return $this;
    }

    /**
     * Gets ruleContexts.
     *
     * @return null|string[]
     */
    public function getRuleContexts()
    {
        return $this->container['ruleContexts'] ?? null;
    }

    /**
     * Sets ruleContexts.
     *
     * @param null|string[] $ruleContexts Assigns a rule context to the search query.  [Rule contexts](https://www.algolia.com/doc/guides/managing-results/rules/rules-overview/how-to/customize-search-results-by-platform/#whats-a-context) are strings that you can use to trigger matching rules.
     *
     * @return self
     */
    public function setRuleContexts($ruleContexts)
    {
        $this->container['ruleContexts'] = $ruleContexts;

        return $this;
    }

    /**
     * Gets personalizationImpact.
     *
     * @return null|int
     */
    public function getPersonalizationImpact()
    {
        return $this->container['personalizationImpact'] ?? null;
    }

    /**
     * Sets personalizationImpact.
     *
     * @param null|int $personalizationImpact Impact that Personalization should have on this search.  The higher this value is, the more Personalization determines the ranking compared to other factors. For more information, see [Understanding Personalization impact](https://www.algolia.com/doc/guides/personalization/personalizing-results/in-depth/configuring-personalization/#understanding-personalization-impact).
     *
     * @return self
     */
    public function setPersonalizationImpact($personalizationImpact)
    {
        if (!is_null($personalizationImpact) && ($personalizationImpact > 100)) {
            throw new \InvalidArgumentException('invalid value for $personalizationImpact when calling SearchQuery., must be smaller than or equal to 100.');
        }
        if (!is_null($personalizationImpact) && ($personalizationImpact < 0)) {
            throw new \InvalidArgumentException('invalid value for $personalizationImpact when calling SearchQuery., must be bigger than or equal to 0.');
        }

        $this->container['personalizationImpact'] = $personalizationImpact;

        return $this;
    }

    /**
     * Gets userToken.
     *
     * @return null|string
     */
    public function getUserToken()
    {
        return $this->container['userToken'] ?? null;
    }

    /**
     * Sets userToken.
     *
     * @param null|string $userToken Unique pseudonymous or anonymous user identifier.  This helps with analytics and click and conversion events. For more information, see [user token](https://www.algolia.com/doc/guides/sending-events/concepts/usertoken/).
     *
     * @return self
     */
    public function setUserToken($userToken)
    {
        $this->container['userToken'] = $userToken;

        return $this;
    }

    /**
     * Gets getRankingInfo.
     *
     * @return null|bool
     */
    public function getGetRankingInfo()
    {
        return $this->container['getRankingInfo'] ?? null;
    }

    /**
     * Sets getRankingInfo.
     *
     * @param null|bool $getRankingInfo whether the search response should include detailed ranking information
     *
     * @return self
     */
    public function setGetRankingInfo($getRankingInfo)
    {
        $this->container['getRankingInfo'] = $getRankingInfo;

        return $this;
    }

    /**
     * Gets synonyms.
     *
     * @return null|bool
     */
    public function getSynonyms()
    {
        return $this->container['synonyms'] ?? null;
    }

    /**
     * Sets synonyms.
     *
     * @param null|bool $synonyms whether to take into account an index's synonyms for this search
     *
     * @return self
     */
    public function setSynonyms($synonyms)
    {
        $this->container['synonyms'] = $synonyms;

        return $this;
    }

    /**
     * Gets clickAnalytics.
     *
     * @return null|bool
     */
    public function getClickAnalytics()
    {
        return $this->container['clickAnalytics'] ?? null;
    }

    /**
     * Sets clickAnalytics.
     *
     * @param null|bool $clickAnalytics Whether to include a `queryID` attribute in the response.  The query ID is a unique identifier for a search query and is required for tracking [click and conversion events](https://www.algolia.com/guides/sending-events/getting-started/).
     *
     * @return self
     */
    public function setClickAnalytics($clickAnalytics)
    {
        $this->container['clickAnalytics'] = $clickAnalytics;

        return $this;
    }

    /**
     * Gets analytics.
     *
     * @return null|bool
     */
    public function getAnalytics()
    {
        return $this->container['analytics'] ?? null;
    }

    /**
     * Sets analytics.
     *
     * @param null|bool $analytics whether this search will be included in Analytics
     *
     * @return self
     */
    public function setAnalytics($analytics)
    {
        $this->container['analytics'] = $analytics;

        return $this;
    }

    /**
     * Gets analyticsTags.
     *
     * @return null|string[]
     */
    public function getAnalyticsTags()
    {
        return $this->container['analyticsTags'] ?? null;
    }

    /**
     * Sets analyticsTags.
     *
     * @param null|string[] $analyticsTags Tags to apply to the query for [segmenting analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).
     *
     * @return self
     */
    public function setAnalyticsTags($analyticsTags)
    {
        $this->container['analyticsTags'] = $analyticsTags;

        return $this;
    }

    /**
     * Gets percentileComputation.
     *
     * @return null|bool
     */
    public function getPercentileComputation()
    {
        return $this->container['percentileComputation'] ?? null;
    }

    /**
     * Sets percentileComputation.
     *
     * @param null|bool $percentileComputation whether to include this search when calculating processing-time percentiles
     *
     * @return self
     */
    public function setPercentileComputation($percentileComputation)
    {
        $this->container['percentileComputation'] = $percentileComputation;

        return $this;
    }

    /**
     * Gets enableABTest.
     *
     * @return null|bool
     */
    public function getEnableABTest()
    {
        return $this->container['enableABTest'] ?? null;
    }

    /**
     * Sets enableABTest.
     *
     * @param null|bool $enableABTest whether to enable A/B testing for this search
     *
     * @return self
     */
    public function setEnableABTest($enableABTest)
    {
        $this->container['enableABTest'] = $enableABTest;

        return $this;
    }

    /**
     * Gets attributesToRetrieve.
     *
     * @return null|string[]
     */
    public function getAttributesToRetrieve()
    {
        return $this->container['attributesToRetrieve'] ?? null;
    }

    /**
     * Sets attributesToRetrieve.
     *
     * @param null|string[] $attributesToRetrieve Attributes to include in the API response.  To reduce the size of your response, you can retrieve only some of the attributes. Attribute names are case-sensitive.  - `*` retrieves all attributes, except attributes included in the `customRanking` and `unretrievableAttributes` settings. - To retrieve all attributes except a specific one, prefix the attribute with a dash and combine it with the `*`: `[\"*\", \"-ATTRIBUTE\"]`. - The `objectID` attribute is always included.
     *
     * @return self
     */
    public function setAttributesToRetrieve($attributesToRetrieve)
    {
        $this->container['attributesToRetrieve'] = $attributesToRetrieve;

        return $this;
    }

    /**
     * Gets ranking.
     *
     * @return null|string[]
     */
    public function getRanking()
    {
        return $this->container['ranking'] ?? null;
    }

    /**
     * Sets ranking.
     *
     * @param null|string[] $ranking Determines the order in which Algolia returns your results.  By default, each entry corresponds to a [ranking criteria](https://www.algolia.com/doc/guides/managing-results/relevance-overview/in-depth/ranking-criteria/). The tie-breaking algorithm sequentially applies each criterion in the order they're specified. If you configure a replica index for [sorting by an attribute](https://www.algolia.com/doc/guides/managing-results/refine-results/sorting/how-to/sort-by-attribute/), you put the sorting attribute at the top of the list.  **Modifiers**  - `asc(\"ATTRIBUTE\")`.   Sort the index by the values of an attribute, in ascending order. - `desc(\"ATTRIBUTE\")`.   Sort the index by the values of an attribute, in descending order.  Before you modify the default setting, you should test your changes in the dashboard, and by [A/B testing](https://www.algolia.com/doc/guides/ab-testing/what-is-ab-testing/).
     *
     * @return self
     */
    public function setRanking($ranking)
    {
        $this->container['ranking'] = $ranking;

        return $this;
    }

    /**
     * Gets customRanking.
     *
     * @return null|string[]
     */
    public function getCustomRanking()
    {
        return $this->container['customRanking'] ?? null;
    }

    /**
     * Sets customRanking.
     *
     * @param null|string[] $customRanking Attributes to use as [custom ranking](https://www.algolia.com/doc/guides/managing-results/must-do/custom-ranking/). Attribute names are case-sensitive.  The custom ranking attributes decide which items are shown first if the other ranking criteria are equal.  Records with missing values for your selected custom ranking attributes are always sorted last. Boolean attributes are sorted based on their alphabetical order.  **Modifiers**  - `asc(\"ATTRIBUTE\")`.   Sort the index by the values of an attribute, in ascending order.  - `desc(\"ATTRIBUTE\")`.   Sort the index by the values of an attribute, in descending order.  If you use two or more custom ranking attributes, [reduce the precision](https://www.algolia.com/doc/guides/managing-results/must-do/custom-ranking/how-to/controlling-custom-ranking-metrics-precision/) of your first attributes, or the other attributes will never be applied.
     *
     * @return self
     */
    public function setCustomRanking($customRanking)
    {
        $this->container['customRanking'] = $customRanking;

        return $this;
    }

    /**
     * Gets relevancyStrictness.
     *
     * @return null|int
     */
    public function getRelevancyStrictness()
    {
        return $this->container['relevancyStrictness'] ?? null;
    }

    /**
     * Sets relevancyStrictness.
     *
     * @param null|int $relevancyStrictness Relevancy threshold below which less relevant results aren't included in the results.  You can only set `relevancyStrictness` on [virtual replica indices](https://www.algolia.com/doc/guides/managing-results/refine-results/sorting/in-depth/replicas/#what-are-virtual-replicas). Use this setting to strike a balance between the relevance and number of returned results.
     *
     * @return self
     */
    public function setRelevancyStrictness($relevancyStrictness)
    {
        $this->container['relevancyStrictness'] = $relevancyStrictness;

        return $this;
    }

    /**
     * Gets attributesToHighlight.
     *
     * @return null|string[]
     */
    public function getAttributesToHighlight()
    {
        return $this->container['attributesToHighlight'] ?? null;
    }

    /**
     * Sets attributesToHighlight.
     *
     * @param null|string[] $attributesToHighlight Attributes to highlight.  By default, all searchable attributes are highlighted. Use `*` to highlight all attributes or use an empty array `[]` to turn off highlighting. Attribute names are case-sensitive.  With highlighting, strings that match the search query are surrounded by HTML tags defined by `highlightPreTag` and `highlightPostTag`. You can use this to visually highlight matching parts of a search query in your UI.  For more information, see [Highlighting and snippeting](https://www.algolia.com/doc/guides/building-search-ui/ui-and-ux-patterns/highlighting-snippeting/js/).
     *
     * @return self
     */
    public function setAttributesToHighlight($attributesToHighlight)
    {
        $this->container['attributesToHighlight'] = $attributesToHighlight;

        return $this;
    }

    /**
     * Gets attributesToSnippet.
     *
     * @return null|string[]
     */
    public function getAttributesToSnippet()
    {
        return $this->container['attributesToSnippet'] ?? null;
    }

    /**
     * Sets attributesToSnippet.
     *
     * @param null|string[] $attributesToSnippet Attributes for which to enable snippets. Attribute names are case-sensitive.  Snippets provide additional context to matched words. If you enable snippets, they include 10 words, including the matched word. The matched word will also be wrapped by HTML tags for highlighting. You can adjust the number of words with the following notation: `ATTRIBUTE:NUMBER`, where `NUMBER` is the number of words to be extracted.
     *
     * @return self
     */
    public function setAttributesToSnippet($attributesToSnippet)
    {
        $this->container['attributesToSnippet'] = $attributesToSnippet;

        return $this;
    }

    /**
     * Gets highlightPreTag.
     *
     * @return null|string
     */
    public function getHighlightPreTag()
    {
        return $this->container['highlightPreTag'] ?? null;
    }

    /**
     * Sets highlightPreTag.
     *
     * @param null|string $highlightPreTag HTML tag to insert before the highlighted parts in all highlighted results and snippets
     *
     * @return self
     */
    public function setHighlightPreTag($highlightPreTag)
    {
        $this->container['highlightPreTag'] = $highlightPreTag;

        return $this;
    }

    /**
     * Gets highlightPostTag.
     *
     * @return null|string
     */
    public function getHighlightPostTag()
    {
        return $this->container['highlightPostTag'] ?? null;
    }

    /**
     * Sets highlightPostTag.
     *
     * @param null|string $highlightPostTag HTML tag to insert after the highlighted parts in all highlighted results and snippets
     *
     * @return self
     */
    public function setHighlightPostTag($highlightPostTag)
    {
        $this->container['highlightPostTag'] = $highlightPostTag;

        return $this;
    }

    /**
     * Gets snippetEllipsisText.
     *
     * @return null|string
     */
    public function getSnippetEllipsisText()
    {
        return $this->container['snippetEllipsisText'] ?? null;
    }

    /**
     * Sets snippetEllipsisText.
     *
     * @param null|string $snippetEllipsisText string used as an ellipsis indicator when a snippet is truncated
     *
     * @return self
     */
    public function setSnippetEllipsisText($snippetEllipsisText)
    {
        $this->container['snippetEllipsisText'] = $snippetEllipsisText;

        return $this;
    }

    /**
     * Gets restrictHighlightAndSnippetArrays.
     *
     * @return null|bool
     */
    public function getRestrictHighlightAndSnippetArrays()
    {
        return $this->container['restrictHighlightAndSnippetArrays'] ?? null;
    }

    /**
     * Sets restrictHighlightAndSnippetArrays.
     *
     * @param null|bool $restrictHighlightAndSnippetArrays Whether to restrict highlighting and snippeting to items that at least partially matched the search query. By default, all items are highlighted and snippeted.
     *
     * @return self
     */
    public function setRestrictHighlightAndSnippetArrays($restrictHighlightAndSnippetArrays)
    {
        $this->container['restrictHighlightAndSnippetArrays'] = $restrictHighlightAndSnippetArrays;

        return $this;
    }

    /**
     * Gets hitsPerPage.
     *
     * @return null|int
     */
    public function getHitsPerPage()
    {
        return $this->container['hitsPerPage'] ?? null;
    }

    /**
     * Sets hitsPerPage.
     *
     * @param null|int $hitsPerPage number of hits per page
     *
     * @return self
     */
    public function setHitsPerPage($hitsPerPage)
    {
        if (!is_null($hitsPerPage) && ($hitsPerPage > 1000)) {
            throw new \InvalidArgumentException('invalid value for $hitsPerPage when calling SearchQuery., must be smaller than or equal to 1000.');
        }
        if (!is_null($hitsPerPage) && ($hitsPerPage < 1)) {
            throw new \InvalidArgumentException('invalid value for $hitsPerPage when calling SearchQuery., must be bigger than or equal to 1.');
        }

        $this->container['hitsPerPage'] = $hitsPerPage;

        return $this;
    }

    /**
     * Gets minWordSizefor1Typo.
     *
     * @return null|int
     */
    public function getMinWordSizefor1Typo()
    {
        return $this->container['minWordSizefor1Typo'] ?? null;
    }

    /**
     * Sets minWordSizefor1Typo.
     *
     * @param null|int $minWordSizefor1Typo Minimum number of characters a word in the search query must contain to accept matches with [one typo](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/typo-tolerance/in-depth/configuring-typo-tolerance/#configuring-word-length-for-typos).
     *
     * @return self
     */
    public function setMinWordSizefor1Typo($minWordSizefor1Typo)
    {
        $this->container['minWordSizefor1Typo'] = $minWordSizefor1Typo;

        return $this;
    }

    /**
     * Gets minWordSizefor2Typos.
     *
     * @return null|int
     */
    public function getMinWordSizefor2Typos()
    {
        return $this->container['minWordSizefor2Typos'] ?? null;
    }

    /**
     * Sets minWordSizefor2Typos.
     *
     * @param null|int $minWordSizefor2Typos Minimum number of characters a word in the search query must contain to accept matches with [two typos](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/typo-tolerance/in-depth/configuring-typo-tolerance/#configuring-word-length-for-typos).
     *
     * @return self
     */
    public function setMinWordSizefor2Typos($minWordSizefor2Typos)
    {
        $this->container['minWordSizefor2Typos'] = $minWordSizefor2Typos;

        return $this;
    }

    /**
     * Gets typoTolerance.
     *
     * @return null|TypoTolerance
     */
    public function getTypoTolerance()
    {
        return $this->container['typoTolerance'] ?? null;
    }

    /**
     * Sets typoTolerance.
     *
     * @param null|TypoTolerance $typoTolerance typoTolerance
     *
     * @return self
     */
    public function setTypoTolerance($typoTolerance)
    {
        $this->container['typoTolerance'] = $typoTolerance;

        return $this;
    }

    /**
     * Gets allowTyposOnNumericTokens.
     *
     * @return null|bool
     */
    public function getAllowTyposOnNumericTokens()
    {
        return $this->container['allowTyposOnNumericTokens'] ?? null;
    }

    /**
     * Sets allowTyposOnNumericTokens.
     *
     * @param null|bool $allowTyposOnNumericTokens Whether to allow typos on numbers in the search query.  Turn off this setting to reduce the number of irrelevant matches when searching in large sets of similar numbers.
     *
     * @return self
     */
    public function setAllowTyposOnNumericTokens($allowTyposOnNumericTokens)
    {
        $this->container['allowTyposOnNumericTokens'] = $allowTyposOnNumericTokens;

        return $this;
    }

    /**
     * Gets disableTypoToleranceOnAttributes.
     *
     * @return null|string[]
     */
    public function getDisableTypoToleranceOnAttributes()
    {
        return $this->container['disableTypoToleranceOnAttributes'] ?? null;
    }

    /**
     * Sets disableTypoToleranceOnAttributes.
     *
     * @param null|string[] $disableTypoToleranceOnAttributes Attributes for which you want to turn off [typo tolerance](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/typo-tolerance/). Attribute names are case-sensitive.  Returning only exact matches can help when:  - [Searching in hyphenated attributes](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/typo-tolerance/how-to/how-to-search-in-hyphenated-attributes/). - Reducing the number of matches when you have too many.   This can happen with attributes that are long blocks of text, such as product descriptions.  Consider alternatives such as `disableTypoToleranceOnWords` or adding synonyms if your attributes have intentional unusual spellings that might look like typos.
     *
     * @return self
     */
    public function setDisableTypoToleranceOnAttributes($disableTypoToleranceOnAttributes)
    {
        $this->container['disableTypoToleranceOnAttributes'] = $disableTypoToleranceOnAttributes;

        return $this;
    }

    /**
     * Gets ignorePlurals.
     *
     * @return null|IgnorePlurals
     */
    public function getIgnorePlurals()
    {
        return $this->container['ignorePlurals'] ?? null;
    }

    /**
     * Sets ignorePlurals.
     *
     * @param null|IgnorePlurals $ignorePlurals ignorePlurals
     *
     * @return self
     */
    public function setIgnorePlurals($ignorePlurals)
    {
        $this->container['ignorePlurals'] = $ignorePlurals;

        return $this;
    }

    /**
     * Gets removeStopWords.
     *
     * @return null|RemoveStopWords
     */
    public function getRemoveStopWords()
    {
        return $this->container['removeStopWords'] ?? null;
    }

    /**
     * Sets removeStopWords.
     *
     * @param null|RemoveStopWords $removeStopWords removeStopWords
     *
     * @return self
     */
    public function setRemoveStopWords($removeStopWords)
    {
        $this->container['removeStopWords'] = $removeStopWords;

        return $this;
    }

    /**
     * Gets keepDiacriticsOnCharacters.
     *
     * @return null|string
     */
    public function getKeepDiacriticsOnCharacters()
    {
        return $this->container['keepDiacriticsOnCharacters'] ?? null;
    }

    /**
     * Sets keepDiacriticsOnCharacters.
     *
     * @param null|string $keepDiacriticsOnCharacters Characters for which diacritics should be preserved.  By default, Algolia removes diacritics from letters. For example, `é` becomes `e`. If this causes issues in your search, you can specify characters that should keep their diacritics.
     *
     * @return self
     */
    public function setKeepDiacriticsOnCharacters($keepDiacriticsOnCharacters)
    {
        $this->container['keepDiacriticsOnCharacters'] = $keepDiacriticsOnCharacters;

        return $this;
    }

    /**
     * Gets queryLanguages.
     *
     * @return null|\Algolia\AlgoliaSearch\Model\Search\SupportedLanguage[]
     */
    public function getQueryLanguages()
    {
        return $this->container['queryLanguages'] ?? null;
    }

    /**
     * Sets queryLanguages.
     *
     * @param null|\Algolia\AlgoliaSearch\Model\Search\SupportedLanguage[] $queryLanguages Languages for language-specific query processing steps such as plurals, stop-word removal, and word-detection dictionaries.  This setting sets a default list of languages used by the `removeStopWords` and `ignorePlurals` settings. This setting also sets a dictionary for word detection in the logogram-based [CJK](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/handling-natural-languages-nlp/in-depth/normalization/#normalization-for-logogram-based-languages-cjk) languages. To support this, you must place the CJK language **first**.  **You should always specify a query language.** If you don't specify an indexing language, the search engine uses all [supported languages](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/handling-natural-languages-nlp/in-depth/supported-languages/), or the languages you specified with the `ignorePlurals` or `removeStopWords` parameters. This can lead to unexpected search results. For more information, see [Language-specific configuration](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/handling-natural-languages-nlp/in-depth/language-specific-configurations/).
     *
     * @return self
     */
    public function setQueryLanguages($queryLanguages)
    {
        $this->container['queryLanguages'] = $queryLanguages;

        return $this;
    }

    /**
     * Gets decompoundQuery.
     *
     * @return null|bool
     */
    public function getDecompoundQuery()
    {
        return $this->container['decompoundQuery'] ?? null;
    }

    /**
     * Sets decompoundQuery.
     *
     * @param null|bool $decompoundQuery Whether to split compound words in the query into their building blocks.  For more information, see [Word segmentation](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/handling-natural-languages-nlp/in-depth/language-specific-configurations/#splitting-compound-words). Word segmentation is supported for these languages: German, Dutch, Finnish, Swedish, and Norwegian. Decompounding doesn't work for words with [non-spacing mark Unicode characters](https://www.charactercodes.net/category/non-spacing_mark). For example, `Gartenstühle` won't be decompounded if the `ü` consists of `u` (U+0075) and `◌̈` (U+0308).
     *
     * @return self
     */
    public function setDecompoundQuery($decompoundQuery)
    {
        $this->container['decompoundQuery'] = $decompoundQuery;

        return $this;
    }

    /**
     * Gets enableRules.
     *
     * @return null|bool
     */
    public function getEnableRules()
    {
        return $this->container['enableRules'] ?? null;
    }

    /**
     * Sets enableRules.
     *
     * @param null|bool $enableRules whether to enable rules
     *
     * @return self
     */
    public function setEnableRules($enableRules)
    {
        $this->container['enableRules'] = $enableRules;

        return $this;
    }

    /**
     * Gets enablePersonalization.
     *
     * @return null|bool
     */
    public function getEnablePersonalization()
    {
        return $this->container['enablePersonalization'] ?? null;
    }

    /**
     * Sets enablePersonalization.
     *
     * @param null|bool $enablePersonalization whether to enable Personalization
     *
     * @return self
     */
    public function setEnablePersonalization($enablePersonalization)
    {
        $this->container['enablePersonalization'] = $enablePersonalization;

        return $this;
    }

    /**
     * Gets queryType.
     *
     * @return null|QueryType
     */
    public function getQueryType()
    {
        return $this->container['queryType'] ?? null;
    }

    /**
     * Sets queryType.
     *
     * @param null|QueryType $queryType queryType
     *
     * @return self
     */
    public function setQueryType($queryType)
    {
        $this->container['queryType'] = $queryType;

        return $this;
    }

    /**
     * Gets removeWordsIfNoResults.
     *
     * @return null|RemoveWordsIfNoResults
     */
    public function getRemoveWordsIfNoResults()
    {
        return $this->container['removeWordsIfNoResults'] ?? null;
    }

    /**
     * Sets removeWordsIfNoResults.
     *
     * @param null|RemoveWordsIfNoResults $removeWordsIfNoResults removeWordsIfNoResults
     *
     * @return self
     */
    public function setRemoveWordsIfNoResults($removeWordsIfNoResults)
    {
        $this->container['removeWordsIfNoResults'] = $removeWordsIfNoResults;

        return $this;
    }

    /**
     * Gets mode.
     *
     * @return null|Mode
     */
    public function getMode()
    {
        return $this->container['mode'] ?? null;
    }

    /**
     * Sets mode.
     *
     * @param null|Mode $mode mode
     *
     * @return self
     */
    public function setMode($mode)
    {
        $this->container['mode'] = $mode;

        return $this;
    }

    /**
     * Gets semanticSearch.
     *
     * @return null|SemanticSearch
     */
    public function getSemanticSearch()
    {
        return $this->container['semanticSearch'] ?? null;
    }

    /**
     * Sets semanticSearch.
     *
     * @param null|SemanticSearch $semanticSearch semanticSearch
     *
     * @return self
     */
    public function setSemanticSearch($semanticSearch)
    {
        $this->container['semanticSearch'] = $semanticSearch;

        return $this;
    }

    /**
     * Gets advancedSyntax.
     *
     * @return null|bool
     */
    public function getAdvancedSyntax()
    {
        return $this->container['advancedSyntax'] ?? null;
    }

    /**
     * Sets advancedSyntax.
     *
     * @param null|bool $advancedSyntax Whether to support phrase matching and excluding words from search queries.  Use the `advancedSyntaxFeatures` parameter to control which feature is supported.
     *
     * @return self
     */
    public function setAdvancedSyntax($advancedSyntax)
    {
        $this->container['advancedSyntax'] = $advancedSyntax;

        return $this;
    }

    /**
     * Gets optionalWords.
     *
     * @return null|string[]
     */
    public function getOptionalWords()
    {
        return $this->container['optionalWords'] ?? null;
    }

    /**
     * Sets optionalWords.
     *
     * @param null|string[] $optionalWords Words that should be considered optional when found in the query.  By default, records must match all words in the search query to be included in the search results. Adding optional words can help to increase the number of search results by running an additional search query that doesn't include the optional words. For example, if the search query is \"action video\" and \"video\" is an optional word, the search engine runs two queries. One for \"action video\" and one for \"action\". Records that match all words are ranked higher.  For a search query with 4 or more words **and** all its words are optional, the number of matched words required for a record to be included in the search results increases for every 1,000 records:  - If `optionalWords` has less than 10 words, the required number of matched words increases by 1:   results 1 to 1,000 require 1 matched word, results 1,001 to 2000 need 2 matched words. - If `optionalWords` has 10 or more words, the number of required matched words increases by the number of optional words dividied by 5 (rounded down).   For example, with 18 optional words: results 1 to 1,000 require 1 matched word, results 1,001 to 2000 need 4 matched words.  For more information, see [Optional words](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/empty-or-insufficient-results/#creating-a-list-of-optional-words).
     *
     * @return self
     */
    public function setOptionalWords($optionalWords)
    {
        $this->container['optionalWords'] = $optionalWords;

        return $this;
    }

    /**
     * Gets disableExactOnAttributes.
     *
     * @return null|string[]
     */
    public function getDisableExactOnAttributes()
    {
        return $this->container['disableExactOnAttributes'] ?? null;
    }

    /**
     * Sets disableExactOnAttributes.
     *
     * @param null|string[] $disableExactOnAttributes Searchable attributes for which you want to [turn off the Exact ranking criterion](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/override-search-engine-defaults/in-depth/adjust-exact-settings/#turn-off-exact-for-some-attributes). Attribute names are case-sensitive.  This can be useful for attributes with long values, where the likelyhood of an exact match is high, such as product descriptions. Turning off the Exact ranking criterion for these attributes favors exact matching on other attributes. This reduces the impact of individual attributes with a lot of content on ranking.
     *
     * @return self
     */
    public function setDisableExactOnAttributes($disableExactOnAttributes)
    {
        $this->container['disableExactOnAttributes'] = $disableExactOnAttributes;

        return $this;
    }

    /**
     * Gets exactOnSingleWordQuery.
     *
     * @return null|ExactOnSingleWordQuery
     */
    public function getExactOnSingleWordQuery()
    {
        return $this->container['exactOnSingleWordQuery'] ?? null;
    }

    /**
     * Sets exactOnSingleWordQuery.
     *
     * @param null|ExactOnSingleWordQuery $exactOnSingleWordQuery exactOnSingleWordQuery
     *
     * @return self
     */
    public function setExactOnSingleWordQuery($exactOnSingleWordQuery)
    {
        $this->container['exactOnSingleWordQuery'] = $exactOnSingleWordQuery;

        return $this;
    }

    /**
     * Gets alternativesAsExact.
     *
     * @return null|\Algolia\AlgoliaSearch\Model\Search\AlternativesAsExact[]
     */
    public function getAlternativesAsExact()
    {
        return $this->container['alternativesAsExact'] ?? null;
    }

    /**
     * Sets alternativesAsExact.
     *
     * @param null|\Algolia\AlgoliaSearch\Model\Search\AlternativesAsExact[] $alternativesAsExact Alternatives of query words that should be considered as exact matches by the Exact ranking criterion.  - `ignorePlurals`.   Plurals and similar declensions added by the `ignorePlurals` setting are considered exact matches.  - `singleWordSynonym`.   Single-word synonyms, such as \"NY/NYC\" are considered exact matches.  - `multiWordsSynonym`.   Multi-word synonyms, such as \"NY/New York\" are considered exact matches.
     *
     * @return self
     */
    public function setAlternativesAsExact($alternativesAsExact)
    {
        $this->container['alternativesAsExact'] = $alternativesAsExact;

        return $this;
    }

    /**
     * Gets advancedSyntaxFeatures.
     *
     * @return null|\Algolia\AlgoliaSearch\Model\Search\AdvancedSyntaxFeatures[]
     */
    public function getAdvancedSyntaxFeatures()
    {
        return $this->container['advancedSyntaxFeatures'] ?? null;
    }

    /**
     * Sets advancedSyntaxFeatures.
     *
     * @param null|\Algolia\AlgoliaSearch\Model\Search\AdvancedSyntaxFeatures[] $advancedSyntaxFeatures Advanced search syntax features you want to support.  - `exactPhrase`.   Phrases in quotes must match exactly.   For example, `sparkly blue \"iPhone case\"` only returns records with the exact string \"iPhone case\".  - `excludeWords`.   Query words prefixed with a `-` must not occur in a record.   For example, `search -engine` matches records that contain \"search\" but not \"engine\".  This setting only has an effect if `advancedSyntax` is true.
     *
     * @return self
     */
    public function setAdvancedSyntaxFeatures($advancedSyntaxFeatures)
    {
        $this->container['advancedSyntaxFeatures'] = $advancedSyntaxFeatures;

        return $this;
    }

    /**
     * Gets distinct.
     *
     * @return null|Distinct
     */
    public function getDistinct()
    {
        return $this->container['distinct'] ?? null;
    }

    /**
     * Sets distinct.
     *
     * @param null|Distinct $distinct distinct
     *
     * @return self
     */
    public function setDistinct($distinct)
    {
        $this->container['distinct'] = $distinct;

        return $this;
    }

    /**
     * Gets replaceSynonymsInHighlight.
     *
     * @return null|bool
     */
    public function getReplaceSynonymsInHighlight()
    {
        return $this->container['replaceSynonymsInHighlight'] ?? null;
    }

    /**
     * Sets replaceSynonymsInHighlight.
     *
     * @param null|bool $replaceSynonymsInHighlight Whether to replace a highlighted word with the matched synonym.  By default, the original words are highlighted even if a synonym matches. For example, with `home` as a synonym for `house` and a search for `home`, records matching either \"home\" or \"house\" are included in the search results, and either \"home\" or \"house\" are highlighted.  With `replaceSynonymsInHighlight` set to `true`, a search for `home` still matches the same records, but all occurences of \"house\" are replaced by \"home\" in the highlighted response.
     *
     * @return self
     */
    public function setReplaceSynonymsInHighlight($replaceSynonymsInHighlight)
    {
        $this->container['replaceSynonymsInHighlight'] = $replaceSynonymsInHighlight;

        return $this;
    }

    /**
     * Gets minProximity.
     *
     * @return null|int
     */
    public function getMinProximity()
    {
        return $this->container['minProximity'] ?? null;
    }

    /**
     * Sets minProximity.
     *
     * @param null|int $minProximity Minimum proximity score for two matching words.  This adjusts the [Proximity ranking criterion](https://www.algolia.com/doc/guides/managing-results/relevance-overview/in-depth/ranking-criteria/#proximity) by equally scoring matches that are farther apart.  For example, if `minProximity` is 2, neighboring matches and matches with one word between them would have the same score.
     *
     * @return self
     */
    public function setMinProximity($minProximity)
    {
        if (!is_null($minProximity) && ($minProximity > 7)) {
            throw new \InvalidArgumentException('invalid value for $minProximity when calling SearchQuery., must be smaller than or equal to 7.');
        }
        if (!is_null($minProximity) && ($minProximity < 1)) {
            throw new \InvalidArgumentException('invalid value for $minProximity when calling SearchQuery., must be bigger than or equal to 1.');
        }

        $this->container['minProximity'] = $minProximity;

        return $this;
    }

    /**
     * Gets responseFields.
     *
     * @return null|string[]
     */
    public function getResponseFields()
    {
        return $this->container['responseFields'] ?? null;
    }

    /**
     * Sets responseFields.
     *
     * @param null|string[] $responseFields Properties to include in the API response of `search` and `browse` requests.  By default, all response properties are included. To reduce the response size, you can select, which attributes should be included.  You can't exclude these properties: `message`, `warning`, `cursor`, `serverUsed`, `indexUsed`, `abTestVariantID`, `parsedQuery`, or any property triggered by the `getRankingInfo` parameter.  Don't exclude properties that you might need in your search UI.
     *
     * @return self
     */
    public function setResponseFields($responseFields)
    {
        $this->container['responseFields'] = $responseFields;

        return $this;
    }

    /**
     * Gets maxFacetHits.
     *
     * @return null|int
     */
    public function getMaxFacetHits()
    {
        return $this->container['maxFacetHits'] ?? null;
    }

    /**
     * Sets maxFacetHits.
     *
     * @param null|int $maxFacetHits Maximum number of facet values to return when [searching for facet values](https://www.algolia.com/doc/guides/managing-results/refine-results/faceting/#search-for-facet-values).
     *
     * @return self
     */
    public function setMaxFacetHits($maxFacetHits)
    {
        if (!is_null($maxFacetHits) && ($maxFacetHits > 100)) {
            throw new \InvalidArgumentException('invalid value for $maxFacetHits when calling SearchQuery., must be smaller than or equal to 100.');
        }

        $this->container['maxFacetHits'] = $maxFacetHits;

        return $this;
    }

    /**
     * Gets maxValuesPerFacet.
     *
     * @return null|int
     */
    public function getMaxValuesPerFacet()
    {
        return $this->container['maxValuesPerFacet'] ?? null;
    }

    /**
     * Sets maxValuesPerFacet.
     *
     * @param null|int $maxValuesPerFacet maximum number of facet values to return for each facet
     *
     * @return self
     */
    public function setMaxValuesPerFacet($maxValuesPerFacet)
    {
        if (!is_null($maxValuesPerFacet) && ($maxValuesPerFacet > 1000)) {
            throw new \InvalidArgumentException('invalid value for $maxValuesPerFacet when calling SearchQuery., must be smaller than or equal to 1000.');
        }

        $this->container['maxValuesPerFacet'] = $maxValuesPerFacet;

        return $this;
    }

    /**
     * Gets sortFacetValuesBy.
     *
     * @return null|string
     */
    public function getSortFacetValuesBy()
    {
        return $this->container['sortFacetValuesBy'] ?? null;
    }

    /**
     * Sets sortFacetValuesBy.
     *
     * @param null|string $sortFacetValuesBy Order in which to retrieve facet values.  - `count`.   Facet values are retrieved by decreasing count.   The count is the number of matching records containing this facet value.  - `alpha`.   Retrieve facet values alphabetically.  This setting doesn't influence how facet values are displayed in your UI (see `renderingContent`). For more information, see [facet value display](https://www.algolia.com/doc/guides/building-search-ui/ui-and-ux-patterns/facet-display/js/).
     *
     * @return self
     */
    public function setSortFacetValuesBy($sortFacetValuesBy)
    {
        $this->container['sortFacetValuesBy'] = $sortFacetValuesBy;

        return $this;
    }

    /**
     * Gets attributeCriteriaComputedByMinProximity.
     *
     * @return null|bool
     */
    public function getAttributeCriteriaComputedByMinProximity()
    {
        return $this->container['attributeCriteriaComputedByMinProximity'] ?? null;
    }

    /**
     * Sets attributeCriteriaComputedByMinProximity.
     *
     * @param null|bool $attributeCriteriaComputedByMinProximity Whether the best matching attribute should be determined by minimum proximity.  This setting only affects ranking if the Attribute ranking criterion comes before Proximity in the `ranking` setting. If true, the best matching attribute is selected based on the minimum proximity of multiple matches. Otherwise, the best matching attribute is determined by the order in the `searchableAttributes` setting.
     *
     * @return self
     */
    public function setAttributeCriteriaComputedByMinProximity($attributeCriteriaComputedByMinProximity)
    {
        $this->container['attributeCriteriaComputedByMinProximity'] = $attributeCriteriaComputedByMinProximity;

        return $this;
    }

    /**
     * Gets renderingContent.
     *
     * @return null|RenderingContent
     */
    public function getRenderingContent()
    {
        return $this->container['renderingContent'] ?? null;
    }

    /**
     * Sets renderingContent.
     *
     * @param null|RenderingContent $renderingContent renderingContent
     *
     * @return self
     */
    public function setRenderingContent($renderingContent)
    {
        $this->container['renderingContent'] = $renderingContent;

        return $this;
    }

    /**
     * Gets enableReRanking.
     *
     * @return null|bool
     */
    public function getEnableReRanking()
    {
        return $this->container['enableReRanking'] ?? null;
    }

    /**
     * Sets enableReRanking.
     *
     * @param null|bool $enableReRanking Whether this search will use [Dynamic Re-Ranking](https://www.algolia.com/doc/guides/algolia-ai/re-ranking/).  This setting only has an effect if you activated Dynamic Re-Ranking for this index in the Algolia dashboard.
     *
     * @return self
     */
    public function setEnableReRanking($enableReRanking)
    {
        $this->container['enableReRanking'] = $enableReRanking;

        return $this;
    }

    /**
     * Gets reRankingApplyFilter.
     *
     * @return null|ReRankingApplyFilter
     */
    public function getReRankingApplyFilter()
    {
        return $this->container['reRankingApplyFilter'] ?? null;
    }

    /**
     * Sets reRankingApplyFilter.
     *
     * @param null|ReRankingApplyFilter $reRankingApplyFilter reRankingApplyFilter
     *
     * @return self
     */
    public function setReRankingApplyFilter($reRankingApplyFilter)
    {
        $this->container['reRankingApplyFilter'] = $reRankingApplyFilter;

        return $this;
    }

    /**
     * Gets indexName.
     *
     * @return string
     */
    public function getIndexName()
    {
        return $this->container['indexName'] ?? null;
    }

    /**
     * Sets indexName.
     *
     * @param string $indexName index name (case-sensitive)
     *
     * @return self
     */
    public function setIndexName($indexName)
    {
        $this->container['indexName'] = $indexName;

        return $this;
    }

    /**
     * Gets type.
     *
     * @return SearchTypeFacet
     */
    public function getType()
    {
        return $this->container['type'] ?? null;
    }

    /**
     * Sets type.
     *
     * @param SearchTypeFacet $type type
     *
     * @return self
     */
    public function setType($type)
    {
        $this->container['type'] = $type;

        return $this;
    }

    /**
     * Gets facet.
     *
     * @return string
     */
    public function getFacet()
    {
        return $this->container['facet'] ?? null;
    }

    /**
     * Sets facet.
     *
     * @param string $facet facet name
     *
     * @return self
     */
    public function setFacet($facet)
    {
        $this->container['facet'] = $facet;

        return $this;
    }

    /**
     * Gets facetQuery.
     *
     * @return null|string
     */
    public function getFacetQuery()
    {
        return $this->container['facetQuery'] ?? null;
    }

    /**
     * Sets facetQuery.
     *
     * @param null|string $facetQuery text to search inside the facet's values
     *
     * @return self
     */
    public function setFacetQuery($facetQuery)
    {
        $this->container['facetQuery'] = $facetQuery;

        return $this;
    }

    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param int $offset Offset
     *
     * @return bool
     */
    public function offsetExists($offset)
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param int $offset Offset
     *
     * @return null|mixed
     */
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param null|int $offset Offset
     * @param mixed    $value  Value to be set
     */
    public function offsetSet($offset, $value)
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param int $offset Offset
     */
    public function offsetUnset($offset)
    {
        unset($this->container[$offset]);
    }
}
