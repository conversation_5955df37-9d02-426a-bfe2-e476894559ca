<?php

// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.

namespace Algolia\AlgoliaSearch\Model\Search;

use Algolia\AlgoliaSearch\Model\AbstractModel;

/**
 * ReplaceAllObjectsResponse Class Doc Comment.
 *
 * @category Class
 */
class ReplaceAllObjectsResponse extends AbstractModel implements ModelInterface, \ArrayAccess, \JsonSerializable
{
    /**
     * Array of property to type mappings. Used for (de)serialization.
     *
     * @var string[]
     */
    protected static $modelTypes = [
        'copyOperationResponse' => '\Algolia\AlgoliaSearch\Model\Search\UpdatedAtResponse',
        'batchResponses' => '\Algolia\AlgoliaSearch\Model\Search\BatchResponse[]',
        'moveOperationResponse' => '\Algolia\AlgoliaSearch\Model\Search\UpdatedAtResponse',
    ];

    /**
     * Array of property to format mappings. Used for (de)serialization.
     *
     * @var string[]
     */
    protected static $modelFormats = [
        'copyOperationResponse' => null,
        'batchResponses' => null,
        'moveOperationResponse' => null,
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name.
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'copyOperationResponse' => 'copyOperationResponse',
        'batchResponses' => 'batchResponses',
        'moveOperationResponse' => 'moveOperationResponse',
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses).
     *
     * @var string[]
     */
    protected static $setters = [
        'copyOperationResponse' => 'setCopyOperationResponse',
        'batchResponses' => 'setBatchResponses',
        'moveOperationResponse' => 'setMoveOperationResponse',
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests).
     *
     * @var string[]
     */
    protected static $getters = [
        'copyOperationResponse' => 'getCopyOperationResponse',
        'batchResponses' => 'getBatchResponses',
        'moveOperationResponse' => 'getMoveOperationResponse',
    ];

    /**
     * Associative array for storing property values.
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor.
     *
     * @param mixed[] $data Associated array of property values
     */
    public function __construct(?array $data = null)
    {
        if (isset($data['copyOperationResponse'])) {
            $this->container['copyOperationResponse'] = $data['copyOperationResponse'];
        }
        if (isset($data['batchResponses'])) {
            $this->container['batchResponses'] = $data['batchResponses'];
        }
        if (isset($data['moveOperationResponse'])) {
            $this->container['moveOperationResponse'] = $data['moveOperationResponse'];
        }
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name.
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of property to type mappings. Used for (de)serialization.
     *
     * @return array
     */
    public static function modelTypes()
    {
        return self::$modelTypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization.
     *
     * @return array
     */
    public static function modelFormats()
    {
        return self::$modelFormats;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses).
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests).
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        if (!isset($this->container['copyOperationResponse']) || null === $this->container['copyOperationResponse']) {
            $invalidProperties[] = "'copyOperationResponse' can't be null";
        }
        if (!isset($this->container['batchResponses']) || null === $this->container['batchResponses']) {
            $invalidProperties[] = "'batchResponses' can't be null";
        }
        if (!isset($this->container['moveOperationResponse']) || null === $this->container['moveOperationResponse']) {
            $invalidProperties[] = "'moveOperationResponse' can't be null";
        }

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed.
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return 0 === count($this->listInvalidProperties());
    }

    /**
     * Gets copyOperationResponse.
     *
     * @return UpdatedAtResponse
     */
    public function getCopyOperationResponse()
    {
        return $this->container['copyOperationResponse'] ?? null;
    }

    /**
     * Sets copyOperationResponse.
     *
     * @param UpdatedAtResponse $copyOperationResponse copyOperationResponse
     *
     * @return self
     */
    public function setCopyOperationResponse($copyOperationResponse)
    {
        $this->container['copyOperationResponse'] = $copyOperationResponse;

        return $this;
    }

    /**
     * Gets batchResponses.
     *
     * @return \Algolia\AlgoliaSearch\Model\Search\BatchResponse[]
     */
    public function getBatchResponses()
    {
        return $this->container['batchResponses'] ?? null;
    }

    /**
     * Sets batchResponses.
     *
     * @param \Algolia\AlgoliaSearch\Model\Search\BatchResponse[] $batchResponses the response of the `batch` request(s)
     *
     * @return self
     */
    public function setBatchResponses($batchResponses)
    {
        $this->container['batchResponses'] = $batchResponses;

        return $this;
    }

    /**
     * Gets moveOperationResponse.
     *
     * @return UpdatedAtResponse
     */
    public function getMoveOperationResponse()
    {
        return $this->container['moveOperationResponse'] ?? null;
    }

    /**
     * Sets moveOperationResponse.
     *
     * @param UpdatedAtResponse $moveOperationResponse moveOperationResponse
     *
     * @return self
     */
    public function setMoveOperationResponse($moveOperationResponse)
    {
        $this->container['moveOperationResponse'] = $moveOperationResponse;

        return $this;
    }

    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param int $offset Offset
     *
     * @return bool
     */
    public function offsetExists($offset)
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param int $offset Offset
     *
     * @return null|mixed
     */
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param null|int $offset Offset
     * @param mixed    $value  Value to be set
     */
    public function offsetSet($offset, $value)
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param int $offset Offset
     */
    public function offsetUnset($offset)
    {
        unset($this->container[$offset]);
    }
}
