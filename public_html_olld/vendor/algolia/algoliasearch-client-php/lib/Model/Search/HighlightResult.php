<?php

// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.

namespace Algolia\AlgoliaSearch\Model\Search;

use Algolia\AlgoliaSearch\Model\AbstractModel;

/**
 * HighlightResult Class Doc Comment.
 *
 * @category Class
 */
class HighlightResult extends AbstractModel implements ModelInterface, \ArrayAccess, \JsonSerializable
{
    /**
     * Array of property to type mappings. Used for (de)serialization.
     *
     * @var string[]
     */
    protected static $modelTypes = [
        'value' => 'string',
        'matchLevel' => '\Algolia\AlgoliaSearch\Model\Search\MatchLevel',
        'matchedWords' => 'string[]',
        'fullyHighlighted' => 'bool',
    ];

    /**
     * Array of property to format mappings. Used for (de)serialization.
     *
     * @var string[]
     */
    protected static $modelFormats = [
        'value' => null,
        'matchLevel' => null,
        'matchedWords' => null,
        'fullyHighlighted' => null,
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name.
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'value' => 'value',
        'matchLevel' => 'matchLevel',
        'matchedWords' => 'matchedWords',
        'fullyHighlighted' => 'fullyHighlighted',
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses).
     *
     * @var string[]
     */
    protected static $setters = [
        'value' => 'setValue',
        'matchLevel' => 'setMatchLevel',
        'matchedWords' => 'setMatchedWords',
        'fullyHighlighted' => 'setFullyHighlighted',
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests).
     *
     * @var string[]
     */
    protected static $getters = [
        'value' => 'getValue',
        'matchLevel' => 'getMatchLevel',
        'matchedWords' => 'getMatchedWords',
        'fullyHighlighted' => 'getFullyHighlighted',
    ];

    /**
     * Associative array for storing property values.
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor.
     *
     * @param mixed[] $data Associated array of property values
     */
    public function __construct(?array $data = null)
    {
        if (isset($data['value'])) {
            $this->container['value'] = $data['value'];
        }
        if (isset($data['matchLevel'])) {
            $this->container['matchLevel'] = $data['matchLevel'];
        }
        if (isset($data['matchedWords'])) {
            $this->container['matchedWords'] = $data['matchedWords'];
        }
        if (isset($data['fullyHighlighted'])) {
            $this->container['fullyHighlighted'] = $data['fullyHighlighted'];
        }
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name.
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of property to type mappings. Used for (de)serialization.
     *
     * @return array
     */
    public static function modelTypes()
    {
        return self::$modelTypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization.
     *
     * @return array
     */
    public static function modelFormats()
    {
        return self::$modelFormats;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses).
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests).
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        if (!isset($this->container['value']) || null === $this->container['value']) {
            $invalidProperties[] = "'value' can't be null";
        }
        if (!isset($this->container['matchLevel']) || null === $this->container['matchLevel']) {
            $invalidProperties[] = "'matchLevel' can't be null";
        }
        if (!isset($this->container['matchedWords']) || null === $this->container['matchedWords']) {
            $invalidProperties[] = "'matchedWords' can't be null";
        }

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed.
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return 0 === count($this->listInvalidProperties());
    }

    /**
     * Gets value.
     *
     * @return string
     */
    public function getValue()
    {
        return $this->container['value'] ?? null;
    }

    /**
     * Sets value.
     *
     * @param string $value highlighted attribute value, including HTML tags
     *
     * @return self
     */
    public function setValue($value)
    {
        $this->container['value'] = $value;

        return $this;
    }

    /**
     * Gets matchLevel.
     *
     * @return MatchLevel
     */
    public function getMatchLevel()
    {
        return $this->container['matchLevel'] ?? null;
    }

    /**
     * Sets matchLevel.
     *
     * @param MatchLevel $matchLevel matchLevel
     *
     * @return self
     */
    public function setMatchLevel($matchLevel)
    {
        $this->container['matchLevel'] = $matchLevel;

        return $this;
    }

    /**
     * Gets matchedWords.
     *
     * @return string[]
     */
    public function getMatchedWords()
    {
        return $this->container['matchedWords'] ?? null;
    }

    /**
     * Sets matchedWords.
     *
     * @param string[] $matchedWords list of matched words from the search query
     *
     * @return self
     */
    public function setMatchedWords($matchedWords)
    {
        $this->container['matchedWords'] = $matchedWords;

        return $this;
    }

    /**
     * Gets fullyHighlighted.
     *
     * @return null|bool
     */
    public function getFullyHighlighted()
    {
        return $this->container['fullyHighlighted'] ?? null;
    }

    /**
     * Sets fullyHighlighted.
     *
     * @param null|bool $fullyHighlighted whether the entire attribute value is highlighted
     *
     * @return self
     */
    public function setFullyHighlighted($fullyHighlighted)
    {
        $this->container['fullyHighlighted'] = $fullyHighlighted;

        return $this;
    }

    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param int $offset Offset
     *
     * @return bool
     */
    public function offsetExists($offset)
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param int $offset Offset
     *
     * @return null|mixed
     */
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param null|int $offset Offset
     * @param mixed    $value  Value to be set
     */
    public function offsetSet($offset, $value)
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param int $offset Offset
     */
    public function offsetUnset($offset)
    {
        unset($this->container[$offset]);
    }
}
