<?php

// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.

namespace Algolia\AlgoliaSearch\Model\Search;

/**
 * DictionaryAction Class Doc Comment.
 *
 * @category Class
 *
 * @description Actions to perform.
 */
class DictionaryAction
{
    /**
     * Possible values of this enum.
     */
    public const ADD_ENTRY = 'addEntry';

    public const DELETE_ENTRY = 'deleteEntry';

    /**
     * Gets allowable values of the enum.
     *
     * @return string[]
     */
    public static function getAllowableEnumValues()
    {
        return [
            self::ADD_ENTRY,
            self::DELETE_ENTRY,
        ];
    }
}
