<?php

// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.

namespace Algolia\AlgoliaSearch\Model\Search;

/**
 * TypoToleranceEnum Class Doc Comment.
 *
 * @category Class
 *
 * @description - &#x60;min&#x60;. Return matches with the lowest number of typos.   For example, if you have matches without typos, only include those.   But if there are no matches without typos (with 1 typo), include matches with 1 typo (2 typos). - &#x60;strict&#x60;. Return matches with the two lowest numbers of typos.   With &#x60;strict&#x60;, the Typo ranking criterion is applied first in the &#x60;ranking&#x60; setting.
 */
class TypoToleranceEnum
{
    /**
     * Possible values of this enum.
     */
    public const MIN = 'min';

    public const STRICT = 'strict';

    /**
     * Gets allowable values of the enum.
     *
     * @return string[]
     */
    public static function getAllowableEnumValues()
    {
        return [
            self::MIN,
            self::STRICT,
        ];
    }
}
