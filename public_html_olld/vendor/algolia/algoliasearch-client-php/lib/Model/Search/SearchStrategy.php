<?php

// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.

namespace Algolia\AlgoliaSearch\Model\Search;

/**
 * SearchStrategy Class Doc Comment.
 *
 * @category Class
 *
 * @description Strategy for multiple search queries:  - &#x60;none&#x60;. Run all queries. - &#x60;stopIfEnoughMatches&#x60;. Run the queries one by one, stopping as soon as a query matches at least the &#x60;hitsPerPage&#x60; number of results.
 */
class SearchStrategy
{
    /**
     * Possible values of this enum.
     */
    public const NONE = 'none';

    public const STOP_IF_ENOUGH_MATCHES = 'stopIfEnoughMatches';

    /**
     * Gets allowable values of the enum.
     *
     * @return string[]
     */
    public static function getAllowableEnumValues()
    {
        return [
            self::NONE,
            self::STOP_IF_ENOUGH_MATCHES,
        ];
    }
}
