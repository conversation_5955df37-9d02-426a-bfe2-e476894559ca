<?php

// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.

namespace Algolia\AlgoliaSearch\Model\Search;

use Algolia\AlgoliaSearch\Model\AbstractModel;

/**
 * IndexSettings Class Doc Comment.
 *
 * @category Class
 *
 * @description Index settings.
 */
class IndexSettings extends AbstractModel implements ModelInterface, \ArrayAccess, \JsonSerializable
{
    /**
     * Array of property to type mappings. Used for (de)serialization.
     *
     * @var string[]
     */
    protected static $modelTypes = [
        'attributesForFaceting' => 'string[]',
        'replicas' => 'string[]',
        'paginationLimitedTo' => 'int',
        'unretrievableAttributes' => 'string[]',
        'disableTypoToleranceOnWords' => 'string[]',
        'attributesToTransliterate' => 'string[]',
        'camelCaseAttributes' => 'string[]',
        'decompoundedAttributes' => 'object',
        'indexLanguages' => '\Algolia\AlgoliaSearch\Model\Search\SupportedLanguage[]',
        'disablePrefixOnAttributes' => 'string[]',
        'allowCompressionOfIntegerArray' => 'bool',
        'numericAttributesForFiltering' => 'string[]',
        'separatorsToIndex' => 'string',
        'searchableAttributes' => 'string[]',
        'userData' => 'object',
        'customNormalization' => 'array<string,array<string,string>>',
        'attributeForDistinct' => 'string',
        'attributesToRetrieve' => 'string[]',
        'ranking' => 'string[]',
        'customRanking' => 'string[]',
        'relevancyStrictness' => 'int',
        'attributesToHighlight' => 'string[]',
        'attributesToSnippet' => 'string[]',
        'highlightPreTag' => 'string',
        'highlightPostTag' => 'string',
        'snippetEllipsisText' => 'string',
        'restrictHighlightAndSnippetArrays' => 'bool',
        'hitsPerPage' => 'int',
        'minWordSizefor1Typo' => 'int',
        'minWordSizefor2Typos' => 'int',
        'typoTolerance' => '\Algolia\AlgoliaSearch\Model\Search\TypoTolerance',
        'allowTyposOnNumericTokens' => 'bool',
        'disableTypoToleranceOnAttributes' => 'string[]',
        'ignorePlurals' => '\Algolia\AlgoliaSearch\Model\Search\IgnorePlurals',
        'removeStopWords' => '\Algolia\AlgoliaSearch\Model\Search\RemoveStopWords',
        'keepDiacriticsOnCharacters' => 'string',
        'queryLanguages' => '\Algolia\AlgoliaSearch\Model\Search\SupportedLanguage[]',
        'decompoundQuery' => 'bool',
        'enableRules' => 'bool',
        'enablePersonalization' => 'bool',
        'queryType' => '\Algolia\AlgoliaSearch\Model\Search\QueryType',
        'removeWordsIfNoResults' => '\Algolia\AlgoliaSearch\Model\Search\RemoveWordsIfNoResults',
        'mode' => '\Algolia\AlgoliaSearch\Model\Search\Mode',
        'semanticSearch' => '\Algolia\AlgoliaSearch\Model\Search\SemanticSearch',
        'advancedSyntax' => 'bool',
        'optionalWords' => 'string[]',
        'disableExactOnAttributes' => 'string[]',
        'exactOnSingleWordQuery' => '\Algolia\AlgoliaSearch\Model\Search\ExactOnSingleWordQuery',
        'alternativesAsExact' => '\Algolia\AlgoliaSearch\Model\Search\AlternativesAsExact[]',
        'advancedSyntaxFeatures' => '\Algolia\AlgoliaSearch\Model\Search\AdvancedSyntaxFeatures[]',
        'distinct' => '\Algolia\AlgoliaSearch\Model\Search\Distinct',
        'replaceSynonymsInHighlight' => 'bool',
        'minProximity' => 'int',
        'responseFields' => 'string[]',
        'maxFacetHits' => 'int',
        'maxValuesPerFacet' => 'int',
        'sortFacetValuesBy' => 'string',
        'attributeCriteriaComputedByMinProximity' => 'bool',
        'renderingContent' => '\Algolia\AlgoliaSearch\Model\Search\RenderingContent',
        'enableReRanking' => 'bool',
        'reRankingApplyFilter' => '\Algolia\AlgoliaSearch\Model\Search\ReRankingApplyFilter',
    ];

    /**
     * Array of property to format mappings. Used for (de)serialization.
     *
     * @var string[]
     */
    protected static $modelFormats = [
        'attributesForFaceting' => null,
        'replicas' => null,
        'paginationLimitedTo' => null,
        'unretrievableAttributes' => null,
        'disableTypoToleranceOnWords' => null,
        'attributesToTransliterate' => null,
        'camelCaseAttributes' => null,
        'decompoundedAttributes' => null,
        'indexLanguages' => null,
        'disablePrefixOnAttributes' => null,
        'allowCompressionOfIntegerArray' => null,
        'numericAttributesForFiltering' => null,
        'separatorsToIndex' => null,
        'searchableAttributes' => null,
        'userData' => null,
        'customNormalization' => null,
        'attributeForDistinct' => null,
        'attributesToRetrieve' => null,
        'ranking' => null,
        'customRanking' => null,
        'relevancyStrictness' => null,
        'attributesToHighlight' => null,
        'attributesToSnippet' => null,
        'highlightPreTag' => null,
        'highlightPostTag' => null,
        'snippetEllipsisText' => null,
        'restrictHighlightAndSnippetArrays' => null,
        'hitsPerPage' => null,
        'minWordSizefor1Typo' => null,
        'minWordSizefor2Typos' => null,
        'typoTolerance' => null,
        'allowTyposOnNumericTokens' => null,
        'disableTypoToleranceOnAttributes' => null,
        'ignorePlurals' => null,
        'removeStopWords' => null,
        'keepDiacriticsOnCharacters' => null,
        'queryLanguages' => null,
        'decompoundQuery' => null,
        'enableRules' => null,
        'enablePersonalization' => null,
        'queryType' => null,
        'removeWordsIfNoResults' => null,
        'mode' => null,
        'semanticSearch' => null,
        'advancedSyntax' => null,
        'optionalWords' => null,
        'disableExactOnAttributes' => null,
        'exactOnSingleWordQuery' => null,
        'alternativesAsExact' => null,
        'advancedSyntaxFeatures' => null,
        'distinct' => null,
        'replaceSynonymsInHighlight' => null,
        'minProximity' => null,
        'responseFields' => null,
        'maxFacetHits' => null,
        'maxValuesPerFacet' => null,
        'sortFacetValuesBy' => null,
        'attributeCriteriaComputedByMinProximity' => null,
        'renderingContent' => null,
        'enableReRanking' => null,
        'reRankingApplyFilter' => null,
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name.
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'attributesForFaceting' => 'attributesForFaceting',
        'replicas' => 'replicas',
        'paginationLimitedTo' => 'paginationLimitedTo',
        'unretrievableAttributes' => 'unretrievableAttributes',
        'disableTypoToleranceOnWords' => 'disableTypoToleranceOnWords',
        'attributesToTransliterate' => 'attributesToTransliterate',
        'camelCaseAttributes' => 'camelCaseAttributes',
        'decompoundedAttributes' => 'decompoundedAttributes',
        'indexLanguages' => 'indexLanguages',
        'disablePrefixOnAttributes' => 'disablePrefixOnAttributes',
        'allowCompressionOfIntegerArray' => 'allowCompressionOfIntegerArray',
        'numericAttributesForFiltering' => 'numericAttributesForFiltering',
        'separatorsToIndex' => 'separatorsToIndex',
        'searchableAttributes' => 'searchableAttributes',
        'userData' => 'userData',
        'customNormalization' => 'customNormalization',
        'attributeForDistinct' => 'attributeForDistinct',
        'attributesToRetrieve' => 'attributesToRetrieve',
        'ranking' => 'ranking',
        'customRanking' => 'customRanking',
        'relevancyStrictness' => 'relevancyStrictness',
        'attributesToHighlight' => 'attributesToHighlight',
        'attributesToSnippet' => 'attributesToSnippet',
        'highlightPreTag' => 'highlightPreTag',
        'highlightPostTag' => 'highlightPostTag',
        'snippetEllipsisText' => 'snippetEllipsisText',
        'restrictHighlightAndSnippetArrays' => 'restrictHighlightAndSnippetArrays',
        'hitsPerPage' => 'hitsPerPage',
        'minWordSizefor1Typo' => 'minWordSizefor1Typo',
        'minWordSizefor2Typos' => 'minWordSizefor2Typos',
        'typoTolerance' => 'typoTolerance',
        'allowTyposOnNumericTokens' => 'allowTyposOnNumericTokens',
        'disableTypoToleranceOnAttributes' => 'disableTypoToleranceOnAttributes',
        'ignorePlurals' => 'ignorePlurals',
        'removeStopWords' => 'removeStopWords',
        'keepDiacriticsOnCharacters' => 'keepDiacriticsOnCharacters',
        'queryLanguages' => 'queryLanguages',
        'decompoundQuery' => 'decompoundQuery',
        'enableRules' => 'enableRules',
        'enablePersonalization' => 'enablePersonalization',
        'queryType' => 'queryType',
        'removeWordsIfNoResults' => 'removeWordsIfNoResults',
        'mode' => 'mode',
        'semanticSearch' => 'semanticSearch',
        'advancedSyntax' => 'advancedSyntax',
        'optionalWords' => 'optionalWords',
        'disableExactOnAttributes' => 'disableExactOnAttributes',
        'exactOnSingleWordQuery' => 'exactOnSingleWordQuery',
        'alternativesAsExact' => 'alternativesAsExact',
        'advancedSyntaxFeatures' => 'advancedSyntaxFeatures',
        'distinct' => 'distinct',
        'replaceSynonymsInHighlight' => 'replaceSynonymsInHighlight',
        'minProximity' => 'minProximity',
        'responseFields' => 'responseFields',
        'maxFacetHits' => 'maxFacetHits',
        'maxValuesPerFacet' => 'maxValuesPerFacet',
        'sortFacetValuesBy' => 'sortFacetValuesBy',
        'attributeCriteriaComputedByMinProximity' => 'attributeCriteriaComputedByMinProximity',
        'renderingContent' => 'renderingContent',
        'enableReRanking' => 'enableReRanking',
        'reRankingApplyFilter' => 'reRankingApplyFilter',
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses).
     *
     * @var string[]
     */
    protected static $setters = [
        'attributesForFaceting' => 'setAttributesForFaceting',
        'replicas' => 'setReplicas',
        'paginationLimitedTo' => 'setPaginationLimitedTo',
        'unretrievableAttributes' => 'setUnretrievableAttributes',
        'disableTypoToleranceOnWords' => 'setDisableTypoToleranceOnWords',
        'attributesToTransliterate' => 'setAttributesToTransliterate',
        'camelCaseAttributes' => 'setCamelCaseAttributes',
        'decompoundedAttributes' => 'setDecompoundedAttributes',
        'indexLanguages' => 'setIndexLanguages',
        'disablePrefixOnAttributes' => 'setDisablePrefixOnAttributes',
        'allowCompressionOfIntegerArray' => 'setAllowCompressionOfIntegerArray',
        'numericAttributesForFiltering' => 'setNumericAttributesForFiltering',
        'separatorsToIndex' => 'setSeparatorsToIndex',
        'searchableAttributes' => 'setSearchableAttributes',
        'userData' => 'setUserData',
        'customNormalization' => 'setCustomNormalization',
        'attributeForDistinct' => 'setAttributeForDistinct',
        'attributesToRetrieve' => 'setAttributesToRetrieve',
        'ranking' => 'setRanking',
        'customRanking' => 'setCustomRanking',
        'relevancyStrictness' => 'setRelevancyStrictness',
        'attributesToHighlight' => 'setAttributesToHighlight',
        'attributesToSnippet' => 'setAttributesToSnippet',
        'highlightPreTag' => 'setHighlightPreTag',
        'highlightPostTag' => 'setHighlightPostTag',
        'snippetEllipsisText' => 'setSnippetEllipsisText',
        'restrictHighlightAndSnippetArrays' => 'setRestrictHighlightAndSnippetArrays',
        'hitsPerPage' => 'setHitsPerPage',
        'minWordSizefor1Typo' => 'setMinWordSizefor1Typo',
        'minWordSizefor2Typos' => 'setMinWordSizefor2Typos',
        'typoTolerance' => 'setTypoTolerance',
        'allowTyposOnNumericTokens' => 'setAllowTyposOnNumericTokens',
        'disableTypoToleranceOnAttributes' => 'setDisableTypoToleranceOnAttributes',
        'ignorePlurals' => 'setIgnorePlurals',
        'removeStopWords' => 'setRemoveStopWords',
        'keepDiacriticsOnCharacters' => 'setKeepDiacriticsOnCharacters',
        'queryLanguages' => 'setQueryLanguages',
        'decompoundQuery' => 'setDecompoundQuery',
        'enableRules' => 'setEnableRules',
        'enablePersonalization' => 'setEnablePersonalization',
        'queryType' => 'setQueryType',
        'removeWordsIfNoResults' => 'setRemoveWordsIfNoResults',
        'mode' => 'setMode',
        'semanticSearch' => 'setSemanticSearch',
        'advancedSyntax' => 'setAdvancedSyntax',
        'optionalWords' => 'setOptionalWords',
        'disableExactOnAttributes' => 'setDisableExactOnAttributes',
        'exactOnSingleWordQuery' => 'setExactOnSingleWordQuery',
        'alternativesAsExact' => 'setAlternativesAsExact',
        'advancedSyntaxFeatures' => 'setAdvancedSyntaxFeatures',
        'distinct' => 'setDistinct',
        'replaceSynonymsInHighlight' => 'setReplaceSynonymsInHighlight',
        'minProximity' => 'setMinProximity',
        'responseFields' => 'setResponseFields',
        'maxFacetHits' => 'setMaxFacetHits',
        'maxValuesPerFacet' => 'setMaxValuesPerFacet',
        'sortFacetValuesBy' => 'setSortFacetValuesBy',
        'attributeCriteriaComputedByMinProximity' => 'setAttributeCriteriaComputedByMinProximity',
        'renderingContent' => 'setRenderingContent',
        'enableReRanking' => 'setEnableReRanking',
        'reRankingApplyFilter' => 'setReRankingApplyFilter',
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests).
     *
     * @var string[]
     */
    protected static $getters = [
        'attributesForFaceting' => 'getAttributesForFaceting',
        'replicas' => 'getReplicas',
        'paginationLimitedTo' => 'getPaginationLimitedTo',
        'unretrievableAttributes' => 'getUnretrievableAttributes',
        'disableTypoToleranceOnWords' => 'getDisableTypoToleranceOnWords',
        'attributesToTransliterate' => 'getAttributesToTransliterate',
        'camelCaseAttributes' => 'getCamelCaseAttributes',
        'decompoundedAttributes' => 'getDecompoundedAttributes',
        'indexLanguages' => 'getIndexLanguages',
        'disablePrefixOnAttributes' => 'getDisablePrefixOnAttributes',
        'allowCompressionOfIntegerArray' => 'getAllowCompressionOfIntegerArray',
        'numericAttributesForFiltering' => 'getNumericAttributesForFiltering',
        'separatorsToIndex' => 'getSeparatorsToIndex',
        'searchableAttributes' => 'getSearchableAttributes',
        'userData' => 'getUserData',
        'customNormalization' => 'getCustomNormalization',
        'attributeForDistinct' => 'getAttributeForDistinct',
        'attributesToRetrieve' => 'getAttributesToRetrieve',
        'ranking' => 'getRanking',
        'customRanking' => 'getCustomRanking',
        'relevancyStrictness' => 'getRelevancyStrictness',
        'attributesToHighlight' => 'getAttributesToHighlight',
        'attributesToSnippet' => 'getAttributesToSnippet',
        'highlightPreTag' => 'getHighlightPreTag',
        'highlightPostTag' => 'getHighlightPostTag',
        'snippetEllipsisText' => 'getSnippetEllipsisText',
        'restrictHighlightAndSnippetArrays' => 'getRestrictHighlightAndSnippetArrays',
        'hitsPerPage' => 'getHitsPerPage',
        'minWordSizefor1Typo' => 'getMinWordSizefor1Typo',
        'minWordSizefor2Typos' => 'getMinWordSizefor2Typos',
        'typoTolerance' => 'getTypoTolerance',
        'allowTyposOnNumericTokens' => 'getAllowTyposOnNumericTokens',
        'disableTypoToleranceOnAttributes' => 'getDisableTypoToleranceOnAttributes',
        'ignorePlurals' => 'getIgnorePlurals',
        'removeStopWords' => 'getRemoveStopWords',
        'keepDiacriticsOnCharacters' => 'getKeepDiacriticsOnCharacters',
        'queryLanguages' => 'getQueryLanguages',
        'decompoundQuery' => 'getDecompoundQuery',
        'enableRules' => 'getEnableRules',
        'enablePersonalization' => 'getEnablePersonalization',
        'queryType' => 'getQueryType',
        'removeWordsIfNoResults' => 'getRemoveWordsIfNoResults',
        'mode' => 'getMode',
        'semanticSearch' => 'getSemanticSearch',
        'advancedSyntax' => 'getAdvancedSyntax',
        'optionalWords' => 'getOptionalWords',
        'disableExactOnAttributes' => 'getDisableExactOnAttributes',
        'exactOnSingleWordQuery' => 'getExactOnSingleWordQuery',
        'alternativesAsExact' => 'getAlternativesAsExact',
        'advancedSyntaxFeatures' => 'getAdvancedSyntaxFeatures',
        'distinct' => 'getDistinct',
        'replaceSynonymsInHighlight' => 'getReplaceSynonymsInHighlight',
        'minProximity' => 'getMinProximity',
        'responseFields' => 'getResponseFields',
        'maxFacetHits' => 'getMaxFacetHits',
        'maxValuesPerFacet' => 'getMaxValuesPerFacet',
        'sortFacetValuesBy' => 'getSortFacetValuesBy',
        'attributeCriteriaComputedByMinProximity' => 'getAttributeCriteriaComputedByMinProximity',
        'renderingContent' => 'getRenderingContent',
        'enableReRanking' => 'getEnableReRanking',
        'reRankingApplyFilter' => 'getReRankingApplyFilter',
    ];

    /**
     * Associative array for storing property values.
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor.
     *
     * @param mixed[] $data Associated array of property values
     */
    public function __construct(?array $data = null)
    {
        if (isset($data['attributesForFaceting'])) {
            $this->container['attributesForFaceting'] = $data['attributesForFaceting'];
        }
        if (isset($data['replicas'])) {
            $this->container['replicas'] = $data['replicas'];
        }
        if (isset($data['paginationLimitedTo'])) {
            $this->container['paginationLimitedTo'] = $data['paginationLimitedTo'];
        }
        if (isset($data['unretrievableAttributes'])) {
            $this->container['unretrievableAttributes'] = $data['unretrievableAttributes'];
        }
        if (isset($data['disableTypoToleranceOnWords'])) {
            $this->container['disableTypoToleranceOnWords'] = $data['disableTypoToleranceOnWords'];
        }
        if (isset($data['attributesToTransliterate'])) {
            $this->container['attributesToTransliterate'] = $data['attributesToTransliterate'];
        }
        if (isset($data['camelCaseAttributes'])) {
            $this->container['camelCaseAttributes'] = $data['camelCaseAttributes'];
        }
        if (isset($data['decompoundedAttributes'])) {
            $this->container['decompoundedAttributes'] = $data['decompoundedAttributes'];
        }
        if (isset($data['indexLanguages'])) {
            $this->container['indexLanguages'] = $data['indexLanguages'];
        }
        if (isset($data['disablePrefixOnAttributes'])) {
            $this->container['disablePrefixOnAttributes'] = $data['disablePrefixOnAttributes'];
        }
        if (isset($data['allowCompressionOfIntegerArray'])) {
            $this->container['allowCompressionOfIntegerArray'] = $data['allowCompressionOfIntegerArray'];
        }
        if (isset($data['numericAttributesForFiltering'])) {
            $this->container['numericAttributesForFiltering'] = $data['numericAttributesForFiltering'];
        }
        if (isset($data['separatorsToIndex'])) {
            $this->container['separatorsToIndex'] = $data['separatorsToIndex'];
        }
        if (isset($data['searchableAttributes'])) {
            $this->container['searchableAttributes'] = $data['searchableAttributes'];
        }
        if (isset($data['userData'])) {
            $this->container['userData'] = $data['userData'];
        }
        if (isset($data['customNormalization'])) {
            $this->container['customNormalization'] = $data['customNormalization'];
        }
        if (isset($data['attributeForDistinct'])) {
            $this->container['attributeForDistinct'] = $data['attributeForDistinct'];
        }
        if (isset($data['attributesToRetrieve'])) {
            $this->container['attributesToRetrieve'] = $data['attributesToRetrieve'];
        }
        if (isset($data['ranking'])) {
            $this->container['ranking'] = $data['ranking'];
        }
        if (isset($data['customRanking'])) {
            $this->container['customRanking'] = $data['customRanking'];
        }
        if (isset($data['relevancyStrictness'])) {
            $this->container['relevancyStrictness'] = $data['relevancyStrictness'];
        }
        if (isset($data['attributesToHighlight'])) {
            $this->container['attributesToHighlight'] = $data['attributesToHighlight'];
        }
        if (isset($data['attributesToSnippet'])) {
            $this->container['attributesToSnippet'] = $data['attributesToSnippet'];
        }
        if (isset($data['highlightPreTag'])) {
            $this->container['highlightPreTag'] = $data['highlightPreTag'];
        }
        if (isset($data['highlightPostTag'])) {
            $this->container['highlightPostTag'] = $data['highlightPostTag'];
        }
        if (isset($data['snippetEllipsisText'])) {
            $this->container['snippetEllipsisText'] = $data['snippetEllipsisText'];
        }
        if (isset($data['restrictHighlightAndSnippetArrays'])) {
            $this->container['restrictHighlightAndSnippetArrays'] = $data['restrictHighlightAndSnippetArrays'];
        }
        if (isset($data['hitsPerPage'])) {
            $this->container['hitsPerPage'] = $data['hitsPerPage'];
        }
        if (isset($data['minWordSizefor1Typo'])) {
            $this->container['minWordSizefor1Typo'] = $data['minWordSizefor1Typo'];
        }
        if (isset($data['minWordSizefor2Typos'])) {
            $this->container['minWordSizefor2Typos'] = $data['minWordSizefor2Typos'];
        }
        if (isset($data['typoTolerance'])) {
            $this->container['typoTolerance'] = $data['typoTolerance'];
        }
        if (isset($data['allowTyposOnNumericTokens'])) {
            $this->container['allowTyposOnNumericTokens'] = $data['allowTyposOnNumericTokens'];
        }
        if (isset($data['disableTypoToleranceOnAttributes'])) {
            $this->container['disableTypoToleranceOnAttributes'] = $data['disableTypoToleranceOnAttributes'];
        }
        if (isset($data['ignorePlurals'])) {
            $this->container['ignorePlurals'] = $data['ignorePlurals'];
        }
        if (isset($data['removeStopWords'])) {
            $this->container['removeStopWords'] = $data['removeStopWords'];
        }
        if (isset($data['keepDiacriticsOnCharacters'])) {
            $this->container['keepDiacriticsOnCharacters'] = $data['keepDiacriticsOnCharacters'];
        }
        if (isset($data['queryLanguages'])) {
            $this->container['queryLanguages'] = $data['queryLanguages'];
        }
        if (isset($data['decompoundQuery'])) {
            $this->container['decompoundQuery'] = $data['decompoundQuery'];
        }
        if (isset($data['enableRules'])) {
            $this->container['enableRules'] = $data['enableRules'];
        }
        if (isset($data['enablePersonalization'])) {
            $this->container['enablePersonalization'] = $data['enablePersonalization'];
        }
        if (isset($data['queryType'])) {
            $this->container['queryType'] = $data['queryType'];
        }
        if (isset($data['removeWordsIfNoResults'])) {
            $this->container['removeWordsIfNoResults'] = $data['removeWordsIfNoResults'];
        }
        if (isset($data['mode'])) {
            $this->container['mode'] = $data['mode'];
        }
        if (isset($data['semanticSearch'])) {
            $this->container['semanticSearch'] = $data['semanticSearch'];
        }
        if (isset($data['advancedSyntax'])) {
            $this->container['advancedSyntax'] = $data['advancedSyntax'];
        }
        if (isset($data['optionalWords'])) {
            $this->container['optionalWords'] = $data['optionalWords'];
        }
        if (isset($data['disableExactOnAttributes'])) {
            $this->container['disableExactOnAttributes'] = $data['disableExactOnAttributes'];
        }
        if (isset($data['exactOnSingleWordQuery'])) {
            $this->container['exactOnSingleWordQuery'] = $data['exactOnSingleWordQuery'];
        }
        if (isset($data['alternativesAsExact'])) {
            $this->container['alternativesAsExact'] = $data['alternativesAsExact'];
        }
        if (isset($data['advancedSyntaxFeatures'])) {
            $this->container['advancedSyntaxFeatures'] = $data['advancedSyntaxFeatures'];
        }
        if (isset($data['distinct'])) {
            $this->container['distinct'] = $data['distinct'];
        }
        if (isset($data['replaceSynonymsInHighlight'])) {
            $this->container['replaceSynonymsInHighlight'] = $data['replaceSynonymsInHighlight'];
        }
        if (isset($data['minProximity'])) {
            $this->container['minProximity'] = $data['minProximity'];
        }
        if (isset($data['responseFields'])) {
            $this->container['responseFields'] = $data['responseFields'];
        }
        if (isset($data['maxFacetHits'])) {
            $this->container['maxFacetHits'] = $data['maxFacetHits'];
        }
        if (isset($data['maxValuesPerFacet'])) {
            $this->container['maxValuesPerFacet'] = $data['maxValuesPerFacet'];
        }
        if (isset($data['sortFacetValuesBy'])) {
            $this->container['sortFacetValuesBy'] = $data['sortFacetValuesBy'];
        }
        if (isset($data['attributeCriteriaComputedByMinProximity'])) {
            $this->container['attributeCriteriaComputedByMinProximity'] = $data['attributeCriteriaComputedByMinProximity'];
        }
        if (isset($data['renderingContent'])) {
            $this->container['renderingContent'] = $data['renderingContent'];
        }
        if (isset($data['enableReRanking'])) {
            $this->container['enableReRanking'] = $data['enableReRanking'];
        }
        if (isset($data['reRankingApplyFilter'])) {
            $this->container['reRankingApplyFilter'] = $data['reRankingApplyFilter'];
        }
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name.
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of property to type mappings. Used for (de)serialization.
     *
     * @return array
     */
    public static function modelTypes()
    {
        return self::$modelTypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization.
     *
     * @return array
     */
    public static function modelFormats()
    {
        return self::$modelFormats;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses).
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests).
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        if (isset($this->container['paginationLimitedTo']) && ($this->container['paginationLimitedTo'] > 20000)) {
            $invalidProperties[] = "invalid value for 'paginationLimitedTo', must be smaller than or equal to 20000.";
        }

        if (isset($this->container['hitsPerPage']) && ($this->container['hitsPerPage'] > 1000)) {
            $invalidProperties[] = "invalid value for 'hitsPerPage', must be smaller than or equal to 1000.";
        }

        if (isset($this->container['hitsPerPage']) && ($this->container['hitsPerPage'] < 1)) {
            $invalidProperties[] = "invalid value for 'hitsPerPage', must be bigger than or equal to 1.";
        }

        if (isset($this->container['minProximity']) && ($this->container['minProximity'] > 7)) {
            $invalidProperties[] = "invalid value for 'minProximity', must be smaller than or equal to 7.";
        }

        if (isset($this->container['minProximity']) && ($this->container['minProximity'] < 1)) {
            $invalidProperties[] = "invalid value for 'minProximity', must be bigger than or equal to 1.";
        }

        if (isset($this->container['maxFacetHits']) && ($this->container['maxFacetHits'] > 100)) {
            $invalidProperties[] = "invalid value for 'maxFacetHits', must be smaller than or equal to 100.";
        }

        if (isset($this->container['maxValuesPerFacet']) && ($this->container['maxValuesPerFacet'] > 1000)) {
            $invalidProperties[] = "invalid value for 'maxValuesPerFacet', must be smaller than or equal to 1000.";
        }

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed.
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return 0 === count($this->listInvalidProperties());
    }

    /**
     * Gets attributesForFaceting.
     *
     * @return null|string[]
     */
    public function getAttributesForFaceting()
    {
        return $this->container['attributesForFaceting'] ?? null;
    }

    /**
     * Sets attributesForFaceting.
     *
     * @param null|string[] $attributesForFaceting Attributes used for [faceting](https://www.algolia.com/doc/guides/managing-results/refine-results/faceting/).  Facets are attributes that let you categorize search results. They can be used for filtering search results. By default, no attribute is used for faceting. Attribute names are case-sensitive.  **Modifiers**  - `filterOnly(\"ATTRIBUTE\")`.   Allows using this attribute as a filter, but doesn't evalue the facet values.  - `searchable(\"ATTRIBUTE\")`.   Allows searching for facet values.  - `afterDistinct(\"ATTRIBUTE\")`.   Evaluates the facet count _after_ deduplication with `distinct`.   This ensures accurate facet counts.   You can apply this modifier to searchable facets: `afterDistinct(searchable(ATTRIBUTE))`.
     *
     * @return self
     */
    public function setAttributesForFaceting($attributesForFaceting)
    {
        $this->container['attributesForFaceting'] = $attributesForFaceting;

        return $this;
    }

    /**
     * Gets replicas.
     *
     * @return null|string[]
     */
    public function getReplicas()
    {
        return $this->container['replicas'] ?? null;
    }

    /**
     * Sets replicas.
     *
     * @param null|string[] $replicas Creates [replica indices](https://www.algolia.com/doc/guides/managing-results/refine-results/sorting/in-depth/replicas/).  Replicas are copies of a primary index with the same records but different settings, synonyms, or rules. If you want to offer a different ranking or sorting of your search results, you'll use replica indices. All index operations on a primary index are automatically forwarded to its replicas. To add a replica index, you must provide the complete set of replicas to this parameter. If you omit a replica from this list, the replica turns into a regular, standalone index that will no longer by synced with the primary index.  **Modifier**  - `virtual(\"REPLICA\")`.   Create a virtual replica,   Virtual replicas don't increase the number of records and are optimized for [Relevant sorting](https://www.algolia.com/doc/guides/managing-results/refine-results/sorting/in-depth/relevant-sort/).
     *
     * @return self
     */
    public function setReplicas($replicas)
    {
        $this->container['replicas'] = $replicas;

        return $this;
    }

    /**
     * Gets paginationLimitedTo.
     *
     * @return null|int
     */
    public function getPaginationLimitedTo()
    {
        return $this->container['paginationLimitedTo'] ?? null;
    }

    /**
     * Sets paginationLimitedTo.
     *
     * @param null|int $paginationLimitedTo Maximum number of search results that can be obtained through pagination.  Higher pagination limits might slow down your search. For pagination limits above 1,000, the sorting of results beyond the 1,000th hit can't be guaranteed.
     *
     * @return self
     */
    public function setPaginationLimitedTo($paginationLimitedTo)
    {
        if (!is_null($paginationLimitedTo) && ($paginationLimitedTo > 20000)) {
            throw new \InvalidArgumentException('invalid value for $paginationLimitedTo when calling IndexSettings., must be smaller than or equal to 20000.');
        }

        $this->container['paginationLimitedTo'] = $paginationLimitedTo;

        return $this;
    }

    /**
     * Gets unretrievableAttributes.
     *
     * @return null|string[]
     */
    public function getUnretrievableAttributes()
    {
        return $this->container['unretrievableAttributes'] ?? null;
    }

    /**
     * Sets unretrievableAttributes.
     *
     * @param null|string[] $unretrievableAttributes Attributes that can't be retrieved at query time.  This can be useful if you want to use an attribute for ranking or to [restrict access](https://www.algolia.com/doc/guides/security/api-keys/how-to/user-restricted-access-to-data/), but don't want to include it in the search results. Attribute names are case-sensitive.
     *
     * @return self
     */
    public function setUnretrievableAttributes($unretrievableAttributes)
    {
        $this->container['unretrievableAttributes'] = $unretrievableAttributes;

        return $this;
    }

    /**
     * Gets disableTypoToleranceOnWords.
     *
     * @return null|string[]
     */
    public function getDisableTypoToleranceOnWords()
    {
        return $this->container['disableTypoToleranceOnWords'] ?? null;
    }

    /**
     * Sets disableTypoToleranceOnWords.
     *
     * @param null|string[] $disableTypoToleranceOnWords Words for which you want to turn off [typo tolerance](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/typo-tolerance/). This also turns off [word splitting and concatenation](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/handling-natural-languages-nlp/in-depth/splitting-and-concatenation/) for the specified words.
     *
     * @return self
     */
    public function setDisableTypoToleranceOnWords($disableTypoToleranceOnWords)
    {
        $this->container['disableTypoToleranceOnWords'] = $disableTypoToleranceOnWords;

        return $this;
    }

    /**
     * Gets attributesToTransliterate.
     *
     * @return null|string[]
     */
    public function getAttributesToTransliterate()
    {
        return $this->container['attributesToTransliterate'] ?? null;
    }

    /**
     * Sets attributesToTransliterate.
     *
     * @param null|string[] $attributesToTransliterate Attributes, for which you want to support [Japanese transliteration](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/handling-natural-languages-nlp/in-depth/language-specific-configurations/#japanese-transliteration-and-type-ahead).  Transliteration supports searching in any of the Japanese writing systems. To support transliteration, you must set the indexing language to Japanese. Attribute names are case-sensitive.
     *
     * @return self
     */
    public function setAttributesToTransliterate($attributesToTransliterate)
    {
        $this->container['attributesToTransliterate'] = $attributesToTransliterate;

        return $this;
    }

    /**
     * Gets camelCaseAttributes.
     *
     * @return null|string[]
     */
    public function getCamelCaseAttributes()
    {
        return $this->container['camelCaseAttributes'] ?? null;
    }

    /**
     * Sets camelCaseAttributes.
     *
     * @param null|string[] $camelCaseAttributes Attributes for which to split [camel case](https://wikipedia.org/wiki/Camel_case) words. Attribute names are case-sensitive.
     *
     * @return self
     */
    public function setCamelCaseAttributes($camelCaseAttributes)
    {
        $this->container['camelCaseAttributes'] = $camelCaseAttributes;

        return $this;
    }

    /**
     * Gets decompoundedAttributes.
     *
     * @return null|object
     */
    public function getDecompoundedAttributes()
    {
        return $this->container['decompoundedAttributes'] ?? null;
    }

    /**
     * Sets decompoundedAttributes.
     *
     * @param null|object $decompoundedAttributes Searchable attributes to which Algolia should apply [word segmentation](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/handling-natural-languages-nlp/how-to/customize-segmentation/) (decompounding). Attribute names are case-sensitive.  Compound words are formed by combining two or more individual words, and are particularly prevalent in Germanic languages—for example, \"firefighter\". With decompounding, the individual components are indexed separately.  You can specify different lists for different languages. Decompounding is supported for these languages: Dutch (`nl`), German (`de`), Finnish (`fi`), Danish (`da`), Swedish (`sv`), and Norwegian (`no`). Decompounding doesn't work for words with [non-spacing mark Unicode characters](https://www.charactercodes.net/category/non-spacing_mark). For example, `Gartenstühle` won't be decompounded if the `ü` consists of `u` (U+0075) and `◌̈` (U+0308).
     *
     * @return self
     */
    public function setDecompoundedAttributes($decompoundedAttributes)
    {
        $this->container['decompoundedAttributes'] = $decompoundedAttributes;

        return $this;
    }

    /**
     * Gets indexLanguages.
     *
     * @return null|\Algolia\AlgoliaSearch\Model\Search\SupportedLanguage[]
     */
    public function getIndexLanguages()
    {
        return $this->container['indexLanguages'] ?? null;
    }

    /**
     * Sets indexLanguages.
     *
     * @param null|\Algolia\AlgoliaSearch\Model\Search\SupportedLanguage[] $indexLanguages Languages for language-specific processing steps, such as word detection and dictionary settings.  **You should always specify an indexing language.** If you don't specify an indexing language, the search engine uses all [supported languages](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/handling-natural-languages-nlp/in-depth/supported-languages/), or the languages you specified with the `ignorePlurals` or `removeStopWords` parameters. This can lead to unexpected search results. For more information, see [Language-specific configuration](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/handling-natural-languages-nlp/in-depth/language-specific-configurations/).
     *
     * @return self
     */
    public function setIndexLanguages($indexLanguages)
    {
        $this->container['indexLanguages'] = $indexLanguages;

        return $this;
    }

    /**
     * Gets disablePrefixOnAttributes.
     *
     * @return null|string[]
     */
    public function getDisablePrefixOnAttributes()
    {
        return $this->container['disablePrefixOnAttributes'] ?? null;
    }

    /**
     * Sets disablePrefixOnAttributes.
     *
     * @param null|string[] $disablePrefixOnAttributes Searchable attributes for which you want to turn off [prefix matching](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/override-search-engine-defaults/#adjusting-prefix-search). Attribute names are case-sensitive.
     *
     * @return self
     */
    public function setDisablePrefixOnAttributes($disablePrefixOnAttributes)
    {
        $this->container['disablePrefixOnAttributes'] = $disablePrefixOnAttributes;

        return $this;
    }

    /**
     * Gets allowCompressionOfIntegerArray.
     *
     * @return null|bool
     */
    public function getAllowCompressionOfIntegerArray()
    {
        return $this->container['allowCompressionOfIntegerArray'] ?? null;
    }

    /**
     * Sets allowCompressionOfIntegerArray.
     *
     * @param null|bool $allowCompressionOfIntegerArray Whether arrays with exclusively non-negative integers should be compressed for better performance. If true, the compressed arrays may be reordered.
     *
     * @return self
     */
    public function setAllowCompressionOfIntegerArray($allowCompressionOfIntegerArray)
    {
        $this->container['allowCompressionOfIntegerArray'] = $allowCompressionOfIntegerArray;

        return $this;
    }

    /**
     * Gets numericAttributesForFiltering.
     *
     * @return null|string[]
     */
    public function getNumericAttributesForFiltering()
    {
        return $this->container['numericAttributesForFiltering'] ?? null;
    }

    /**
     * Sets numericAttributesForFiltering.
     *
     * @param null|string[] $numericAttributesForFiltering Numeric attributes that can be used as [numerical filters](https://www.algolia.com/doc/guides/managing-results/rules/detecting-intent/how-to/applying-a-custom-filter-for-a-specific-query/#numerical-filters). Attribute names are case-sensitive.  By default, all numeric attributes are available as numerical filters. For faster indexing, reduce the number of numeric attributes.  If you want to turn off filtering for all numeric attributes, specifiy an attribute that doesn't exist in your index, such as `NO_NUMERIC_FILTERING`.  **Modifier**  - `equalOnly(\"ATTRIBUTE\")`.   Support only filtering based on equality comparisons `=` and `!=`.
     *
     * @return self
     */
    public function setNumericAttributesForFiltering($numericAttributesForFiltering)
    {
        $this->container['numericAttributesForFiltering'] = $numericAttributesForFiltering;

        return $this;
    }

    /**
     * Gets separatorsToIndex.
     *
     * @return null|string
     */
    public function getSeparatorsToIndex()
    {
        return $this->container['separatorsToIndex'] ?? null;
    }

    /**
     * Sets separatorsToIndex.
     *
     * @param null|string $separatorsToIndex Controls which separators are indexed.  Separators are all non-letter characters except spaces and currency characters, such as $€£¥. By default, separator characters aren't indexed. With `separatorsToIndex`, Algolia treats separator characters as separate words. For example, a search for `C#` would report two matches.
     *
     * @return self
     */
    public function setSeparatorsToIndex($separatorsToIndex)
    {
        $this->container['separatorsToIndex'] = $separatorsToIndex;

        return $this;
    }

    /**
     * Gets searchableAttributes.
     *
     * @return null|string[]
     */
    public function getSearchableAttributes()
    {
        return $this->container['searchableAttributes'] ?? null;
    }

    /**
     * Sets searchableAttributes.
     *
     * @param null|string[] $searchableAttributes Attributes used for searching. Attribute names are case-sensitive.  By default, all attributes are searchable and the [Attribute](https://www.algolia.com/doc/guides/managing-results/relevance-overview/in-depth/ranking-criteria/#attribute) ranking criterion is turned off. With a non-empty list, Algolia only returns results with matches in the selected attributes. In addition, the Attribute ranking criterion is turned on: matches in attributes that are higher in the list of `searchableAttributes` rank first. To make matches in two attributes rank equally, include them in a comma-separated string, such as `\"title,alternate_title\"`. Attributes with the same priority are always unordered.  For more information, see [Searchable attributes](https://www.algolia.com/doc/guides/sending-and-managing-data/prepare-your-data/how-to/setting-searchable-attributes/).  **Modifier**  - `unordered(\"ATTRIBUTE\")`.   Ignore the position of a match within the attribute.  Without modifier, matches at the beginning of an attribute rank higer than matches at the end.
     *
     * @return self
     */
    public function setSearchableAttributes($searchableAttributes)
    {
        $this->container['searchableAttributes'] = $searchableAttributes;

        return $this;
    }

    /**
     * Gets userData.
     *
     * @return null|object
     */
    public function getUserData()
    {
        return $this->container['userData'] ?? null;
    }

    /**
     * Sets userData.
     *
     * @param null|object $userData An object with custom data.  You can store up to 32kB as custom data.
     *
     * @return self
     */
    public function setUserData($userData)
    {
        $this->container['userData'] = $userData;

        return $this;
    }

    /**
     * Gets customNormalization.
     *
     * @return null|array<string,array<string,string>>
     */
    public function getCustomNormalization()
    {
        return $this->container['customNormalization'] ?? null;
    }

    /**
     * Sets customNormalization.
     *
     * @param null|array<string,array<string,string>> $customNormalization Characters and their normalized replacements. This overrides Algolia's default [normalization](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/handling-natural-languages-nlp/in-depth/normalization/).
     *
     * @return self
     */
    public function setCustomNormalization($customNormalization)
    {
        $this->container['customNormalization'] = $customNormalization;

        return $this;
    }

    /**
     * Gets attributeForDistinct.
     *
     * @return null|string
     */
    public function getAttributeForDistinct()
    {
        return $this->container['attributeForDistinct'] ?? null;
    }

    /**
     * Sets attributeForDistinct.
     *
     * @param null|string $attributeForDistinct Attribute that should be used to establish groups of results. Attribute names are case-sensitive.  All records with the same value for this attribute are considered a group. You can combine `attributeForDistinct` with the `distinct` search parameter to control how many items per group are included in the search results.  If you want to use the same attribute also for faceting, use the `afterDistinct` modifier of the `attributesForFaceting` setting. This applies faceting _after_ deduplication, which will result in accurate facet counts.
     *
     * @return self
     */
    public function setAttributeForDistinct($attributeForDistinct)
    {
        $this->container['attributeForDistinct'] = $attributeForDistinct;

        return $this;
    }

    /**
     * Gets attributesToRetrieve.
     *
     * @return null|string[]
     */
    public function getAttributesToRetrieve()
    {
        return $this->container['attributesToRetrieve'] ?? null;
    }

    /**
     * Sets attributesToRetrieve.
     *
     * @param null|string[] $attributesToRetrieve Attributes to include in the API response.  To reduce the size of your response, you can retrieve only some of the attributes. Attribute names are case-sensitive.  - `*` retrieves all attributes, except attributes included in the `customRanking` and `unretrievableAttributes` settings. - To retrieve all attributes except a specific one, prefix the attribute with a dash and combine it with the `*`: `[\"*\", \"-ATTRIBUTE\"]`. - The `objectID` attribute is always included.
     *
     * @return self
     */
    public function setAttributesToRetrieve($attributesToRetrieve)
    {
        $this->container['attributesToRetrieve'] = $attributesToRetrieve;

        return $this;
    }

    /**
     * Gets ranking.
     *
     * @return null|string[]
     */
    public function getRanking()
    {
        return $this->container['ranking'] ?? null;
    }

    /**
     * Sets ranking.
     *
     * @param null|string[] $ranking Determines the order in which Algolia returns your results.  By default, each entry corresponds to a [ranking criteria](https://www.algolia.com/doc/guides/managing-results/relevance-overview/in-depth/ranking-criteria/). The tie-breaking algorithm sequentially applies each criterion in the order they're specified. If you configure a replica index for [sorting by an attribute](https://www.algolia.com/doc/guides/managing-results/refine-results/sorting/how-to/sort-by-attribute/), you put the sorting attribute at the top of the list.  **Modifiers**  - `asc(\"ATTRIBUTE\")`.   Sort the index by the values of an attribute, in ascending order. - `desc(\"ATTRIBUTE\")`.   Sort the index by the values of an attribute, in descending order.  Before you modify the default setting, you should test your changes in the dashboard, and by [A/B testing](https://www.algolia.com/doc/guides/ab-testing/what-is-ab-testing/).
     *
     * @return self
     */
    public function setRanking($ranking)
    {
        $this->container['ranking'] = $ranking;

        return $this;
    }

    /**
     * Gets customRanking.
     *
     * @return null|string[]
     */
    public function getCustomRanking()
    {
        return $this->container['customRanking'] ?? null;
    }

    /**
     * Sets customRanking.
     *
     * @param null|string[] $customRanking Attributes to use as [custom ranking](https://www.algolia.com/doc/guides/managing-results/must-do/custom-ranking/). Attribute names are case-sensitive.  The custom ranking attributes decide which items are shown first if the other ranking criteria are equal.  Records with missing values for your selected custom ranking attributes are always sorted last. Boolean attributes are sorted based on their alphabetical order.  **Modifiers**  - `asc(\"ATTRIBUTE\")`.   Sort the index by the values of an attribute, in ascending order.  - `desc(\"ATTRIBUTE\")`.   Sort the index by the values of an attribute, in descending order.  If you use two or more custom ranking attributes, [reduce the precision](https://www.algolia.com/doc/guides/managing-results/must-do/custom-ranking/how-to/controlling-custom-ranking-metrics-precision/) of your first attributes, or the other attributes will never be applied.
     *
     * @return self
     */
    public function setCustomRanking($customRanking)
    {
        $this->container['customRanking'] = $customRanking;

        return $this;
    }

    /**
     * Gets relevancyStrictness.
     *
     * @return null|int
     */
    public function getRelevancyStrictness()
    {
        return $this->container['relevancyStrictness'] ?? null;
    }

    /**
     * Sets relevancyStrictness.
     *
     * @param null|int $relevancyStrictness Relevancy threshold below which less relevant results aren't included in the results.  You can only set `relevancyStrictness` on [virtual replica indices](https://www.algolia.com/doc/guides/managing-results/refine-results/sorting/in-depth/replicas/#what-are-virtual-replicas). Use this setting to strike a balance between the relevance and number of returned results.
     *
     * @return self
     */
    public function setRelevancyStrictness($relevancyStrictness)
    {
        $this->container['relevancyStrictness'] = $relevancyStrictness;

        return $this;
    }

    /**
     * Gets attributesToHighlight.
     *
     * @return null|string[]
     */
    public function getAttributesToHighlight()
    {
        return $this->container['attributesToHighlight'] ?? null;
    }

    /**
     * Sets attributesToHighlight.
     *
     * @param null|string[] $attributesToHighlight Attributes to highlight.  By default, all searchable attributes are highlighted. Use `*` to highlight all attributes or use an empty array `[]` to turn off highlighting. Attribute names are case-sensitive.  With highlighting, strings that match the search query are surrounded by HTML tags defined by `highlightPreTag` and `highlightPostTag`. You can use this to visually highlight matching parts of a search query in your UI.  For more information, see [Highlighting and snippeting](https://www.algolia.com/doc/guides/building-search-ui/ui-and-ux-patterns/highlighting-snippeting/js/).
     *
     * @return self
     */
    public function setAttributesToHighlight($attributesToHighlight)
    {
        $this->container['attributesToHighlight'] = $attributesToHighlight;

        return $this;
    }

    /**
     * Gets attributesToSnippet.
     *
     * @return null|string[]
     */
    public function getAttributesToSnippet()
    {
        return $this->container['attributesToSnippet'] ?? null;
    }

    /**
     * Sets attributesToSnippet.
     *
     * @param null|string[] $attributesToSnippet Attributes for which to enable snippets. Attribute names are case-sensitive.  Snippets provide additional context to matched words. If you enable snippets, they include 10 words, including the matched word. The matched word will also be wrapped by HTML tags for highlighting. You can adjust the number of words with the following notation: `ATTRIBUTE:NUMBER`, where `NUMBER` is the number of words to be extracted.
     *
     * @return self
     */
    public function setAttributesToSnippet($attributesToSnippet)
    {
        $this->container['attributesToSnippet'] = $attributesToSnippet;

        return $this;
    }

    /**
     * Gets highlightPreTag.
     *
     * @return null|string
     */
    public function getHighlightPreTag()
    {
        return $this->container['highlightPreTag'] ?? null;
    }

    /**
     * Sets highlightPreTag.
     *
     * @param null|string $highlightPreTag HTML tag to insert before the highlighted parts in all highlighted results and snippets
     *
     * @return self
     */
    public function setHighlightPreTag($highlightPreTag)
    {
        $this->container['highlightPreTag'] = $highlightPreTag;

        return $this;
    }

    /**
     * Gets highlightPostTag.
     *
     * @return null|string
     */
    public function getHighlightPostTag()
    {
        return $this->container['highlightPostTag'] ?? null;
    }

    /**
     * Sets highlightPostTag.
     *
     * @param null|string $highlightPostTag HTML tag to insert after the highlighted parts in all highlighted results and snippets
     *
     * @return self
     */
    public function setHighlightPostTag($highlightPostTag)
    {
        $this->container['highlightPostTag'] = $highlightPostTag;

        return $this;
    }

    /**
     * Gets snippetEllipsisText.
     *
     * @return null|string
     */
    public function getSnippetEllipsisText()
    {
        return $this->container['snippetEllipsisText'] ?? null;
    }

    /**
     * Sets snippetEllipsisText.
     *
     * @param null|string $snippetEllipsisText string used as an ellipsis indicator when a snippet is truncated
     *
     * @return self
     */
    public function setSnippetEllipsisText($snippetEllipsisText)
    {
        $this->container['snippetEllipsisText'] = $snippetEllipsisText;

        return $this;
    }

    /**
     * Gets restrictHighlightAndSnippetArrays.
     *
     * @return null|bool
     */
    public function getRestrictHighlightAndSnippetArrays()
    {
        return $this->container['restrictHighlightAndSnippetArrays'] ?? null;
    }

    /**
     * Sets restrictHighlightAndSnippetArrays.
     *
     * @param null|bool $restrictHighlightAndSnippetArrays Whether to restrict highlighting and snippeting to items that at least partially matched the search query. By default, all items are highlighted and snippeted.
     *
     * @return self
     */
    public function setRestrictHighlightAndSnippetArrays($restrictHighlightAndSnippetArrays)
    {
        $this->container['restrictHighlightAndSnippetArrays'] = $restrictHighlightAndSnippetArrays;

        return $this;
    }

    /**
     * Gets hitsPerPage.
     *
     * @return null|int
     */
    public function getHitsPerPage()
    {
        return $this->container['hitsPerPage'] ?? null;
    }

    /**
     * Sets hitsPerPage.
     *
     * @param null|int $hitsPerPage number of hits per page
     *
     * @return self
     */
    public function setHitsPerPage($hitsPerPage)
    {
        if (!is_null($hitsPerPage) && ($hitsPerPage > 1000)) {
            throw new \InvalidArgumentException('invalid value for $hitsPerPage when calling IndexSettings., must be smaller than or equal to 1000.');
        }
        if (!is_null($hitsPerPage) && ($hitsPerPage < 1)) {
            throw new \InvalidArgumentException('invalid value for $hitsPerPage when calling IndexSettings., must be bigger than or equal to 1.');
        }

        $this->container['hitsPerPage'] = $hitsPerPage;

        return $this;
    }

    /**
     * Gets minWordSizefor1Typo.
     *
     * @return null|int
     */
    public function getMinWordSizefor1Typo()
    {
        return $this->container['minWordSizefor1Typo'] ?? null;
    }

    /**
     * Sets minWordSizefor1Typo.
     *
     * @param null|int $minWordSizefor1Typo Minimum number of characters a word in the search query must contain to accept matches with [one typo](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/typo-tolerance/in-depth/configuring-typo-tolerance/#configuring-word-length-for-typos).
     *
     * @return self
     */
    public function setMinWordSizefor1Typo($minWordSizefor1Typo)
    {
        $this->container['minWordSizefor1Typo'] = $minWordSizefor1Typo;

        return $this;
    }

    /**
     * Gets minWordSizefor2Typos.
     *
     * @return null|int
     */
    public function getMinWordSizefor2Typos()
    {
        return $this->container['minWordSizefor2Typos'] ?? null;
    }

    /**
     * Sets minWordSizefor2Typos.
     *
     * @param null|int $minWordSizefor2Typos Minimum number of characters a word in the search query must contain to accept matches with [two typos](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/typo-tolerance/in-depth/configuring-typo-tolerance/#configuring-word-length-for-typos).
     *
     * @return self
     */
    public function setMinWordSizefor2Typos($minWordSizefor2Typos)
    {
        $this->container['minWordSizefor2Typos'] = $minWordSizefor2Typos;

        return $this;
    }

    /**
     * Gets typoTolerance.
     *
     * @return null|TypoTolerance
     */
    public function getTypoTolerance()
    {
        return $this->container['typoTolerance'] ?? null;
    }

    /**
     * Sets typoTolerance.
     *
     * @param null|TypoTolerance $typoTolerance typoTolerance
     *
     * @return self
     */
    public function setTypoTolerance($typoTolerance)
    {
        $this->container['typoTolerance'] = $typoTolerance;

        return $this;
    }

    /**
     * Gets allowTyposOnNumericTokens.
     *
     * @return null|bool
     */
    public function getAllowTyposOnNumericTokens()
    {
        return $this->container['allowTyposOnNumericTokens'] ?? null;
    }

    /**
     * Sets allowTyposOnNumericTokens.
     *
     * @param null|bool $allowTyposOnNumericTokens Whether to allow typos on numbers in the search query.  Turn off this setting to reduce the number of irrelevant matches when searching in large sets of similar numbers.
     *
     * @return self
     */
    public function setAllowTyposOnNumericTokens($allowTyposOnNumericTokens)
    {
        $this->container['allowTyposOnNumericTokens'] = $allowTyposOnNumericTokens;

        return $this;
    }

    /**
     * Gets disableTypoToleranceOnAttributes.
     *
     * @return null|string[]
     */
    public function getDisableTypoToleranceOnAttributes()
    {
        return $this->container['disableTypoToleranceOnAttributes'] ?? null;
    }

    /**
     * Sets disableTypoToleranceOnAttributes.
     *
     * @param null|string[] $disableTypoToleranceOnAttributes Attributes for which you want to turn off [typo tolerance](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/typo-tolerance/). Attribute names are case-sensitive.  Returning only exact matches can help when:  - [Searching in hyphenated attributes](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/typo-tolerance/how-to/how-to-search-in-hyphenated-attributes/). - Reducing the number of matches when you have too many.   This can happen with attributes that are long blocks of text, such as product descriptions.  Consider alternatives such as `disableTypoToleranceOnWords` or adding synonyms if your attributes have intentional unusual spellings that might look like typos.
     *
     * @return self
     */
    public function setDisableTypoToleranceOnAttributes($disableTypoToleranceOnAttributes)
    {
        $this->container['disableTypoToleranceOnAttributes'] = $disableTypoToleranceOnAttributes;

        return $this;
    }

    /**
     * Gets ignorePlurals.
     *
     * @return null|IgnorePlurals
     */
    public function getIgnorePlurals()
    {
        return $this->container['ignorePlurals'] ?? null;
    }

    /**
     * Sets ignorePlurals.
     *
     * @param null|IgnorePlurals $ignorePlurals ignorePlurals
     *
     * @return self
     */
    public function setIgnorePlurals($ignorePlurals)
    {
        $this->container['ignorePlurals'] = $ignorePlurals;

        return $this;
    }

    /**
     * Gets removeStopWords.
     *
     * @return null|RemoveStopWords
     */
    public function getRemoveStopWords()
    {
        return $this->container['removeStopWords'] ?? null;
    }

    /**
     * Sets removeStopWords.
     *
     * @param null|RemoveStopWords $removeStopWords removeStopWords
     *
     * @return self
     */
    public function setRemoveStopWords($removeStopWords)
    {
        $this->container['removeStopWords'] = $removeStopWords;

        return $this;
    }

    /**
     * Gets keepDiacriticsOnCharacters.
     *
     * @return null|string
     */
    public function getKeepDiacriticsOnCharacters()
    {
        return $this->container['keepDiacriticsOnCharacters'] ?? null;
    }

    /**
     * Sets keepDiacriticsOnCharacters.
     *
     * @param null|string $keepDiacriticsOnCharacters Characters for which diacritics should be preserved.  By default, Algolia removes diacritics from letters. For example, `é` becomes `e`. If this causes issues in your search, you can specify characters that should keep their diacritics.
     *
     * @return self
     */
    public function setKeepDiacriticsOnCharacters($keepDiacriticsOnCharacters)
    {
        $this->container['keepDiacriticsOnCharacters'] = $keepDiacriticsOnCharacters;

        return $this;
    }

    /**
     * Gets queryLanguages.
     *
     * @return null|\Algolia\AlgoliaSearch\Model\Search\SupportedLanguage[]
     */
    public function getQueryLanguages()
    {
        return $this->container['queryLanguages'] ?? null;
    }

    /**
     * Sets queryLanguages.
     *
     * @param null|\Algolia\AlgoliaSearch\Model\Search\SupportedLanguage[] $queryLanguages Languages for language-specific query processing steps such as plurals, stop-word removal, and word-detection dictionaries.  This setting sets a default list of languages used by the `removeStopWords` and `ignorePlurals` settings. This setting also sets a dictionary for word detection in the logogram-based [CJK](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/handling-natural-languages-nlp/in-depth/normalization/#normalization-for-logogram-based-languages-cjk) languages. To support this, you must place the CJK language **first**.  **You should always specify a query language.** If you don't specify an indexing language, the search engine uses all [supported languages](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/handling-natural-languages-nlp/in-depth/supported-languages/), or the languages you specified with the `ignorePlurals` or `removeStopWords` parameters. This can lead to unexpected search results. For more information, see [Language-specific configuration](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/handling-natural-languages-nlp/in-depth/language-specific-configurations/).
     *
     * @return self
     */
    public function setQueryLanguages($queryLanguages)
    {
        $this->container['queryLanguages'] = $queryLanguages;

        return $this;
    }

    /**
     * Gets decompoundQuery.
     *
     * @return null|bool
     */
    public function getDecompoundQuery()
    {
        return $this->container['decompoundQuery'] ?? null;
    }

    /**
     * Sets decompoundQuery.
     *
     * @param null|bool $decompoundQuery Whether to split compound words in the query into their building blocks.  For more information, see [Word segmentation](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/handling-natural-languages-nlp/in-depth/language-specific-configurations/#splitting-compound-words). Word segmentation is supported for these languages: German, Dutch, Finnish, Swedish, and Norwegian. Decompounding doesn't work for words with [non-spacing mark Unicode characters](https://www.charactercodes.net/category/non-spacing_mark). For example, `Gartenstühle` won't be decompounded if the `ü` consists of `u` (U+0075) and `◌̈` (U+0308).
     *
     * @return self
     */
    public function setDecompoundQuery($decompoundQuery)
    {
        $this->container['decompoundQuery'] = $decompoundQuery;

        return $this;
    }

    /**
     * Gets enableRules.
     *
     * @return null|bool
     */
    public function getEnableRules()
    {
        return $this->container['enableRules'] ?? null;
    }

    /**
     * Sets enableRules.
     *
     * @param null|bool $enableRules whether to enable rules
     *
     * @return self
     */
    public function setEnableRules($enableRules)
    {
        $this->container['enableRules'] = $enableRules;

        return $this;
    }

    /**
     * Gets enablePersonalization.
     *
     * @return null|bool
     */
    public function getEnablePersonalization()
    {
        return $this->container['enablePersonalization'] ?? null;
    }

    /**
     * Sets enablePersonalization.
     *
     * @param null|bool $enablePersonalization whether to enable Personalization
     *
     * @return self
     */
    public function setEnablePersonalization($enablePersonalization)
    {
        $this->container['enablePersonalization'] = $enablePersonalization;

        return $this;
    }

    /**
     * Gets queryType.
     *
     * @return null|QueryType
     */
    public function getQueryType()
    {
        return $this->container['queryType'] ?? null;
    }

    /**
     * Sets queryType.
     *
     * @param null|QueryType $queryType queryType
     *
     * @return self
     */
    public function setQueryType($queryType)
    {
        $this->container['queryType'] = $queryType;

        return $this;
    }

    /**
     * Gets removeWordsIfNoResults.
     *
     * @return null|RemoveWordsIfNoResults
     */
    public function getRemoveWordsIfNoResults()
    {
        return $this->container['removeWordsIfNoResults'] ?? null;
    }

    /**
     * Sets removeWordsIfNoResults.
     *
     * @param null|RemoveWordsIfNoResults $removeWordsIfNoResults removeWordsIfNoResults
     *
     * @return self
     */
    public function setRemoveWordsIfNoResults($removeWordsIfNoResults)
    {
        $this->container['removeWordsIfNoResults'] = $removeWordsIfNoResults;

        return $this;
    }

    /**
     * Gets mode.
     *
     * @return null|Mode
     */
    public function getMode()
    {
        return $this->container['mode'] ?? null;
    }

    /**
     * Sets mode.
     *
     * @param null|Mode $mode mode
     *
     * @return self
     */
    public function setMode($mode)
    {
        $this->container['mode'] = $mode;

        return $this;
    }

    /**
     * Gets semanticSearch.
     *
     * @return null|SemanticSearch
     */
    public function getSemanticSearch()
    {
        return $this->container['semanticSearch'] ?? null;
    }

    /**
     * Sets semanticSearch.
     *
     * @param null|SemanticSearch $semanticSearch semanticSearch
     *
     * @return self
     */
    public function setSemanticSearch($semanticSearch)
    {
        $this->container['semanticSearch'] = $semanticSearch;

        return $this;
    }

    /**
     * Gets advancedSyntax.
     *
     * @return null|bool
     */
    public function getAdvancedSyntax()
    {
        return $this->container['advancedSyntax'] ?? null;
    }

    /**
     * Sets advancedSyntax.
     *
     * @param null|bool $advancedSyntax Whether to support phrase matching and excluding words from search queries.  Use the `advancedSyntaxFeatures` parameter to control which feature is supported.
     *
     * @return self
     */
    public function setAdvancedSyntax($advancedSyntax)
    {
        $this->container['advancedSyntax'] = $advancedSyntax;

        return $this;
    }

    /**
     * Gets optionalWords.
     *
     * @return null|string[]
     */
    public function getOptionalWords()
    {
        return $this->container['optionalWords'] ?? null;
    }

    /**
     * Sets optionalWords.
     *
     * @param null|string[] $optionalWords Words that should be considered optional when found in the query.  By default, records must match all words in the search query to be included in the search results. Adding optional words can help to increase the number of search results by running an additional search query that doesn't include the optional words. For example, if the search query is \"action video\" and \"video\" is an optional word, the search engine runs two queries. One for \"action video\" and one for \"action\". Records that match all words are ranked higher.  For a search query with 4 or more words **and** all its words are optional, the number of matched words required for a record to be included in the search results increases for every 1,000 records:  - If `optionalWords` has less than 10 words, the required number of matched words increases by 1:   results 1 to 1,000 require 1 matched word, results 1,001 to 2000 need 2 matched words. - If `optionalWords` has 10 or more words, the number of required matched words increases by the number of optional words dividied by 5 (rounded down).   For example, with 18 optional words: results 1 to 1,000 require 1 matched word, results 1,001 to 2000 need 4 matched words.  For more information, see [Optional words](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/empty-or-insufficient-results/#creating-a-list-of-optional-words).
     *
     * @return self
     */
    public function setOptionalWords($optionalWords)
    {
        $this->container['optionalWords'] = $optionalWords;

        return $this;
    }

    /**
     * Gets disableExactOnAttributes.
     *
     * @return null|string[]
     */
    public function getDisableExactOnAttributes()
    {
        return $this->container['disableExactOnAttributes'] ?? null;
    }

    /**
     * Sets disableExactOnAttributes.
     *
     * @param null|string[] $disableExactOnAttributes Searchable attributes for which you want to [turn off the Exact ranking criterion](https://www.algolia.com/doc/guides/managing-results/optimize-search-results/override-search-engine-defaults/in-depth/adjust-exact-settings/#turn-off-exact-for-some-attributes). Attribute names are case-sensitive.  This can be useful for attributes with long values, where the likelyhood of an exact match is high, such as product descriptions. Turning off the Exact ranking criterion for these attributes favors exact matching on other attributes. This reduces the impact of individual attributes with a lot of content on ranking.
     *
     * @return self
     */
    public function setDisableExactOnAttributes($disableExactOnAttributes)
    {
        $this->container['disableExactOnAttributes'] = $disableExactOnAttributes;

        return $this;
    }

    /**
     * Gets exactOnSingleWordQuery.
     *
     * @return null|ExactOnSingleWordQuery
     */
    public function getExactOnSingleWordQuery()
    {
        return $this->container['exactOnSingleWordQuery'] ?? null;
    }

    /**
     * Sets exactOnSingleWordQuery.
     *
     * @param null|ExactOnSingleWordQuery $exactOnSingleWordQuery exactOnSingleWordQuery
     *
     * @return self
     */
    public function setExactOnSingleWordQuery($exactOnSingleWordQuery)
    {
        $this->container['exactOnSingleWordQuery'] = $exactOnSingleWordQuery;

        return $this;
    }

    /**
     * Gets alternativesAsExact.
     *
     * @return null|\Algolia\AlgoliaSearch\Model\Search\AlternativesAsExact[]
     */
    public function getAlternativesAsExact()
    {
        return $this->container['alternativesAsExact'] ?? null;
    }

    /**
     * Sets alternativesAsExact.
     *
     * @param null|\Algolia\AlgoliaSearch\Model\Search\AlternativesAsExact[] $alternativesAsExact Alternatives of query words that should be considered as exact matches by the Exact ranking criterion.  - `ignorePlurals`.   Plurals and similar declensions added by the `ignorePlurals` setting are considered exact matches.  - `singleWordSynonym`.   Single-word synonyms, such as \"NY/NYC\" are considered exact matches.  - `multiWordsSynonym`.   Multi-word synonyms, such as \"NY/New York\" are considered exact matches.
     *
     * @return self
     */
    public function setAlternativesAsExact($alternativesAsExact)
    {
        $this->container['alternativesAsExact'] = $alternativesAsExact;

        return $this;
    }

    /**
     * Gets advancedSyntaxFeatures.
     *
     * @return null|\Algolia\AlgoliaSearch\Model\Search\AdvancedSyntaxFeatures[]
     */
    public function getAdvancedSyntaxFeatures()
    {
        return $this->container['advancedSyntaxFeatures'] ?? null;
    }

    /**
     * Sets advancedSyntaxFeatures.
     *
     * @param null|\Algolia\AlgoliaSearch\Model\Search\AdvancedSyntaxFeatures[] $advancedSyntaxFeatures Advanced search syntax features you want to support.  - `exactPhrase`.   Phrases in quotes must match exactly.   For example, `sparkly blue \"iPhone case\"` only returns records with the exact string \"iPhone case\".  - `excludeWords`.   Query words prefixed with a `-` must not occur in a record.   For example, `search -engine` matches records that contain \"search\" but not \"engine\".  This setting only has an effect if `advancedSyntax` is true.
     *
     * @return self
     */
    public function setAdvancedSyntaxFeatures($advancedSyntaxFeatures)
    {
        $this->container['advancedSyntaxFeatures'] = $advancedSyntaxFeatures;

        return $this;
    }

    /**
     * Gets distinct.
     *
     * @return null|Distinct
     */
    public function getDistinct()
    {
        return $this->container['distinct'] ?? null;
    }

    /**
     * Sets distinct.
     *
     * @param null|Distinct $distinct distinct
     *
     * @return self
     */
    public function setDistinct($distinct)
    {
        $this->container['distinct'] = $distinct;

        return $this;
    }

    /**
     * Gets replaceSynonymsInHighlight.
     *
     * @return null|bool
     */
    public function getReplaceSynonymsInHighlight()
    {
        return $this->container['replaceSynonymsInHighlight'] ?? null;
    }

    /**
     * Sets replaceSynonymsInHighlight.
     *
     * @param null|bool $replaceSynonymsInHighlight Whether to replace a highlighted word with the matched synonym.  By default, the original words are highlighted even if a synonym matches. For example, with `home` as a synonym for `house` and a search for `home`, records matching either \"home\" or \"house\" are included in the search results, and either \"home\" or \"house\" are highlighted.  With `replaceSynonymsInHighlight` set to `true`, a search for `home` still matches the same records, but all occurences of \"house\" are replaced by \"home\" in the highlighted response.
     *
     * @return self
     */
    public function setReplaceSynonymsInHighlight($replaceSynonymsInHighlight)
    {
        $this->container['replaceSynonymsInHighlight'] = $replaceSynonymsInHighlight;

        return $this;
    }

    /**
     * Gets minProximity.
     *
     * @return null|int
     */
    public function getMinProximity()
    {
        return $this->container['minProximity'] ?? null;
    }

    /**
     * Sets minProximity.
     *
     * @param null|int $minProximity Minimum proximity score for two matching words.  This adjusts the [Proximity ranking criterion](https://www.algolia.com/doc/guides/managing-results/relevance-overview/in-depth/ranking-criteria/#proximity) by equally scoring matches that are farther apart.  For example, if `minProximity` is 2, neighboring matches and matches with one word between them would have the same score.
     *
     * @return self
     */
    public function setMinProximity($minProximity)
    {
        if (!is_null($minProximity) && ($minProximity > 7)) {
            throw new \InvalidArgumentException('invalid value for $minProximity when calling IndexSettings., must be smaller than or equal to 7.');
        }
        if (!is_null($minProximity) && ($minProximity < 1)) {
            throw new \InvalidArgumentException('invalid value for $minProximity when calling IndexSettings., must be bigger than or equal to 1.');
        }

        $this->container['minProximity'] = $minProximity;

        return $this;
    }

    /**
     * Gets responseFields.
     *
     * @return null|string[]
     */
    public function getResponseFields()
    {
        return $this->container['responseFields'] ?? null;
    }

    /**
     * Sets responseFields.
     *
     * @param null|string[] $responseFields Properties to include in the API response of `search` and `browse` requests.  By default, all response properties are included. To reduce the response size, you can select, which attributes should be included.  You can't exclude these properties: `message`, `warning`, `cursor`, `serverUsed`, `indexUsed`, `abTestVariantID`, `parsedQuery`, or any property triggered by the `getRankingInfo` parameter.  Don't exclude properties that you might need in your search UI.
     *
     * @return self
     */
    public function setResponseFields($responseFields)
    {
        $this->container['responseFields'] = $responseFields;

        return $this;
    }

    /**
     * Gets maxFacetHits.
     *
     * @return null|int
     */
    public function getMaxFacetHits()
    {
        return $this->container['maxFacetHits'] ?? null;
    }

    /**
     * Sets maxFacetHits.
     *
     * @param null|int $maxFacetHits Maximum number of facet values to return when [searching for facet values](https://www.algolia.com/doc/guides/managing-results/refine-results/faceting/#search-for-facet-values).
     *
     * @return self
     */
    public function setMaxFacetHits($maxFacetHits)
    {
        if (!is_null($maxFacetHits) && ($maxFacetHits > 100)) {
            throw new \InvalidArgumentException('invalid value for $maxFacetHits when calling IndexSettings., must be smaller than or equal to 100.');
        }

        $this->container['maxFacetHits'] = $maxFacetHits;

        return $this;
    }

    /**
     * Gets maxValuesPerFacet.
     *
     * @return null|int
     */
    public function getMaxValuesPerFacet()
    {
        return $this->container['maxValuesPerFacet'] ?? null;
    }

    /**
     * Sets maxValuesPerFacet.
     *
     * @param null|int $maxValuesPerFacet maximum number of facet values to return for each facet
     *
     * @return self
     */
    public function setMaxValuesPerFacet($maxValuesPerFacet)
    {
        if (!is_null($maxValuesPerFacet) && ($maxValuesPerFacet > 1000)) {
            throw new \InvalidArgumentException('invalid value for $maxValuesPerFacet when calling IndexSettings., must be smaller than or equal to 1000.');
        }

        $this->container['maxValuesPerFacet'] = $maxValuesPerFacet;

        return $this;
    }

    /**
     * Gets sortFacetValuesBy.
     *
     * @return null|string
     */
    public function getSortFacetValuesBy()
    {
        return $this->container['sortFacetValuesBy'] ?? null;
    }

    /**
     * Sets sortFacetValuesBy.
     *
     * @param null|string $sortFacetValuesBy Order in which to retrieve facet values.  - `count`.   Facet values are retrieved by decreasing count.   The count is the number of matching records containing this facet value.  - `alpha`.   Retrieve facet values alphabetically.  This setting doesn't influence how facet values are displayed in your UI (see `renderingContent`). For more information, see [facet value display](https://www.algolia.com/doc/guides/building-search-ui/ui-and-ux-patterns/facet-display/js/).
     *
     * @return self
     */
    public function setSortFacetValuesBy($sortFacetValuesBy)
    {
        $this->container['sortFacetValuesBy'] = $sortFacetValuesBy;

        return $this;
    }

    /**
     * Gets attributeCriteriaComputedByMinProximity.
     *
     * @return null|bool
     */
    public function getAttributeCriteriaComputedByMinProximity()
    {
        return $this->container['attributeCriteriaComputedByMinProximity'] ?? null;
    }

    /**
     * Sets attributeCriteriaComputedByMinProximity.
     *
     * @param null|bool $attributeCriteriaComputedByMinProximity Whether the best matching attribute should be determined by minimum proximity.  This setting only affects ranking if the Attribute ranking criterion comes before Proximity in the `ranking` setting. If true, the best matching attribute is selected based on the minimum proximity of multiple matches. Otherwise, the best matching attribute is determined by the order in the `searchableAttributes` setting.
     *
     * @return self
     */
    public function setAttributeCriteriaComputedByMinProximity($attributeCriteriaComputedByMinProximity)
    {
        $this->container['attributeCriteriaComputedByMinProximity'] = $attributeCriteriaComputedByMinProximity;

        return $this;
    }

    /**
     * Gets renderingContent.
     *
     * @return null|RenderingContent
     */
    public function getRenderingContent()
    {
        return $this->container['renderingContent'] ?? null;
    }

    /**
     * Sets renderingContent.
     *
     * @param null|RenderingContent $renderingContent renderingContent
     *
     * @return self
     */
    public function setRenderingContent($renderingContent)
    {
        $this->container['renderingContent'] = $renderingContent;

        return $this;
    }

    /**
     * Gets enableReRanking.
     *
     * @return null|bool
     */
    public function getEnableReRanking()
    {
        return $this->container['enableReRanking'] ?? null;
    }

    /**
     * Sets enableReRanking.
     *
     * @param null|bool $enableReRanking Whether this search will use [Dynamic Re-Ranking](https://www.algolia.com/doc/guides/algolia-ai/re-ranking/).  This setting only has an effect if you activated Dynamic Re-Ranking for this index in the Algolia dashboard.
     *
     * @return self
     */
    public function setEnableReRanking($enableReRanking)
    {
        $this->container['enableReRanking'] = $enableReRanking;

        return $this;
    }

    /**
     * Gets reRankingApplyFilter.
     *
     * @return null|ReRankingApplyFilter
     */
    public function getReRankingApplyFilter()
    {
        return $this->container['reRankingApplyFilter'] ?? null;
    }

    /**
     * Sets reRankingApplyFilter.
     *
     * @param null|ReRankingApplyFilter $reRankingApplyFilter reRankingApplyFilter
     *
     * @return self
     */
    public function setReRankingApplyFilter($reRankingApplyFilter)
    {
        $this->container['reRankingApplyFilter'] = $reRankingApplyFilter;

        return $this;
    }

    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param int $offset Offset
     *
     * @return bool
     */
    public function offsetExists($offset)
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param int $offset Offset
     *
     * @return null|mixed
     */
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param null|int $offset Offset
     * @param mixed    $value  Value to be set
     */
    public function offsetSet($offset, $value)
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param int $offset Offset
     */
    public function offsetUnset($offset)
    {
        unset($this->container[$offset]);
    }
}
