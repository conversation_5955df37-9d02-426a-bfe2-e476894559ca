<?php

// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.

namespace Algolia\AlgoliaSearch\Model\Recommend;

/**
 * TrendingItemsModel Class Doc Comment.
 *
 * @category Class
 *
 * @description Trending items model.  Trending items are determined from the number of conversion events collected on them.
 */
class TrendingItemsModel
{
    /**
     * Possible values of this enum.
     */
    public const TRENDING_ITEMS = 'trending-items';

    /**
     * Gets allowable values of the enum.
     *
     * @return string[]
     */
    public static function getAllowableEnumValues()
    {
        return [
            self::TRENDING_ITEMS,
        ];
    }
}
