<?php

// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.

namespace Algolia\AlgoliaSearch\Model\Recommend;

/**
 * RecommendModels Class Doc Comment.
 *
 * @category Class
 */
class RecommendModels
{
    /**
     * Possible values of this enum.
     */
    public const RELATED_PRODUCTS = 'related-products';

    public const BOUGHT_TOGETHER = 'bought-together';

    public const TRENDING_FACETS = 'trending-facets';

    public const TRENDING_ITEMS = 'trending-items';

    /**
     * Gets allowable values of the enum.
     *
     * @return string[]
     */
    public static function getAllowableEnumValues()
    {
        return [
            self::RELATED_PRODUCTS,
            self::BOUGHT_TOGETHER,
            self::TRENDING_FACETS,
            self::TRENDING_ITEMS,
        ];
    }
}
