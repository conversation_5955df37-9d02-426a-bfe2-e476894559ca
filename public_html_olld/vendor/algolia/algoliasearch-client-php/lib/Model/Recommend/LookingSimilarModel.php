<?php

// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.

namespace Algolia\AlgoliaSearch\Model\Recommend;

/**
 * LookingSimilarModel Class Doc Comment.
 *
 * @category Class
 *
 * @description Looking similar model.  This model recommends items that look similar to the item with the ID &#x60;objectID&#x60; based on image attributes in your index.
 */
class LookingSimilarModel
{
    /**
     * Possible values of this enum.
     */
    public const LOOKING_SIMILAR = 'looking-similar';

    /**
     * Gets allowable values of the enum.
     *
     * @return string[]
     */
    public static function getAllowableEnumValues()
    {
        return [
            self::LOOKING_SIMILAR,
        ];
    }
}
