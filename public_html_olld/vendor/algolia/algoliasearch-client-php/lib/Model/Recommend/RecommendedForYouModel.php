<?php

// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.

namespace Algolia\AlgoliaSearch\Model\Recommend;

/**
 * RecommendedForYouModel Class Doc Comment.
 *
 * @category Class
 *
 * @description \&quot;Recommened for you\&quot; model.
 */
class RecommendedForYouModel
{
    /**
     * Possible values of this enum.
     */
    public const RECOMMENDED_FOR_YOU = 'recommended-for-you';

    /**
     * Gets allowable values of the enum.
     *
     * @return string[]
     */
    public static function getAllowableEnumValues()
    {
        return [
            self::RECOMMENDED_FOR_YOU,
        ];
    }
}
