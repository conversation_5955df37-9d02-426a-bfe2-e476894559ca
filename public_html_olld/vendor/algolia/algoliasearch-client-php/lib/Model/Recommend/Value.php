<?php

// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.

namespace Algolia\AlgoliaSearch\Model\Recommend;

use Algolia\AlgoliaSearch\Model\AbstractModel;

/**
 * Value Class Doc Comment.
 *
 * @category Class
 */
class Value extends AbstractModel implements ModelInterface, \ArrayAccess, \JsonSerializable
{
    /**
     * Array of property to type mappings. Used for (de)serialization.
     *
     * @var string[]
     */
    protected static $modelTypes = [
        'order' => 'string[]',
        'sortRemainingBy' => '\Algolia\AlgoliaSearch\Model\Recommend\SortRemainingBy',
        'hide' => 'string[]',
    ];

    /**
     * Array of property to format mappings. Used for (de)serialization.
     *
     * @var string[]
     */
    protected static $modelFormats = [
        'order' => null,
        'sortRemainingBy' => null,
        'hide' => null,
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name.
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'order' => 'order',
        'sortRemainingBy' => 'sortRemainingBy',
        'hide' => 'hide',
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses).
     *
     * @var string[]
     */
    protected static $setters = [
        'order' => 'setOrder',
        'sortRemainingBy' => 'setSortRemainingBy',
        'hide' => 'setHide',
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests).
     *
     * @var string[]
     */
    protected static $getters = [
        'order' => 'getOrder',
        'sortRemainingBy' => 'getSortRemainingBy',
        'hide' => 'getHide',
    ];

    /**
     * Associative array for storing property values.
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor.
     *
     * @param mixed[] $data Associated array of property values
     */
    public function __construct(?array $data = null)
    {
        if (isset($data['order'])) {
            $this->container['order'] = $data['order'];
        }
        if (isset($data['sortRemainingBy'])) {
            $this->container['sortRemainingBy'] = $data['sortRemainingBy'];
        }
        if (isset($data['hide'])) {
            $this->container['hide'] = $data['hide'];
        }
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name.
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of property to type mappings. Used for (de)serialization.
     *
     * @return array
     */
    public static function modelTypes()
    {
        return self::$modelTypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization.
     *
     * @return array
     */
    public static function modelFormats()
    {
        return self::$modelFormats;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses).
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests).
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        return [];
    }

    /**
     * Validate all the properties in the model
     * return true if all passed.
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return 0 === count($this->listInvalidProperties());
    }

    /**
     * Gets order.
     *
     * @return null|string[]
     */
    public function getOrder()
    {
        return $this->container['order'] ?? null;
    }

    /**
     * Sets order.
     *
     * @param null|string[] $order Explicit order of facets or facet values.  This setting lets you always show specific facets or facet values at the top of the list.
     *
     * @return self
     */
    public function setOrder($order)
    {
        $this->container['order'] = $order;

        return $this;
    }

    /**
     * Gets sortRemainingBy.
     *
     * @return null|SortRemainingBy
     */
    public function getSortRemainingBy()
    {
        return $this->container['sortRemainingBy'] ?? null;
    }

    /**
     * Sets sortRemainingBy.
     *
     * @param null|SortRemainingBy $sortRemainingBy sortRemainingBy
     *
     * @return self
     */
    public function setSortRemainingBy($sortRemainingBy)
    {
        $this->container['sortRemainingBy'] = $sortRemainingBy;

        return $this;
    }

    /**
     * Gets hide.
     *
     * @return null|string[]
     */
    public function getHide()
    {
        return $this->container['hide'] ?? null;
    }

    /**
     * Sets hide.
     *
     * @param null|string[] $hide hide facet values
     *
     * @return self
     */
    public function setHide($hide)
    {
        $this->container['hide'] = $hide;

        return $this;
    }

    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param int $offset Offset
     *
     * @return bool
     */
    public function offsetExists($offset)
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param int $offset Offset
     *
     * @return null|mixed
     */
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param null|int $offset Offset
     * @param mixed    $value  Value to be set
     */
    public function offsetSet($offset, $value)
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param int $offset Offset
     */
    public function offsetUnset($offset)
    {
        unset($this->container[$offset]);
    }
}
