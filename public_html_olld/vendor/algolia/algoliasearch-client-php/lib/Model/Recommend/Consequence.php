<?php

// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.

namespace Algolia\AlgoliaSearch\Model\Recommend;

use Algolia\AlgoliaSearch\Model\AbstractModel;

/**
 * Consequence Class Doc Comment.
 *
 * @category Class
 *
 * @description Effect of the rule.
 */
class Consequence extends AbstractModel implements ModelInterface, \ArrayAccess, \JsonSerializable
{
    /**
     * Array of property to type mappings. Used for (de)serialization.
     *
     * @var string[]
     */
    protected static $modelTypes = [
        'hide' => '\Algolia\AlgoliaSearch\Model\Recommend\HideConsequenceObject[]',
        'promote' => '\Algolia\AlgoliaSearch\Model\Recommend\PromoteConsequenceObject[]',
        'params' => '\Algolia\AlgoliaSearch\Model\Recommend\ParamsConsequence',
    ];

    /**
     * Array of property to format mappings. Used for (de)serialization.
     *
     * @var string[]
     */
    protected static $modelFormats = [
        'hide' => null,
        'promote' => null,
        'params' => null,
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name.
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'hide' => 'hide',
        'promote' => 'promote',
        'params' => 'params',
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses).
     *
     * @var string[]
     */
    protected static $setters = [
        'hide' => 'setHide',
        'promote' => 'setPromote',
        'params' => 'setParams',
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests).
     *
     * @var string[]
     */
    protected static $getters = [
        'hide' => 'getHide',
        'promote' => 'getPromote',
        'params' => 'getParams',
    ];

    /**
     * Associative array for storing property values.
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor.
     *
     * @param mixed[] $data Associated array of property values
     */
    public function __construct(?array $data = null)
    {
        if (isset($data['hide'])) {
            $this->container['hide'] = $data['hide'];
        }
        if (isset($data['promote'])) {
            $this->container['promote'] = $data['promote'];
        }
        if (isset($data['params'])) {
            $this->container['params'] = $data['params'];
        }
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name.
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of property to type mappings. Used for (de)serialization.
     *
     * @return array
     */
    public static function modelTypes()
    {
        return self::$modelTypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization.
     *
     * @return array
     */
    public static function modelFormats()
    {
        return self::$modelFormats;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses).
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests).
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        if (isset($this->container['hide']) && (count($this->container['hide']) < 1)) {
            $invalidProperties[] = "invalid value for 'hide', number of items must be greater than or equal to 1.";
        }

        if (isset($this->container['promote']) && (count($this->container['promote']) < 1)) {
            $invalidProperties[] = "invalid value for 'promote', number of items must be greater than or equal to 1.";
        }

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed.
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return 0 === count($this->listInvalidProperties());
    }

    /**
     * Gets hide.
     *
     * @return null|\Algolia\AlgoliaSearch\Model\Recommend\HideConsequenceObject[]
     */
    public function getHide()
    {
        return $this->container['hide'] ?? null;
    }

    /**
     * Sets hide.
     *
     * @param null|\Algolia\AlgoliaSearch\Model\Recommend\HideConsequenceObject[] $hide exclude items from recommendations
     *
     * @return self
     */
    public function setHide($hide)
    {
        if (!is_null($hide) && (count($hide) < 1)) {
            throw new \InvalidArgumentException('invalid length for $hide when calling Consequence., number of items must be greater than or equal to 1.');
        }
        $this->container['hide'] = $hide;

        return $this;
    }

    /**
     * Gets promote.
     *
     * @return null|\Algolia\AlgoliaSearch\Model\Recommend\PromoteConsequenceObject[]
     */
    public function getPromote()
    {
        return $this->container['promote'] ?? null;
    }

    /**
     * Sets promote.
     *
     * @param null|\Algolia\AlgoliaSearch\Model\Recommend\PromoteConsequenceObject[] $promote place items at specific positions in the list of recommendations
     *
     * @return self
     */
    public function setPromote($promote)
    {
        if (!is_null($promote) && (count($promote) < 1)) {
            throw new \InvalidArgumentException('invalid length for $promote when calling Consequence., number of items must be greater than or equal to 1.');
        }
        $this->container['promote'] = $promote;

        return $this;
    }

    /**
     * Gets params.
     *
     * @return null|ParamsConsequence
     */
    public function getParams()
    {
        return $this->container['params'] ?? null;
    }

    /**
     * Sets params.
     *
     * @param null|ParamsConsequence $params params
     *
     * @return self
     */
    public function setParams($params)
    {
        $this->container['params'] = $params;

        return $this;
    }

    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param int $offset Offset
     *
     * @return bool
     */
    public function offsetExists($offset)
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param int $offset Offset
     *
     * @return null|mixed
     */
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param null|int $offset Offset
     * @param mixed    $value  Value to be set
     */
    public function offsetSet($offset, $value)
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param int $offset Offset
     */
    public function offsetUnset($offset)
    {
        unset($this->container[$offset]);
    }
}
