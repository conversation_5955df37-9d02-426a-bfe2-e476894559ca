<?php

// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.

namespace Algolia\AlgoliaSearch\Model\Recommend;

/**
 * FbtModel Class Doc Comment.
 *
 * @category Class
 *
 * @description Frequently bought together model.  This model recommends items that have been purchased within 1 day with the item with the ID &#x60;objectID&#x60;.
 */
class FbtModel
{
    /**
     * Possible values of this enum.
     */
    public const BOUGHT_TOGETHER = 'bought-together';

    /**
     * Gets allowable values of the enum.
     *
     * @return string[]
     */
    public static function getAllowableEnumValues()
    {
        return [
            self::BOUGHT_TOGETHER,
        ];
    }
}
