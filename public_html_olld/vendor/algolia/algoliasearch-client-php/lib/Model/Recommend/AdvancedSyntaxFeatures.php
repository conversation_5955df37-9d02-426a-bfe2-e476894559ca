<?php

// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.

namespace Algolia\AlgoliaSearch\Model\Recommend;

/**
 * AdvancedSyntaxFeatures Class Doc Comment.
 *
 * @category Class
 */
class AdvancedSyntaxFeatures
{
    /**
     * Possible values of this enum.
     */
    public const EXACT_PHRASE = 'exactPhrase';

    public const EXCLUDE_WORDS = 'excludeWords';

    /**
     * Gets allowable values of the enum.
     *
     * @return string[]
     */
    public static function getAllowableEnumValues()
    {
        return [
            self::EXACT_PHRASE,
            self::EXCLUDE_WORDS,
        ];
    }
}
