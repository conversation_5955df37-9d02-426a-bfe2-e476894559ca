<?php

// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.

namespace Algolia\AlgoliaSearch\Model\Ingestion;

use Algolia\AlgoliaSearch\Model\AbstractModel;

/**
 * Event Class Doc Comment.
 *
 * @category Class
 *
 * @description An event describe a step of the task execution flow..
 */
class Event extends AbstractModel implements ModelInterface, \ArrayAccess, \JsonSerializable
{
    /**
     * Array of property to type mappings. Used for (de)serialization.
     *
     * @var string[]
     */
    protected static $modelTypes = [
        'eventID' => 'string',
        'runID' => 'string',
        'parentID' => 'string',
        'status' => '\Algolia\AlgoliaSearch\Model\Ingestion\EventStatus',
        'type' => '\Algolia\AlgoliaSearch\Model\Ingestion\EventType',
        'batchSize' => 'int',
        'data' => 'array<string,mixed>',
        'publishedAt' => 'string',
    ];

    /**
     * Array of property to format mappings. Used for (de)serialization.
     *
     * @var string[]
     */
    protected static $modelFormats = [
        'eventID' => null,
        'runID' => null,
        'parentID' => null,
        'status' => null,
        'type' => null,
        'batchSize' => null,
        'data' => null,
        'publishedAt' => null,
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name.
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'eventID' => 'eventID',
        'runID' => 'runID',
        'parentID' => 'parentID',
        'status' => 'status',
        'type' => 'type',
        'batchSize' => 'batchSize',
        'data' => 'data',
        'publishedAt' => 'publishedAt',
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses).
     *
     * @var string[]
     */
    protected static $setters = [
        'eventID' => 'setEventID',
        'runID' => 'setRunID',
        'parentID' => 'setParentID',
        'status' => 'setStatus',
        'type' => 'setType',
        'batchSize' => 'setBatchSize',
        'data' => 'setData',
        'publishedAt' => 'setPublishedAt',
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests).
     *
     * @var string[]
     */
    protected static $getters = [
        'eventID' => 'getEventID',
        'runID' => 'getRunID',
        'parentID' => 'getParentID',
        'status' => 'getStatus',
        'type' => 'getType',
        'batchSize' => 'getBatchSize',
        'data' => 'getData',
        'publishedAt' => 'getPublishedAt',
    ];

    /**
     * Associative array for storing property values.
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor.
     *
     * @param mixed[] $data Associated array of property values
     */
    public function __construct(?array $data = null)
    {
        if (isset($data['eventID'])) {
            $this->container['eventID'] = $data['eventID'];
        }
        if (isset($data['runID'])) {
            $this->container['runID'] = $data['runID'];
        }
        if (isset($data['parentID'])) {
            $this->container['parentID'] = $data['parentID'];
        }
        if (isset($data['status'])) {
            $this->container['status'] = $data['status'];
        }
        if (isset($data['type'])) {
            $this->container['type'] = $data['type'];
        }
        if (isset($data['batchSize'])) {
            $this->container['batchSize'] = $data['batchSize'];
        }
        if (isset($data['data'])) {
            $this->container['data'] = $data['data'];
        }
        if (isset($data['publishedAt'])) {
            $this->container['publishedAt'] = $data['publishedAt'];
        }
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name.
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of property to type mappings. Used for (de)serialization.
     *
     * @return array
     */
    public static function modelTypes()
    {
        return self::$modelTypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization.
     *
     * @return array
     */
    public static function modelFormats()
    {
        return self::$modelFormats;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses).
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests).
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        if (!isset($this->container['eventID']) || null === $this->container['eventID']) {
            $invalidProperties[] = "'eventID' can't be null";
        }
        if (!isset($this->container['runID']) || null === $this->container['runID']) {
            $invalidProperties[] = "'runID' can't be null";
        }
        if (!isset($this->container['status']) || null === $this->container['status']) {
            $invalidProperties[] = "'status' can't be null";
        }
        if (!isset($this->container['type']) || null === $this->container['type']) {
            $invalidProperties[] = "'type' can't be null";
        }
        if (!isset($this->container['batchSize']) || null === $this->container['batchSize']) {
            $invalidProperties[] = "'batchSize' can't be null";
        }
        if ($this->container['batchSize'] < 0) {
            $invalidProperties[] = "invalid value for 'batchSize', must be bigger than or equal to 0.";
        }

        if (!isset($this->container['publishedAt']) || null === $this->container['publishedAt']) {
            $invalidProperties[] = "'publishedAt' can't be null";
        }

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed.
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return 0 === count($this->listInvalidProperties());
    }

    /**
     * Gets eventID.
     *
     * @return string
     */
    public function getEventID()
    {
        return $this->container['eventID'] ?? null;
    }

    /**
     * Sets eventID.
     *
     * @param string $eventID universally unique identifier (UUID) of an event
     *
     * @return self
     */
    public function setEventID($eventID)
    {
        $this->container['eventID'] = $eventID;

        return $this;
    }

    /**
     * Gets runID.
     *
     * @return string
     */
    public function getRunID()
    {
        return $this->container['runID'] ?? null;
    }

    /**
     * Sets runID.
     *
     * @param string $runID universally unique identifier (UUID) of a task run
     *
     * @return self
     */
    public function setRunID($runID)
    {
        $this->container['runID'] = $runID;

        return $this;
    }

    /**
     * Gets parentID.
     *
     * @return null|string
     */
    public function getParentID()
    {
        return $this->container['parentID'] ?? null;
    }

    /**
     * Sets parentID.
     *
     * @param null|string $parentID the parent event, the cause of this event
     *
     * @return self
     */
    public function setParentID($parentID)
    {
        $this->container['parentID'] = $parentID;

        return $this;
    }

    /**
     * Gets status.
     *
     * @return EventStatus
     */
    public function getStatus()
    {
        return $this->container['status'] ?? null;
    }

    /**
     * Sets status.
     *
     * @param EventStatus $status status
     *
     * @return self
     */
    public function setStatus($status)
    {
        $this->container['status'] = $status;

        return $this;
    }

    /**
     * Gets type.
     *
     * @return EventType
     */
    public function getType()
    {
        return $this->container['type'] ?? null;
    }

    /**
     * Sets type.
     *
     * @param EventType $type type
     *
     * @return self
     */
    public function setType($type)
    {
        $this->container['type'] = $type;

        return $this;
    }

    /**
     * Gets batchSize.
     *
     * @return int
     */
    public function getBatchSize()
    {
        return $this->container['batchSize'] ?? null;
    }

    /**
     * Sets batchSize.
     *
     * @param int $batchSize the extracted record batch size
     *
     * @return self
     */
    public function setBatchSize($batchSize)
    {
        if ($batchSize < 0) {
            throw new \InvalidArgumentException('invalid value for $batchSize when calling Event., must be bigger than or equal to 0.');
        }

        $this->container['batchSize'] = $batchSize;

        return $this;
    }

    /**
     * Gets data.
     *
     * @return null|array<string,mixed>
     */
    public function getData()
    {
        return $this->container['data'] ?? null;
    }

    /**
     * Sets data.
     *
     * @param null|array<string,mixed> $data data
     *
     * @return self
     */
    public function setData($data)
    {
        $this->container['data'] = $data;

        return $this;
    }

    /**
     * Gets publishedAt.
     *
     * @return string
     */
    public function getPublishedAt()
    {
        return $this->container['publishedAt'] ?? null;
    }

    /**
     * Sets publishedAt.
     *
     * @param string $publishedAt date of publish RFC 3339 format
     *
     * @return self
     */
    public function setPublishedAt($publishedAt)
    {
        $this->container['publishedAt'] = $publishedAt;

        return $this;
    }

    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param int $offset Offset
     *
     * @return bool
     */
    public function offsetExists($offset)
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param int $offset Offset
     *
     * @return null|mixed
     */
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param null|int $offset Offset
     * @param mixed    $value  Value to be set
     */
    public function offsetSet($offset, $value)
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param int $offset Offset
     */
    public function offsetUnset($offset)
    {
        unset($this->container[$offset]);
    }
}
