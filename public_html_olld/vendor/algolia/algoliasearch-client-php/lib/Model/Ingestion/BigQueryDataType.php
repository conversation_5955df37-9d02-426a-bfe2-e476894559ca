<?php

// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.

namespace Algolia\AlgoliaSearch\Model\Ingestion;

/**
 * BigQueryDataType Class Doc Comment.
 *
 * @category Class
 */
class BigQueryDataType
{
    /**
     * Possible values of this enum.
     */
    public const GA4 = 'ga4';

    public const GA360 = 'ga360';

    /**
     * Gets allowable values of the enum.
     *
     * @return string[]
     */
    public static function getAllowableEnumValues()
    {
        return [
            self::GA4,
            self::GA360,
        ];
    }
}
