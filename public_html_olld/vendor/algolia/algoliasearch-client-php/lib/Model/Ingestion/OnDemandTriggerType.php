<?php

// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.

namespace Algolia\AlgoliaSearch\Model\Ingestion;

/**
 * OnDemandTriggerType Class Doc Comment.
 *
 * @category Class
 *
 * @description Task is run manually, with the &#x60;/run&#x60; endpoint.
 */
class OnDemandTriggerType
{
    /**
     * Possible values of this enum.
     */
    public const ON_DEMAND = 'onDemand';

    /**
     * Gets allowable values of the enum.
     *
     * @return string[]
     */
    public static function getAllowableEnumValues()
    {
        return [
            self::ON_DEMAND,
        ];
    }
}
