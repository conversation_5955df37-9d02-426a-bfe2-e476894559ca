<?php

// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.

namespace Algolia\AlgoliaSearch\Model\Ingestion;

/**
 * DockerImageType Class Doc Comment.
 *
 * @category Class
 *
 * @description Image type.
 */
class DockerImageType
{
    /**
     * Possible values of this enum.
     */
    public const SINGER = 'singer';

    public const CUSTOM = 'custom';

    public const AIRBYTE = 'airbyte';

    /**
     * Gets allowable values of the enum.
     *
     * @return string[]
     */
    public static function getAllowableEnumValues()
    {
        return [
            self::SINGER,
            self::CUSTOM,
            self::AIRBYTE,
        ];
    }
}
