<?php

// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.

namespace Algolia\AlgoliaSearch\Model\Ingestion;

/**
 * AuthenticationType Class Doc Comment.
 *
 * @category Class
 *
 * @description Type of authentication. This determines the type of credentials required in the &#x60;input&#x60; object.
 */
class AuthenticationType
{
    /**
     * Possible values of this enum.
     */
    public const GOOGLE_SERVICE_ACCOUNT = 'googleServiceAccount';

    public const BASIC = 'basic';

    public const API_KEY = 'apiKey';

    public const OAUTH = 'oauth';

    public const ALGOLIA = 'algolia';

    public const ALGOLIA_INSIGHTS = 'algoliaInsights';

    /**
     * Gets allowable values of the enum.
     *
     * @return string[]
     */
    public static function getAllowableEnumValues()
    {
        return [
            self::GOOGLE_SERVICE_ACCOUNT,
            self::BASIC,
            self::API_KEY,
            self::OAUTH,
            self::ALGOLIA,
            self::ALGOLIA_INSIGHTS,
        ];
    }
}
