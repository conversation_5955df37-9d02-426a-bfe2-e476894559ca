<?php

// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.

namespace Algolia\AlgoliaSearch\Model\Ingestion;

/**
 * RunStatus Class Doc Comment.
 *
 * @category Class
 *
 * @description Task run status.
 */
class RunStatus
{
    /**
     * Possible values of this enum.
     */
    public const CREATED = 'created';

    public const STARTED = 'started';

    public const IDLED = 'idled';

    public const FINISHED = 'finished';

    public const SKIPPED = 'skipped';

    /**
     * Gets allowable values of the enum.
     *
     * @return string[]
     */
    public static function getAllowableEnumValues()
    {
        return [
            self::CREATED,
            self::STARTED,
            self::IDLED,
            self::FINISHED,
            self::SKIPPED,
        ];
    }
}
