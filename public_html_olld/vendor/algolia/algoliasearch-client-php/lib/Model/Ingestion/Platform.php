<?php

// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.

namespace Algolia\AlgoliaSearch\Model\Ingestion;

/**
 * Platform Class Doc Comment.
 *
 * @category Class
 *
 * @description Name of an ecommerce platform with which to authenticate. This determines which authentication type you can select.
 */
class Platform
{
    /**
     * Possible values of this enum.
     */
    public const BIGCOMMERCE = 'bigcommerce';

    public const COMMERCETOOLS = 'commercetools';

    public const SHOPIFY = 'shopify';

    /**
     * Gets allowable values of the enum.
     *
     * @return string[]
     */
    public static function getAllowableEnumValues()
    {
        return [
            self::BIGCOMMERCE,
            self::COMMERCETOOLS,
            self::SHOPIFY,
        ];
    }
}
