<?php

// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.

namespace Algolia\AlgoliaSearch\Model\Ingestion;

/**
 * MappingTypeCSV Class Doc Comment.
 *
 * @category Class
 */
class MappingTypeCSV
{
    /**
     * Possible values of this enum.
     */
    public const STRING = 'string';

    public const INTEGER = 'integer';

    public const FLOAT = 'float';

    public const BOOLEAN = 'boolean';

    public const JSON = 'json';

    /**
     * Gets allowable values of the enum.
     *
     * @return string[]
     */
    public static function getAllowableEnumValues()
    {
        return [
            self::STRING,
            self::INTEGER,
            self::FLOAT,
            self::BOOLEAN,
            self::JSON,
        ];
    }
}
