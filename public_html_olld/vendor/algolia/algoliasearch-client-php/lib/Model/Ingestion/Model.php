<?php

// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.

namespace Algolia\AlgoliaSearch\Model\Ingestion;

use Algolia\AlgoliaSearch\Model\AbstractModel;

/**
 * Model Class Doc Comment.
 *
 * @category Class
 */
class Model extends AbstractModel implements ModelInterface, \ArrayAccess, \JsonSerializable
{
    /**
     * Array of property to type mappings. Used for (de)serialization.
     *
     * @var string[]
     */
    protected static $modelTypes = [
        'fullname' => 'string',
        'modelName' => 'string',
        'systemPrompt' => 'string',
        'id' => 'string',
        'provider' => 'string',
    ];

    /**
     * Array of property to format mappings. Used for (de)serialization.
     *
     * @var string[]
     */
    protected static $modelFormats = [
        'fullname' => null,
        'modelName' => null,
        'systemPrompt' => null,
        'id' => null,
        'provider' => null,
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name.
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'fullname' => 'fullname',
        'modelName' => 'modelName',
        'systemPrompt' => 'systemPrompt',
        'id' => 'id',
        'provider' => 'provider',
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses).
     *
     * @var string[]
     */
    protected static $setters = [
        'fullname' => 'setFullname',
        'modelName' => 'setModelName',
        'systemPrompt' => 'setSystemPrompt',
        'id' => 'setId',
        'provider' => 'setProvider',
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests).
     *
     * @var string[]
     */
    protected static $getters = [
        'fullname' => 'getFullname',
        'modelName' => 'getModelName',
        'systemPrompt' => 'getSystemPrompt',
        'id' => 'getId',
        'provider' => 'getProvider',
    ];

    /**
     * Associative array for storing property values.
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor.
     *
     * @param mixed[] $data Associated array of property values
     */
    public function __construct(?array $data = null)
    {
        if (isset($data['fullname'])) {
            $this->container['fullname'] = $data['fullname'];
        }
        if (isset($data['modelName'])) {
            $this->container['modelName'] = $data['modelName'];
        }
        if (isset($data['systemPrompt'])) {
            $this->container['systemPrompt'] = $data['systemPrompt'];
        }
        if (isset($data['id'])) {
            $this->container['id'] = $data['id'];
        }
        if (isset($data['provider'])) {
            $this->container['provider'] = $data['provider'];
        }
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name.
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of property to type mappings. Used for (de)serialization.
     *
     * @return array
     */
    public static function modelTypes()
    {
        return self::$modelTypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization.
     *
     * @return array
     */
    public static function modelFormats()
    {
        return self::$modelFormats;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses).
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests).
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        if (!isset($this->container['fullname']) || null === $this->container['fullname']) {
            $invalidProperties[] = "'fullname' can't be null";
        }
        if (!isset($this->container['modelName']) || null === $this->container['modelName']) {
            $invalidProperties[] = "'modelName' can't be null";
        }
        if (!isset($this->container['systemPrompt']) || null === $this->container['systemPrompt']) {
            $invalidProperties[] = "'systemPrompt' can't be null";
        }
        if (!isset($this->container['id']) || null === $this->container['id']) {
            $invalidProperties[] = "'id' can't be null";
        }
        if (!isset($this->container['provider']) || null === $this->container['provider']) {
            $invalidProperties[] = "'provider' can't be null";
        }

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed.
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return 0 === count($this->listInvalidProperties());
    }

    /**
     * Gets fullname.
     *
     * @return string
     */
    public function getFullname()
    {
        return $this->container['fullname'] ?? null;
    }

    /**
     * Sets fullname.
     *
     * @param string $fullname fullname
     *
     * @return self
     */
    public function setFullname($fullname)
    {
        $this->container['fullname'] = $fullname;

        return $this;
    }

    /**
     * Gets modelName.
     *
     * @return string
     */
    public function getModelName()
    {
        return $this->container['modelName'] ?? null;
    }

    /**
     * Sets modelName.
     *
     * @param string $modelName modelName
     *
     * @return self
     */
    public function setModelName($modelName)
    {
        $this->container['modelName'] = $modelName;

        return $this;
    }

    /**
     * Gets systemPrompt.
     *
     * @return string
     */
    public function getSystemPrompt()
    {
        return $this->container['systemPrompt'] ?? null;
    }

    /**
     * Sets systemPrompt.
     *
     * @param string $systemPrompt systemPrompt
     *
     * @return self
     */
    public function setSystemPrompt($systemPrompt)
    {
        $this->container['systemPrompt'] = $systemPrompt;

        return $this;
    }

    /**
     * Gets id.
     *
     * @return string
     */
    public function getId()
    {
        return $this->container['id'] ?? null;
    }

    /**
     * Sets id.
     *
     * @param string $id id
     *
     * @return self
     */
    public function setId($id)
    {
        $this->container['id'] = $id;

        return $this;
    }

    /**
     * Gets provider.
     *
     * @return string
     */
    public function getProvider()
    {
        return $this->container['provider'] ?? null;
    }

    /**
     * Sets provider.
     *
     * @param string $provider provider
     *
     * @return self
     */
    public function setProvider($provider)
    {
        $this->container['provider'] = $provider;

        return $this;
    }

    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param int $offset Offset
     *
     * @return bool
     */
    public function offsetExists($offset)
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param int $offset Offset
     *
     * @return null|mixed
     */
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param null|int $offset Offset
     * @param mixed    $value  Value to be set
     */
    public function offsetSet($offset, $value)
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param int $offset Offset
     */
    public function offsetUnset($offset)
    {
        unset($this->container[$offset]);
    }
}
