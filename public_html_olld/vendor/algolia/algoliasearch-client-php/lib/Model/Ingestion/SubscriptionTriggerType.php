<?php

// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.

namespace Algolia\AlgoliaSearch\Model\Ingestion;

/**
 * SubscriptionTriggerType Class Doc Comment.
 *
 * @category Class
 *
 * @description Task runs after receiving subscribed event.
 */
class SubscriptionTriggerType
{
    /**
     * Possible values of this enum.
     */
    public const SUBSCRIPTION = 'subscription';

    /**
     * Gets allowable values of the enum.
     *
     * @return string[]
     */
    public static function getAllowableEnumValues()
    {
        return [
            self::SUBSCRIPTION,
        ];
    }
}
