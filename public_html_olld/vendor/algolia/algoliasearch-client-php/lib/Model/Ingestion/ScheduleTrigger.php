<?php

// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.

namespace Algolia\AlgoliaSearch\Model\Ingestion;

use Algolia\AlgoliaSearch\Model\AbstractModel;

/**
 * ScheduleTrigger Class Doc Comment.
 *
 * @category Class
 *
 * @description Trigger information for scheduled tasks.
 */
class ScheduleTrigger extends AbstractModel implements ModelInterface, \ArrayAccess, \JsonSerializable
{
    /**
     * Array of property to type mappings. Used for (de)serialization.
     *
     * @var string[]
     */
    protected static $modelTypes = [
        'type' => '\Algolia\AlgoliaSearch\Model\Ingestion\ScheduleTriggerType',
        'cron' => 'string',
        'lastRun' => 'string',
        'nextRun' => 'string',
    ];

    /**
     * Array of property to format mappings. Used for (de)serialization.
     *
     * @var string[]
     */
    protected static $modelFormats = [
        'type' => null,
        'cron' => null,
        'lastRun' => null,
        'nextRun' => null,
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name.
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'type' => 'type',
        'cron' => 'cron',
        'lastRun' => 'lastRun',
        'nextRun' => 'nextRun',
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses).
     *
     * @var string[]
     */
    protected static $setters = [
        'type' => 'setType',
        'cron' => 'setCron',
        'lastRun' => 'setLastRun',
        'nextRun' => 'setNextRun',
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests).
     *
     * @var string[]
     */
    protected static $getters = [
        'type' => 'getType',
        'cron' => 'getCron',
        'lastRun' => 'getLastRun',
        'nextRun' => 'getNextRun',
    ];

    /**
     * Associative array for storing property values.
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor.
     *
     * @param mixed[] $data Associated array of property values
     */
    public function __construct(?array $data = null)
    {
        if (isset($data['type'])) {
            $this->container['type'] = $data['type'];
        }
        if (isset($data['cron'])) {
            $this->container['cron'] = $data['cron'];
        }
        if (isset($data['lastRun'])) {
            $this->container['lastRun'] = $data['lastRun'];
        }
        if (isset($data['nextRun'])) {
            $this->container['nextRun'] = $data['nextRun'];
        }
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name.
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of property to type mappings. Used for (de)serialization.
     *
     * @return array
     */
    public static function modelTypes()
    {
        return self::$modelTypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization.
     *
     * @return array
     */
    public static function modelFormats()
    {
        return self::$modelFormats;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses).
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests).
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        if (!isset($this->container['type']) || null === $this->container['type']) {
            $invalidProperties[] = "'type' can't be null";
        }
        if (!isset($this->container['cron']) || null === $this->container['cron']) {
            $invalidProperties[] = "'cron' can't be null";
        }
        if (!isset($this->container['nextRun']) || null === $this->container['nextRun']) {
            $invalidProperties[] = "'nextRun' can't be null";
        }

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed.
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return 0 === count($this->listInvalidProperties());
    }

    /**
     * Gets type.
     *
     * @return ScheduleTriggerType
     */
    public function getType()
    {
        return $this->container['type'] ?? null;
    }

    /**
     * Sets type.
     *
     * @param ScheduleTriggerType $type type
     *
     * @return self
     */
    public function setType($type)
    {
        $this->container['type'] = $type;

        return $this;
    }

    /**
     * Gets cron.
     *
     * @return string
     */
    public function getCron()
    {
        return $this->container['cron'] ?? null;
    }

    /**
     * Sets cron.
     *
     * @param string $cron cron expression for the task's schedule
     *
     * @return self
     */
    public function setCron($cron)
    {
        $this->container['cron'] = $cron;

        return $this;
    }

    /**
     * Gets lastRun.
     *
     * @return null|string
     */
    public function getLastRun()
    {
        return $this->container['lastRun'] ?? null;
    }

    /**
     * Sets lastRun.
     *
     * @param null|string $lastRun the last time the scheduled task ran in RFC 3339 format
     *
     * @return self
     */
    public function setLastRun($lastRun)
    {
        $this->container['lastRun'] = $lastRun;

        return $this;
    }

    /**
     * Gets nextRun.
     *
     * @return string
     */
    public function getNextRun()
    {
        return $this->container['nextRun'] ?? null;
    }

    /**
     * Sets nextRun.
     *
     * @param string $nextRun the next scheduled run of the task in RFC 3339 format
     *
     * @return self
     */
    public function setNextRun($nextRun)
    {
        $this->container['nextRun'] = $nextRun;

        return $this;
    }

    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param int $offset Offset
     *
     * @return bool
     */
    public function offsetExists($offset)
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param int $offset Offset
     *
     * @return null|mixed
     */
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param null|int $offset Offset
     * @param mixed    $value  Value to be set
     */
    public function offsetSet($offset, $value)
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param int $offset Offset
     */
    public function offsetUnset($offset)
    {
        unset($this->container[$offset]);
    }
}
