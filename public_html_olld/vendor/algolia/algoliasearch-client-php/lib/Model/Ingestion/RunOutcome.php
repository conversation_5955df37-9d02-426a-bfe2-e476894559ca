<?php

// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.

namespace Algolia\AlgoliaSearch\Model\Ingestion;

/**
 * RunOutcome Class Doc Comment.
 *
 * @category Class
 *
 * @description Task run outcome.
 */
class RunOutcome
{
    /**
     * Possible values of this enum.
     */
    public const SUCCESS = 'success';

    public const FAILURE = 'failure';

    public const PROCESSING = 'processing';

    /**
     * Gets allowable values of the enum.
     *
     * @return string[]
     */
    public static function getAllowableEnumValues()
    {
        return [
            self::SUCCESS,
            self::FAILURE,
            self::PROCESSING,
        ];
    }
}
