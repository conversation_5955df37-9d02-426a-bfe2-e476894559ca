<?php

// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.

namespace Algolia\AlgoliaSearch\Model\Ingestion;

/**
 * SourceType Class Doc Comment.
 *
 * @category Class
 */
class SourceType
{
    /**
     * Possible values of this enum.
     */
    public const BIGCOMMERCE = 'bigcommerce';

    public const BIGQUERY = 'bigquery';

    public const COMMERCETOOLS = 'commercetools';

    public const CSV = 'csv';

    public const DOCKER = 'docker';

    public const GA4_BIGQUERY_EXPORT = 'ga4BigqueryExport';

    public const JSON = 'json';

    public const SHOPIFY = 'shopify';

    public const SFCC = 'sfcc';

    /**
     * Gets allowable values of the enum.
     *
     * @return string[]
     */
    public static function getAllowableEnumValues()
    {
        return [
            self::BIGCOMMERCE,
            self::BIGQUERY,
            self::COMMERCETOOLS,
            self::CSV,
            self::DOCKER,
            self::GA4_BIGQUERY_EXPORT,
            self::JSON,
            self::SHOPIFY,
            self::SFCC,
        ];
    }
}
