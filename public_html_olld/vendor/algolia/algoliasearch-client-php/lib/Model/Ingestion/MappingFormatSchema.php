<?php

// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.

namespace Algolia\AlgoliaSearch\Model\Ingestion;

/**
 * MappingFormatSchema Class Doc Comment.
 *
 * @category Class
 *
 * @description Mapping format schema.
 */
class MappingFormatSchema
{
    /**
     * Possible values of this enum.
     */
    public const MAPPINGKIT_V1 = 'mappingkit/v1';

    /**
     * Gets allowable values of the enum.
     *
     * @return string[]
     */
    public static function getAllowableEnumValues()
    {
        return [
            self::MAPPINGKIT_V1,
        ];
    }
}
