<?php

// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.

namespace Algolia\AlgoliaSearch\Model\Ingestion;

/**
 * RunReasonCode Class Doc Comment.
 *
 * @category Class
 *
 * @description A code for the task run&#39;s outcome. A readable description of the code is included in the &#x60;reason&#x60; response property.
 */
class RunReasonCode
{
    /**
     * Possible values of this enum.
     */
    public const INTERNAL = 'internal';

    public const CRITICAL = 'critical';

    public const NO_EVENTS = 'no_events';

    public const TOO_MANY_ERRORS = 'too_many_errors';

    public const OK = 'ok';

    public const DISCARDED = 'discarded';

    public const BLOCKING = 'blocking';

    /**
     * Gets allowable values of the enum.
     *
     * @return string[]
     */
    public static function getAllowableEnumValues()
    {
        return [
            self::INTERNAL,
            self::CRITICAL,
            self::NO_EVENTS,
            self::TOO_MANY_ERRORS,
            self::OK,
            self::DISCARDED,
            self::BLOCKING,
        ];
    }
}
