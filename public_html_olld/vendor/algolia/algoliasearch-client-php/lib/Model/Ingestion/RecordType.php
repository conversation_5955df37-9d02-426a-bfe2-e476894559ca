<?php

// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.

namespace Algolia\AlgoliaSearch\Model\Ingestion;

/**
 * RecordType Class Doc Comment.
 *
 * @category Class
 *
 * @description Record type for ecommerce sources.
 */
class RecordType
{
    /**
     * Possible values of this enum.
     */
    public const PRODUCT = 'product';

    public const VARIANT = 'variant';

    /**
     * Gets allowable values of the enum.
     *
     * @return string[]
     */
    public static function getAllowableEnumValues()
    {
        return [
            self::PRODUCT,
            self::VARIANT,
        ];
    }
}
