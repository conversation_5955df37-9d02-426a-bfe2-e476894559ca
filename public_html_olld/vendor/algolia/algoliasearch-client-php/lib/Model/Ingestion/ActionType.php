<?php

// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.

namespace Algolia\AlgoliaSearch\Model\Ingestion;

/**
 * ActionType Class Doc Comment.
 *
 * @category Class
 *
 * @description Action to perform on the Algolia index.
 */
class ActionType
{
    /**
     * Possible values of this enum.
     */
    public const REPLACE = 'replace';

    public const SAVE = 'save';

    public const PARTIAL = 'partial';

    public const APPEND = 'append';

    /**
     * Gets allowable values of the enum.
     *
     * @return string[]
     */
    public static function getAllowableEnumValues()
    {
        return [
            self::REPLACE,
            self::SAVE,
            self::PARTIAL,
            self::APPEND,
        ];
    }
}
