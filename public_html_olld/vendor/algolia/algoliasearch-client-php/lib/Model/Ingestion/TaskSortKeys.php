<?php

// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.

namespace Algolia\AlgoliaSearch\Model\Ingestion;

/**
 * TaskSortKeys Class Doc Comment.
 *
 * @category Class
 *
 * @description Property by which to sort the list of tasks.
 */
class TaskSortKeys
{
    /**
     * Possible values of this enum.
     */
    public const ENABLED = 'enabled';

    public const TRIGGER_TYPE = 'triggerType';

    public const ACTION = 'action';

    public const UPDATED_AT = 'updatedAt';

    public const CREATED_AT = 'createdAt';

    /**
     * Gets allowable values of the enum.
     *
     * @return string[]
     */
    public static function getAllowableEnumValues()
    {
        return [
            self::ENABLED,
            self::TRIGGER_TYPE,
            self::ACTION,
            self::UPDATED_AT,
            self::CREATED_AT,
        ];
    }
}
