<?php

// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.

namespace Algolia\AlgoliaSearch\Model\Ingestion;

use Algolia\AlgoliaSearch\Model\AbstractModel;

/**
 * TransformationTry Class Doc Comment.
 *
 * @category Class
 */
class TransformationTry extends AbstractModel implements ModelInterface, \ArrayAccess, \JsonSerializable
{
    /**
     * Array of property to type mappings. Used for (de)serialization.
     *
     * @var string[]
     */
    protected static $modelTypes = [
        'code' => 'string',
        'sampleRecord' => 'object',
        'authentications' => '\Algolia\AlgoliaSearch\Model\Ingestion\AuthenticationCreate[]',
    ];

    /**
     * Array of property to format mappings. Used for (de)serialization.
     *
     * @var string[]
     */
    protected static $modelFormats = [
        'code' => null,
        'sampleRecord' => null,
        'authentications' => null,
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name.
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'code' => 'code',
        'sampleRecord' => 'sampleRecord',
        'authentications' => 'authentications',
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses).
     *
     * @var string[]
     */
    protected static $setters = [
        'code' => 'setCode',
        'sampleRecord' => 'setSampleRecord',
        'authentications' => 'setAuthentications',
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests).
     *
     * @var string[]
     */
    protected static $getters = [
        'code' => 'getCode',
        'sampleRecord' => 'getSampleRecord',
        'authentications' => 'getAuthentications',
    ];

    /**
     * Associative array for storing property values.
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor.
     *
     * @param mixed[] $data Associated array of property values
     */
    public function __construct(?array $data = null)
    {
        if (isset($data['code'])) {
            $this->container['code'] = $data['code'];
        }
        if (isset($data['sampleRecord'])) {
            $this->container['sampleRecord'] = $data['sampleRecord'];
        }
        if (isset($data['authentications'])) {
            $this->container['authentications'] = $data['authentications'];
        }
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name.
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of property to type mappings. Used for (de)serialization.
     *
     * @return array
     */
    public static function modelTypes()
    {
        return self::$modelTypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization.
     *
     * @return array
     */
    public static function modelFormats()
    {
        return self::$modelFormats;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses).
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests).
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        if (!isset($this->container['code']) || null === $this->container['code']) {
            $invalidProperties[] = "'code' can't be null";
        }
        if (!isset($this->container['sampleRecord']) || null === $this->container['sampleRecord']) {
            $invalidProperties[] = "'sampleRecord' can't be null";
        }

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed.
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return 0 === count($this->listInvalidProperties());
    }

    /**
     * Gets code.
     *
     * @return string
     */
    public function getCode()
    {
        return $this->container['code'] ?? null;
    }

    /**
     * Sets code.
     *
     * @param string $code the source code of the transformation
     *
     * @return self
     */
    public function setCode($code)
    {
        $this->container['code'] = $code;

        return $this;
    }

    /**
     * Gets sampleRecord.
     *
     * @return object
     */
    public function getSampleRecord()
    {
        return $this->container['sampleRecord'] ?? null;
    }

    /**
     * Sets sampleRecord.
     *
     * @param object $sampleRecord the record to apply the given code to
     *
     * @return self
     */
    public function setSampleRecord($sampleRecord)
    {
        $this->container['sampleRecord'] = $sampleRecord;

        return $this;
    }

    /**
     * Gets authentications.
     *
     * @return null|\Algolia\AlgoliaSearch\Model\Ingestion\AuthenticationCreate[]
     */
    public function getAuthentications()
    {
        return $this->container['authentications'] ?? null;
    }

    /**
     * Sets authentications.
     *
     * @param null|\Algolia\AlgoliaSearch\Model\Ingestion\AuthenticationCreate[] $authentications authentications
     *
     * @return self
     */
    public function setAuthentications($authentications)
    {
        $this->container['authentications'] = $authentications;

        return $this;
    }

    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param int $offset Offset
     *
     * @return bool
     */
    public function offsetExists($offset)
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param int $offset Offset
     *
     * @return null|mixed
     */
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param null|int $offset Offset
     * @param mixed    $value  Value to be set
     */
    public function offsetSet($offset, $value)
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param int $offset Offset
     */
    public function offsetUnset($offset)
    {
        unset($this->container[$offset]);
    }
}
