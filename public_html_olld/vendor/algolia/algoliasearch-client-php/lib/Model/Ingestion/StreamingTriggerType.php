<?php

// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.

namespace Algolia\AlgoliaSearch\Model\Ingestion;

/**
 * StreamingTriggerType Class Doc Comment.
 *
 * @category Class
 *
 * @description Task runs continuously.
 */
class StreamingTriggerType
{
    /**
     * Possible values of this enum.
     */
    public const STREAMING = 'streaming';

    /**
     * Gets allowable values of the enum.
     *
     * @return string[]
     */
    public static function getAllowableEnumValues()
    {
        return [
            self::STREAMING,
        ];
    }
}
