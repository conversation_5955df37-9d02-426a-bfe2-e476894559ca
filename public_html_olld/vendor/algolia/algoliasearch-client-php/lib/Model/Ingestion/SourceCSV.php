<?php

// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.

namespace Algolia\AlgoliaSearch\Model\Ingestion;

use Algolia\AlgoliaSearch\Model\AbstractModel;

/**
 * SourceCSV Class Doc Comment.
 *
 * @category Class
 */
class SourceCSV extends AbstractModel implements ModelInterface, \ArrayAccess, \JsonSerializable
{
    /**
     * Array of property to type mappings. Used for (de)serialization.
     *
     * @var string[]
     */
    protected static $modelTypes = [
        'url' => 'string',
        'uniqueIDColumn' => 'string',
        'mapping' => 'array<string,\Algolia\AlgoliaSearch\Model\Ingestion\MappingTypeCSV>',
        'method' => '\Algolia\AlgoliaSearch\Model\Ingestion\MethodType',
        'delimiter' => 'string',
    ];

    /**
     * Array of property to format mappings. Used for (de)serialization.
     *
     * @var string[]
     */
    protected static $modelFormats = [
        'url' => null,
        'uniqueIDColumn' => null,
        'mapping' => null,
        'method' => null,
        'delimiter' => null,
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name.
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'url' => 'url',
        'uniqueIDColumn' => 'uniqueIDColumn',
        'mapping' => 'mapping',
        'method' => 'method',
        'delimiter' => 'delimiter',
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses).
     *
     * @var string[]
     */
    protected static $setters = [
        'url' => 'setUrl',
        'uniqueIDColumn' => 'setUniqueIDColumn',
        'mapping' => 'setMapping',
        'method' => 'setMethod',
        'delimiter' => 'setDelimiter',
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests).
     *
     * @var string[]
     */
    protected static $getters = [
        'url' => 'getUrl',
        'uniqueIDColumn' => 'getUniqueIDColumn',
        'mapping' => 'getMapping',
        'method' => 'getMethod',
        'delimiter' => 'getDelimiter',
    ];

    /**
     * Associative array for storing property values.
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor.
     *
     * @param mixed[] $data Associated array of property values
     */
    public function __construct(?array $data = null)
    {
        if (isset($data['url'])) {
            $this->container['url'] = $data['url'];
        }
        if (isset($data['uniqueIDColumn'])) {
            $this->container['uniqueIDColumn'] = $data['uniqueIDColumn'];
        }
        if (isset($data['mapping'])) {
            $this->container['mapping'] = $data['mapping'];
        }
        if (isset($data['method'])) {
            $this->container['method'] = $data['method'];
        }
        if (isset($data['delimiter'])) {
            $this->container['delimiter'] = $data['delimiter'];
        }
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name.
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of property to type mappings. Used for (de)serialization.
     *
     * @return array
     */
    public static function modelTypes()
    {
        return self::$modelTypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization.
     *
     * @return array
     */
    public static function modelFormats()
    {
        return self::$modelFormats;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses).
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests).
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        if (!isset($this->container['url']) || null === $this->container['url']) {
            $invalidProperties[] = "'url' can't be null";
        }
        if (isset($this->container['delimiter']) && (mb_strlen($this->container['delimiter']) > 1)) {
            $invalidProperties[] = "invalid value for 'delimiter', the character length must be smaller than or equal to 1.";
        }

        if (isset($this->container['delimiter']) && (mb_strlen($this->container['delimiter']) < 1)) {
            $invalidProperties[] = "invalid value for 'delimiter', the character length must be bigger than or equal to 1.";
        }

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed.
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return 0 === count($this->listInvalidProperties());
    }

    /**
     * Gets url.
     *
     * @return string
     */
    public function getUrl()
    {
        return $this->container['url'] ?? null;
    }

    /**
     * Sets url.
     *
     * @param string $url URL of the file
     *
     * @return self
     */
    public function setUrl($url)
    {
        $this->container['url'] = $url;

        return $this;
    }

    /**
     * Gets uniqueIDColumn.
     *
     * @return null|string
     */
    public function getUniqueIDColumn()
    {
        return $this->container['uniqueIDColumn'] ?? null;
    }

    /**
     * Sets uniqueIDColumn.
     *
     * @param null|string $uniqueIDColumn name of a column that contains a unique ID which will be used as `objectID` in Algolia
     *
     * @return self
     */
    public function setUniqueIDColumn($uniqueIDColumn)
    {
        $this->container['uniqueIDColumn'] = $uniqueIDColumn;

        return $this;
    }

    /**
     * Gets mapping.
     *
     * @return null|array<string,\Algolia\AlgoliaSearch\Model\Ingestion\MappingTypeCSV>
     */
    public function getMapping()
    {
        return $this->container['mapping'] ?? null;
    }

    /**
     * Sets mapping.
     *
     * @param null|array<string,\Algolia\AlgoliaSearch\Model\Ingestion\MappingTypeCSV> $mapping key-value pairs of column names and their expected types
     *
     * @return self
     */
    public function setMapping($mapping)
    {
        $this->container['mapping'] = $mapping;

        return $this;
    }

    /**
     * Gets method.
     *
     * @return null|MethodType
     */
    public function getMethod()
    {
        return $this->container['method'] ?? null;
    }

    /**
     * Sets method.
     *
     * @param null|MethodType $method method
     *
     * @return self
     */
    public function setMethod($method)
    {
        $this->container['method'] = $method;

        return $this;
    }

    /**
     * Gets delimiter.
     *
     * @return null|string
     */
    public function getDelimiter()
    {
        return $this->container['delimiter'] ?? null;
    }

    /**
     * Sets delimiter.
     *
     * @param null|string $delimiter the character used to split the value on each line, default to a comma (\\r, \\n, 0xFFFD, and space are forbidden)
     *
     * @return self
     */
    public function setDelimiter($delimiter)
    {
        if (!is_null($delimiter) && (mb_strlen($delimiter) > 1)) {
            throw new \InvalidArgumentException('invalid length for $delimiter when calling SourceCSV., must be smaller than or equal to 1.');
        }
        if (!is_null($delimiter) && (mb_strlen($delimiter) < 1)) {
            throw new \InvalidArgumentException('invalid length for $delimiter when calling SourceCSV., must be bigger than or equal to 1.');
        }

        $this->container['delimiter'] = $delimiter;

        return $this;
    }

    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param int $offset Offset
     *
     * @return bool
     */
    public function offsetExists($offset)
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param int $offset Offset
     *
     * @return null|mixed
     */
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param null|int $offset Offset
     * @param mixed    $value  Value to be set
     */
    public function offsetSet($offset, $value)
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param int $offset Offset
     */
    public function offsetUnset($offset)
    {
        unset($this->container[$offset]);
    }
}
