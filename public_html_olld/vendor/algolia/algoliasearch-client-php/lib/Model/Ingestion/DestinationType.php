<?php

// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.

namespace Algolia\AlgoliaSearch\Model\Ingestion;

/**
 * DestinationType Class Doc Comment.
 *
 * @category Class
 *
 * @description Destination type.  - &#x60;search&#x60;.   Data is stored in an Algolia index.  - &#x60;insights&#x60;.   Data is recorded as user events in the Insights API.
 */
class DestinationType
{
    /**
     * Possible values of this enum.
     */
    public const SEARCH = 'search';

    public const INSIGHTS = 'insights';

    /**
     * Gets allowable values of the enum.
     *
     * @return string[]
     */
    public static function getAllowableEnumValues()
    {
        return [
            self::SEARCH,
            self::INSIGHTS,
        ];
    }
}
