<?php

// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.

namespace Algolia\AlgoliaSearch\Model\Analytics;

/**
 * Operator Class Doc Comment.
 *
 * @category Class
 *
 * @description Character that characterizes how the filter is applied.  For example, for a facet filter &#x60;facet:value&#x60;, &#x60;:&#x60; is the operator. For a numeric filter &#x60;count&gt;50&#x60;, &#x60;&gt;&#x60; is the operator.
 */
class Operator
{
    /**
     * Possible values of this enum.
     */
    public const COLON = ':';

    public const LESS_THAN = '<';

    public const LESS_THAN_OR_EQUAL_TO = '<=';

    public const EQUAL = '=';

    public const NOT_EQUAL = '!=';

    public const GREATER_THAN = '>';

    public const GREATER_THAN_OR_EQUAL_TO = '>=';

    /**
     * Gets allowable values of the enum.
     *
     * @return string[]
     */
    public static function getAllowableEnumValues()
    {
        return [
            self::COLON,
            self::LESS_THAN,
            self::LESS_THAN_OR_EQUAL_TO,
            self::EQUAL,
            self::NOT_EQUAL,
            self::GREATER_THAN,
            self::GREATER_THAN_OR_EQUAL_TO,
        ];
    }
}
