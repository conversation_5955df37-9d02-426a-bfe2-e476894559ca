<?php

// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.

namespace Algolia\AlgoliaSearch\Model\Analytics;

/**
 * OrderBy Class Doc Comment.
 *
 * @category Class
 *
 * @description Attribute by which to order the response items.  If the &#x60;clickAnalytics&#x60; parameter is false, only &#x60;searchCount&#x60; is available.
 */
class OrderBy
{
    /**
     * Possible values of this enum.
     */
    public const SEARCH_COUNT = 'searchCount';

    public const CLICK_THROUGH_RATE = 'clickThroughRate';

    public const CONVERSION_RATE = 'conversionRate';

    public const AVERAGE_CLICK_POSITION = 'averageClickPosition';

    /**
     * Gets allowable values of the enum.
     *
     * @return string[]
     */
    public static function getAllowableEnumValues()
    {
        return [
            self::SEARCH_COUNT,
            self::CLICK_THROUGH_RATE,
            self::CONVERSION_RATE,
            self::AVERAGE_CLICK_POSITION,
        ];
    }
}
