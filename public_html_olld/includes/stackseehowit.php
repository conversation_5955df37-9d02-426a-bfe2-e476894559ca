<div class="slider-pad container-xs">
<div class="fix-slider-slider-container">
  <div class="fix-slider-text-section">
    <div class="fix-slider-main-title">
      Shipping your car with Safe Car Hauler in 3 easy steps!
    </div>

    <div class="fix-slider-steps">
      <button class="fix-slider-step-button active" onclick="showContent(1)">Step 1</button>
      <button class="fix-slider-step-button" onclick="showContent(2)">Step 2</button>
      <button class="fix-slider-step-button" onclick="showContent(3)">Step 3</button>
    </div>

    <div class="fix-slider-content active" id="content-1">
      <div class="fix-slider-title">Get your quote and book with ease</div>
      <div class="description-11 ui text size-textmd">
        Get a quick quote online or by calling
        <a href="tel:8778788008" class="getaninstant-span-call2">(*************</a>.
        Choose from a variety of transport options and book through phone or email.
        Once you confirm, your shipment is matched with a fully insured and vetted carrier from our trusted network.
      </div>
    </div>

    <div class="fix-slider-content" id="content-2">
      <div class="fix-slider-title">Seamless vehicle pickup</div>
      <div class="description-11 ui text size-textmd">
        Your vehicle is picked up from your chosen location, with a thorough inspection conducted and documented on the Bill of Lading to ensure accuracy.
      </div>
    </div>

    <div class="fix-slider-content" id="content-3">
      <div class="fix-slider-title">Safe and reliable delivery</div>
      <div class="description-11 ui text size-textmd">
        We keep you informed throughout transit. Before arrival, you’ll be notified of the delivery timing, and a final inspection is conducted upon drop-off to ensure your vehicle arrives in the same condition as when it was picked up.
      </div>
    </div>
  </div>

  <div class="fix-slider-image-section">
    <img src="../public/images/Book2.png" id="step-image" alt="Step Image">
  </div>
</div>
</div>
<script>
  function showContent(step) {
    document.querySelectorAll('.fix-slider-step-button').forEach((button, index) => {
      button.classList.toggle('active', index + 1 === step);
    });

    document.querySelectorAll('.fix-slider-content').forEach((content, index) => {
      content.classList.toggle('active', index + 1 === step);
    });

    const image = document.getElementById('step-image');
    if (step === 1) {
      image.src = "../public/images/Book2.png";
    } else if (step === 2) {
      image.src = "../public/images/Truck1.png";
    } else if (step === 3) {
      image.src = "../public/images/CarKey1.png";
    }
  }
</script>