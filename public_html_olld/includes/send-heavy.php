<div id="notification" class="notification" style="display: none;">Order Sent Successfully!</div>
<!-- Step 1: From/To form -->
<div class="step" id="step-1">
                <div class="input-group">
                  <div class="input-wrapper">
                    <img src="public/images/Vector.svg" alt="Location Icon" class="icon" />
                    <input type="text" name="transport_from" id="transport_from" class="from-input" placeholder="From (ZIP or City, State)" />
                  </div>
                  <div class="input-wrapper">
                    <img src="public/images/Vector-2.svg" alt="Location Icon" class="icon" />
                    <input type="text" name="transport_to" id="transport_to" class="to-input" placeholder="To (ZIP or City, State)" />
                  </div>
                </div>
                <div class="rowtransporttyp">
                </div>

                <button type="button" class="step-buttom" onclick="nextStep(2)">Vehicle Details</button>
              </div>

              <!-- Step 2: Vehicle details -->
              <div class="step" id="step-2" style="display: none;">
                <div class="input-group">
                <div class="select-custom">
            <select name="vehicle_year" id="vehicle_year" class="styled-select">
                <option value="" disabled selected>Vehicle year</option>
            </select>
            <span class="dropdown-icon"></span>
        </div>

        <!-- Brand Dropdown -->
        <div class="input-wrapper">
        <input type="text" name="vehicle_brand" id="vehicle_brand" class="from-input pac-target-input" placeholder="Vehicle Make">
        </div>

        <!-- Model Dropdown -->
        <div class="input-wrapper">
        <input type="text" name="vehicle_model" id="vehicle_model" class="from-input pac-target-input" placeholder="Vehicle Model">
        </div>
                </div>
                <label class="vehicle-type">Is it operable?</label>
                <div class="rowtransporttyp">
                <label class="ui checkbox">
                     <input type="radio" name="vehicle_operable" value="yes" />
                <div></div><span>Yes</span>
               </label>
                 <label class="ui checkbox">
                  <input type="radio" name="vehicle_operable" value="no" />
                    <div></div><span>No</span>
                     </label>
                   </div>
                <div class="step-indicators">
                  <div class="indicator completed-step" id="step-indicator-1" onclick="goToStep(1)">
                    <span class="indicator-number">1</span>
                    <p>Destination</p>
                  </div>
                  <hr class="indicator-line">
                  <div class="indicator active-step" id="step-indicator-2">
                    <span class="indicator-number">2</span>
                    <p>Vehicle</p>
                  </div>
                  <hr class="indicator-line">
                  <div class="indicator" id="step-indicator-3">
                    <span class="indicator-number">3</span>
                    <p>Info</p>
                  </div>
                </div>
                <button type="button" class="step-buttom" onclick="nextStep(3)">Confirmation Details</button>
              </div>

              <!-- Step 3: Contact details -->
              <div class="step" id="step-3" style="display: none;">
                <div class="input-group">
                  <div class="input-wrapper">
                    <input type="email" name="email" id="email" class="info-input" placeholder="Your Email" />
                  </div>
                  <div class="select-custom">
                   <select name="available_date" id="available_date" class="styled-select">
                   <option value="asap">As soon as possible</option>
                   <option value="2_weeks">Within 2 weeks</option>
                   <option value="30_days">Within 30 days</option>
                   <option value="more_than_30_days">More than 30 days</option>
                   </select>
                   <span class="dropdown-icon"></span>
                  </div>
                  <div class="input-wrapper">
                    <input type="text" name="name" id="name" class="info-input" placeholder="Your name" />
                  </div>
                  <div class="input-wrapper">
                    <input type="tel" name="phone" id="phone" class="info-input" placeholder="Your phone" />
                  </div>
                </div>
                <div class="step-indicators">
                  <div class="indicator completed-step" id="step-indicator-1" onclick="goToStep(1)">
                    <span class="indicator-number">1</span>
                    <p>Destination</p>
                  </div>
                  <hr class="indicator-line">
                  <div class="indicator completed-step" id="step-indicator-2"  onclick="goToStep(2)">
                    <span class="indicator-number">2</span>
                    <p>Vehicle</p>
                  </div>
                  <hr class="indicator-line">
                  <div class="indicator active-step" id="step-indicator-3">
                    <span class="indicator-number">3</span>
                    <p>Info</p>
                  </div>
                </div>
                <button type="submit" class="step-buttom" id="finishButton">Get Quote</button>
              </div>
              
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
function initializeAutocomplete(inputElement) {
    const autocomplete = new google.maps.places.Autocomplete(inputElement, {
        types: ['(regions)'], 
        componentRestrictions: { country: 'us' } 
    });

    autocomplete.addListener('place_changed', function () {
        const place = autocomplete.getPlace();
        if (place.address_components) {
            let city = '';
            let state = '';
            let zip = '';

            place.address_components.forEach(component => {
                const types = component.types;
                if (types.includes('locality') || types.includes('postal_town')) {
                    city = component.long_name;
                }
                if (types.includes('administrative_area_level_1')) {
                    state = component.short_name;
                }
                if (types.includes('postal_code')) {
                    zip = component.long_name;
                }
            });

            const formatted = `${city}, ${state} ${zip}, USA`;
            $(inputElement).val(formatted);
        }
    });
}

function checkGoogleAPIAndInitialize() {
    if (typeof google === 'undefined' || typeof google.maps === 'undefined') {
        setTimeout(checkGoogleAPIAndInitialize, 500); 
    } else {
        initializeAutocomplete(document.getElementById('transport_from'));
        initializeAutocomplete(document.getElementById('transport_to'));
    }
}

checkGoogleAPIAndInitialize();
</script>