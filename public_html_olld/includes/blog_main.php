<div class="column_five">
  <div class="columnmostrecen container-xs">
    <h2 class="ui heading size-headingxl two">Most Recent Articles</h2>
    
    <div class="listview">
      <?php if (count($blogs) > 0): ?>
        <?php foreach ($blogs as $blog): ?>
          <div class="columnview">
            
            <!-- Article Image and Link -->
            <div class="stackview">
              <div class="image-container">
                <img src="../uploads/<?php echo htmlspecialchars($blog['image']); ?>" alt="<?php echo htmlspecialchars($blog['title']); ?>" class="image-1" />
                <a href="blog_details.php?id=<?php echo $blog['id']; ?>">
                  <div class="view"></div>
                </a>
              </div>
            </div>
            
            <!-- Article Information -->
            <div class="columnbuyingaca">
              <a href="blog_details.php?id=<?php echo $blog['id']; ?>" class="read-more">
                <p class="buyingacar ui text size-textlg"><?php echo htmlspecialchars($blog['title']); ?></p>
              </a>
              <p class="description-13 ui text size-texts">
                <?php 
                  $description = htmlspecialchars($blog['short_description']);
                  echo strlen($description) > 130 ? substr($description, 0, 130) . '...' : $description;
                ?>
              </p>
              
              <!-- Read More Button and Date -->
              <div class="rowreadmore">
                <a href="blog_details.php?id=<?php echo $blog['id']; ?>" class="read-more">
                  <p class="showmore ui text size-texts">Read more →</p>
                </a>
                <p class="july162024 ui text size-textxs">
                  <?php echo date("F d, Y", strtotime($blog['created_at'])); ?>
                </p>
              </div>
            </div>
            
          </div>
        <?php endforeach; ?>
      <?php else: ?>
        <p>ბლოგები არ არის</p>
      <?php endif; ?>
    </div>
    
    <!-- See All Button -->
    <button class="flex-row-center-center see_all" onclick="redirectToBlog()">See All</button>
  </div>
</div>
