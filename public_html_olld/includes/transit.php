<link rel="stylesheet" type="text/css" href="public/css/transit.css" />
<div class="container-xs">
  <div class="order-fix-slider">
    <h2 class="order-fix-title">Recent Loads Transported</h2>

    <div class="order-fix-slides-container" id="slides-container">
      
      <!-- Slide 1 -->
      <div class="order-fix-slide">
        <div class="order-fix-card" id="card-1">
          <img src="public/images/loads/1937-Buick-Sedan.jpg" alt="1937 Buick Sedan Transported 550 Miles - safecarhauler.com">
          <div class="order-fix-info-section">
            <h2 class="order-fix-h2">1937 Buick Sedan Transported 550 Miles</h2>
            <br>
            <p><strong>Origin:</strong> Middleburg, PA 17842</p>
            <p><strong>Destination:</strong> Fernley, NV 89408</p>
            <p><strong>Service:</strong> <a href="classic-car-transport" class="getaninstant-span-link">Classic Car Transport</a></p>
          </div>
        </div>
      </div>

      <!-- Slide 2 -->
      <div class="order-fix-slide">
        <div class="order-fix-card" id="card-2">
          <img src="public/images/loads/2021-Ford-F-350-Super-Duty.jpg" alt="2021 Ford F-350 Super Duty Transported 630 Miles - safecarhauler.com">
          <div class="order-fix-info-section">
            <h2 class="order-fix-h2">2021 Ford F-350 Super Duty Transported 630 Miles</h2>
            <br>
            <p><strong>Origin:</strong> Quapaw, OK 74363</p>
            <p><strong>Destination:</strong> Waukegan, IL 60085</p>
            <p><strong>Service:</strong> <a href="open-auto-transport" class="getaninstant-span-link">Open Auto Transport</a></p>
          </div>
        </div>
      </div>

      <!-- Slide 3 -->
      <div class="order-fix-slide">
        <div class="order-fix-card" id="card-3">
          <img src="public/images/loads/2018-BMW-430-Coupe.jpg" alt="2018 BMW 430 Coupe Transported 880 Miles - safecarhauler.com">
          <div class="order-fix-info-section">
            <h2 class="order-fix-h2">2018 BMW 430 Coupe Transported 880 Miles</h2>
            <br>
            <p><strong>Origin:</strong> Atlanta, GA 30331</p>
            <p><strong>Destination:</strong> Brooklyn, NY 11203</p>
            <p><strong>Service:</strong> <a href="enclosed-auto-transport" class="getaninstant-span-link">Enclosed Auto Transport</a></p>
          </div>
        </div>
      </div>

      <!-- Slide 4 -->
      <div class="order-fix-slide">
        <div class="order-fix-card" id="card-4">
          <img src="public/images/loads/2024-Tesla-Cybertruck.jpg" alt="2024 Tesla Cybertruck Transported 1720 Miles - safecarhauler.com">
          <div class="order-fix-info-section">
            <h2 class="order-fix-h2">2024 Tesla Cybertruck Transported 1720 Miles</h2>
            <br>
            <p><strong>Origin:</strong> Dale, TX 78616</p>
            <p><strong>Destination:</strong> Langhorne, PA 19047</p>
            <p><strong>Service:</strong> <a href="enclosed-auto-transport" class="getaninstant-span-link">Enclosed Auto Transport</a></p>
          </div>
        </div>
      </div>

      <!-- Slide 5 -->
      <div class="order-fix-slide">
        <div class="order-fix-card" id="card-5">
          <img src="public/images/loads/2024-Dodge-Ram-5500.jpg" alt="2024 Dodge Ram 5500 Transported 640 Miles - safecarhauler.com">
          <div class="order-fix-info-section">
            <h2 class="order-fix-h2">2024 Dodge Ram 5500 Transported 640 Miles</h2>
            <br>
            <p><strong>Origin:</strong> Conway, SC 29526</p>
            <p><strong>Destination:</strong> Secaucus, NJ 07094</p>
            <p><strong>Service:</strong> <a href="heavy-duty-truck-transport" class="getaninstant-span-link">Heavy Duty Truck Transport</a></p>
          </div>
        </div>
      </div>

      <!-- Slide 6 -->
      <div class="order-fix-slide">
        <div class="order-fix-card" id="card-6">
          <img src="public/images/loads/2012-BMW-6-Series-Coupe.jpg" alt="2012 BMW 6 Series Coupe Transported 300 Miles - safecarhauler.com">
          <div class="order-fix-info-section">
            <h2 class="order-fix-h2">2012 BMW 6 Series Coupe Transported 300 Miles</h2>
            <br>
            <p><strong>Origin:</strong> Fredericksburg, VA 22406</p>
            <p><strong>Destination:</strong> Brooklyn, NY 11203</p>
            <p><strong>Service:</strong> <a href="open-auto-transport" class="getaninstant-span-link">Open Auto Transport</a></p>
          </div>
        </div>
      </div>

      <!-- Slide 7 -->
      <div class="order-fix-slide">
        <div class="order-fix-card" id="card-7">
          <img src="public/images/loads/2019-McLaren-720S.jpg" alt="2019 McLaren 720S Transported 2800 Miles - safecarhauler.com">
          <div class="order-fix-info-section">
            <h2 class="order-fix-h2">2019 McLaren 720S Transported 2800 Miles</h2>
            <br>
            <p><strong>Origin:</strong> Riverview, FL 33578</p>
            <p><strong>Destination:</strong> Hayward, CA 94545</p>
            <p><strong>Service:</strong> <a href="exotic-car-transport" class="getaninstant-span-link">Exotic Car Transport</a></p>
          </div>
        </div>
      </div>

      <!-- Slide 8 -->
      <div class="order-fix-slide">
        <div class="order-fix-card" id="card-8">
          <img src="public/images/loads/1971-Chevrolet-Camaro-Z28.jpg" alt="1971 Chevrolet Camaro Z28 Transported 1120 Miles - safecarhauler.com">
          <div class="order-fix-info-section">
            <h2 class="order-fix-h2">1971 Chevrolet Camaro Z28 Transported 1120 Miles</h2>
            <br>
            <p><strong>Origin:</strong> Sun Valley, CA 91352</p>
            <p><strong>Destination:</strong> Cashmere, WA 98815</p>
            <p><strong>Service:</strong> <a href="classic-car-transport" class="getaninstant-span-link">Classic Car Transport</a></p>
          </div>
        </div>
      </div>

      <!-- Slide 9 -->
      <div class="order-fix-slide">
        <div class="order-fix-card" id="card-9">
          <img src="public/images/loads/2021-Bentley-Flying-Spur.jpg" alt="2021 Bentley Flying Spur Transported 1170 Miles - safecarhauler.com">
          <div class="order-fix-info-section">
            <h2 class="order-fix-h2">2021 Bentley Flying Spur Transported 1170 Miles</h2>
            <br>
            <p><strong>Origin:</strong> Warren, MI 48091</p>
            <p><strong>Destination:</strong> Winter Park, FL 32789</p>
            <p><strong>Service:</strong> <a href="exotic-car-transport" class="getaninstant-span-link">Exotic Car Transport</a></p>
          </div>
        </div>
      </div>

      <!-- Slide 10 -->
      <div class="order-fix-slide">
        <div class="order-fix-card" id="card-10">
          <img src="public/images/loads/2020-Mercedes-Benz-G63-AMG.jpg" alt="2020 Mercedes-Benz G63 AMG Transported 2830 Miles - safecarhauler.com">
          <div class="order-fix-info-section">
            <h2 class="order-fix-h2">2020 Mercedes-Benz G63 AMG Transported 2830 Miles</h2>
            <br>
            <p><strong>Origin:</strong> Los Angeles, CA 90023</p>
            <p><strong>Destination:</strong> Long Beach, NY 11561</p>
            <p><strong>Service:</strong> <a href="enclosed-auto-transport" class="getaninstant-span-link">Enclosed Auto Transport</a></p>
          </div>
        </div>
      </div>

      <!-- Slide 11 -->
      <div class="order-fix-slide">
        <div class="order-fix-card" id="card-11">
          <img src="public/images/loads/2017-Aston-Martin-DB11.jpg" alt="2017 Aston Martin DB11 Transported 900 Miles - safecarhauler.com">
          <div class="order-fix-info-section">
            <h2 class="order-fix-h2">2017 Aston Martin DB11 Transported 900 Miles</h2>
            <br>
            <p><strong>Origin:</strong> Chicago, IL 60661</p>
            <p><strong>Destination:</strong> Springfield, MA 01107</p>
            <p><strong>Service:</strong> <a href="enclosed-auto-transport" class="getaninstant-span-link">Enclosed Auto Transport</a></p>
          </div>
        </div>
      </div>

      <!-- Slide 12 -->
      <div class="order-fix-slide">
        <div class="order-fix-card" id="card-12">
          <img src="public/images/loads/1957-Chevrolet-Bel-Air.jpg" alt="1957 Chevrolet Bel Air Transported 650 Miles - safecarhauler.com">
          <div class="order-fix-info-section">
            <h2 class="order-fix-h2">1957 Chevrolet Bel Air Transported 650 Miles</h2>
            <br>
            <p><strong>Origin:</strong> Mount Bethel, PA 18343</p>
            <p><strong>Destination:</strong> Spartanburg, SC 29301</p>
            <p><strong>Service:</strong> <a href="classic-car-transport" class="getaninstant-span-link">Classic Car Transport</a></p>
          </div>
        </div>
      </div>
    </div>

    <!-- Slider Navigation -->
    <div class="order-fix-slider-indicators" id="slider-indicators"></div>
    <div class="order-fix-slider-controls order-fix-slider-controls-left">
      <img src="../public/images/left-icon.svg" class="transit-icon" alt="Left Arrow">
    </div>
    <div class="order-fix-slider-controls order-fix-slider-controls-right">
      <img src="../public/images/right-icon.svg" class="transit-icon" alt="Right Arrow">
    </div>
  </div>
</div>
<script>
let currentIndex = 0;
const slidesContainer = document.getElementById('slides-container');
const slides = document.querySelectorAll('.order-fix-slide');
const slideCount = slides.length;

function shuffleSlides() {
  const slidesArray = Array.from(slides);
  for (let i = slidesArray.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    slidesContainer.appendChild(slidesArray[j]); 
  }
}

shuffleSlides();

function getSlideWidth() {
  return slides[0].offsetWidth;
}

function updateSlider() {
  const slideWidth = getSlideWidth();
  
  slidesContainer.style.transition = 'transform 0.5s ease-in-out';
  slidesContainer.style.transform = `translateX(-${currentIndex * slideWidth}px)`;
  updateIndicators();

  if (currentIndex >= slideCount) {
    setTimeout(() => {
      slidesContainer.style.transition = 'none';
      currentIndex = 0;
      slidesContainer.style.transform = `translateX(0)`;
    }, 500);
  }

  if (currentIndex < 0) {
    setTimeout(() => {
      slidesContainer.style.transition = 'none';
      currentIndex = slideCount - 1;
      slidesContainer.style.transform = `translateX(-${currentIndex * slideWidth}px)`;
    }, 500);
  }
}

function nextSlide() {
  currentIndex = (currentIndex + 1); 
  updateSlider();
}

function prevSlide() {
  currentIndex = (currentIndex - 1);
  updateSlider();
}

const firstClone = slides[3].cloneNode(true);
const lastClone = slides[slides.length - 7].cloneNode(true);

slidesContainer.appendChild(firstClone); 
slidesContainer.insertBefore(lastClone, slides[4]); 
const slidesWithClones = document.querySelectorAll('.order-fix-slide');

function createIndicators() {
  const indicatorsContainer = document.getElementById('slider-indicators');
  indicatorsContainer.innerHTML = '';

  slides.forEach((_, index) => {
    const indicator = document.createElement('div');
    indicator.classList.add('order-fix-slider-indicator');
    indicator.addEventListener('click', () => {
      currentIndex = index; 
      updateSlider();
    });
    indicatorsContainer.appendChild(indicator);
  });

  updateIndicators(); 
}

function updateIndicators() {
  const indicators = document.querySelectorAll('.order-fix-slider-indicator');
  const maxVisibleIndicators = 5; 
  const minVisibleIndicators = 3; 
  const range = Math.floor(maxVisibleIndicators / 2); 

  let start = Math.max(0, currentIndex - range);
  let end = Math.min(slideCount - 1, currentIndex + range);

  while (end - start + 1 < minVisibleIndicators) {
    if (start > 0) start--;
    else if (end < slideCount - 1) end++;
    else break; 
  }

  indicators.forEach((indicator, index) => {
    if (index >= start && index <= end) {
      indicator.style.display = 'block';
      const distance = Math.abs(currentIndex - index);
      const size = 15 - distance * 2; 
      const finalSize = Math.min(Math.max(size, 8), 8);  
      indicator.style.width = `${finalSize}px`;
      indicator.style.height = `${finalSize}px`;
      indicator.classList.toggle('active', index === currentIndex);
    } else {
      indicator.style.display = 'none';
    }
  });
}

createIndicators();
updateSlider();

const leftControl = document.querySelector('.order-fix-slider-controls-left');
const rightControl = document.querySelector('.order-fix-slider-controls-right');

leftControl.removeEventListener('click', prevSlide);
rightControl.removeEventListener('click', nextSlide);

leftControl.addEventListener('click', prevSlide);
rightControl.addEventListener('click', nextSlide);

let startX = 0;
let startY = 0;
let endX = 0;
let endY = 0;

slidesContainer.addEventListener('touchstart', (event) => {
  startX = event.touches[0].clientX;
  startY = event.touches[0].clientY;
});

slidesContainer.addEventListener('touchmove', (event) => {
  endX = event.touches[0].clientX;
  endY = event.touches[0].clientY;

  const diffX = endX - startX;
  const diffY = endY - startY;

  const absDiffX = Math.abs(diffX);
  const absDiffY = Math.abs(diffY);

  if (absDiffX > absDiffY) {
    event.preventDefault();
  }
});

slidesContainer.addEventListener('touchend', () => {
  const swipeDistanceX = endX - startX;
  const swipeDistanceY = endY - startY;

  const threshold = 50;

  if (Math.abs(swipeDistanceX) > Math.abs(swipeDistanceY) && Math.abs(swipeDistanceX) > threshold) {
    if (swipeDistanceX > 0) {
      prevSlide();
    } else {
      nextSlide();
    }
  }

  startX = 0;
  startY = 0;
  endX = 0;
  endY = 0;
});
</script>