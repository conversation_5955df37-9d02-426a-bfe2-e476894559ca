<?php
session_start();
include '../includes/config.php';
include 'connect.php';

// Simple test to update a blog post
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    echo "<h2>POST Request Received</h2>";
    echo "<pre>" . print_r($_POST, true) . "</pre>";
    
    $post_id = $_POST['post_id'];
    $title = $_POST['title'];
    
    try {
        $sql = "UPDATE blog_posts SET title = :title WHERE id = :id";
        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute([':title' => $title, ':id' => $post_id]);
        
        if ($result) {
            echo "<p style='color: green;'>Update successful! Affected rows: " . $stmt->rowCount() . "</p>";
        } else {
            echo "<p style='color: red;'>Update failed!</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    }
} else {
    // Show form
    $post_id = $_GET['id'] ?? 1;
    $sql = "SELECT * FROM blog_posts WHERE id = :id";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([':id' => $post_id]);
    $post = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($post) {
        echo "<h2>Test Update Form</h2>";
        echo "<form method='POST'>";
        echo "<input type='hidden' name='post_id' value='" . $post['id'] . "'>";
        echo "<label>Title:</label><br>";
        echo "<input type='text' name='title' value='" . htmlspecialchars($post['title']) . "' style='width: 400px;'><br><br>";
        echo "<button type='submit'>Update</button>";
        echo "</form>";
        
        echo "<h3>Current Post Data:</h3>";
        echo "<pre>" . print_r($post, true) . "</pre>";
    } else {
        echo "<p>Post not found!</p>";
    }
}
?>
