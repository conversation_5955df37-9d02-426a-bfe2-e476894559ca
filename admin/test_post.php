<?php
session_start();
include '../includes/config.php';
include 'connect.php';

echo "<h2>POST Test</h2>";

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    echo "<h3>POST Data Received:</h3>";
    echo "<pre>";
    print_r($_POST);
    echo "</pre>";
    
    echo "<h3>FILES Data:</h3>";
    echo "<pre>";
    print_r($_FILES);
    echo "</pre>";
    
    echo "<h3>Session Data:</h3>";
    echo "<pre>";
    print_r($_SESSION);
    echo "</pre>";
} else {
    echo "<p>No POST data received</p>";
}

echo "<h3>Current GET parameters:</h3>";
echo "<pre>";
print_r($_GET);
echo "</pre>";

echo "<h3>Database connection test:</h3>";
try {
    $test_sql = "SELECT COUNT(*) as count FROM blog_posts";
    $test_stmt = $pdo->prepare($test_sql);
    $test_stmt->execute();
    $result = $test_stmt->fetch();
    echo "Blog posts count: " . $result['count'];
} catch (Exception $e) {
    echo "Database error: " . $e->getMessage();
}
?>
