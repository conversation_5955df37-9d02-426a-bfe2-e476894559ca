<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();
include '../includes/config.php';
include 'connect.php';

// Debug session
error_log("Session data: " . print_r($_SESSION, true));

// Handle Summernote image uploads
if (isset($_FILES['image_upload'])) {
    header('Content-Type: application/json');
    
    $uploadDir = '../uploads/editor/';
    if (!file_exists($uploadDir)) {
        mkdir($uploadDir, 0777, true);
    }
    
    $fileName = uniqid() . '_' . basename($_FILES['image_upload']['name']);
    $uploadFilePath = $uploadDir . $fileName;
    
    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
    $fileType = $_FILES['image_upload']['type'];
    
    if (in_array($fileType, $allowedTypes)) {
        if (move_uploaded_file($_FILES['image_upload']['tmp_name'], $uploadFilePath)) {
            echo json_encode([
                'success' => true,
                'url' => '/uploads/editor/' . $fileName
            ]);
            exit;
        }
    }
    
    echo json_encode(['success' => false, 'error' => 'Invalid file type or upload failed']);
    exit;
}

// Fetch the blog post data if an ID is provided
if (isset($_GET['id'])) {
    $post_id = $_GET['id'];
    $sql = "SELECT * FROM blog_posts WHERE id = :id";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([':id' => $post_id]);
    $post = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$post) {
        die("Post not found.");
    }
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Debug: Check if we're receiving POST data
    echo "<div style='background: yellow; padding: 10px; margin: 10px;'>POST REQUEST RECEIVED!</div>";
    error_log("Blog edit POST received");
    error_log("POST data: " . print_r($_POST, true));

    $post_id = $_POST['post_id'];
    $title = $_POST['title'];
    $content = $_POST['content'];
    $description = $_POST['short_description'];
    $category = $_POST['category'];
    $url = $_POST['url'];
    $seo_tags = $_POST['seo_tags'];

    echo "<div style='background: lightblue; padding: 10px; margin: 10px;'>
        POST ID: $post_id<br>
        Title: $title<br>
        Content length: " . strlen($content) . "<br>
        Description: $description<br>
        Category: $category<br>
        URL: $url<br>
        SEO Tags: $seo_tags
    </div>";
    
    // Process table of contents from unified input
    $table_of_contents = "";
    if (isset($_POST['toc_item']) && is_array($_POST['toc_item'])) {
        $table_of_contents = implode("\n\n", array_filter($_POST['toc_item']));
    }

    $image = $_FILES['image']['name'];

    // Debug validation
    error_log("Validation check - Title: " . (!empty($title) ? 'OK' : 'EMPTY'));
    error_log("Validation check - Content: " . (!empty($content) ? 'OK' : 'EMPTY'));
    error_log("Validation check - Description: " . (!empty($description) ? 'OK' : 'EMPTY'));
    error_log("Validation check - Category: " . (!empty($category) ? 'OK' : 'EMPTY'));

    if (empty($title) || empty($content) || empty($description) || empty($category)) {
        $error = "All fields are required!";
        error_log("Validation failed: " . $error);
    } else {
        $image_path = $post['image']; // Keep the existing image by default

        if (!empty($image)) {
            $upload_path = '../uploads/' . basename($image);
            if (move_uploaded_file($_FILES['image']['tmp_name'], $upload_path)) {
                $image_path = basename($image); // Store only filename in database
            } else {
                $error = "Failed to upload image.";
            }
        }

        if (!isset($error)) {
            $htmlContent = htmlspecialchars_decode($content);

            // Set default Table of Contents if empty
            if (empty($table_of_contents)) {
                $table_of_contents = "1. Requirements by state\n\n2. Steps for changing your car registration\n\n3. Conclusion";
            }

            try {
                // Check if URL already exists for another post
                $url_check_sql = "SELECT id FROM blog_posts WHERE url = :url AND id != :current_id";
                $url_check_stmt = $pdo->prepare($url_check_sql);
                $url_check_stmt->execute([':url' => $url, ':current_id' => $post_id]);

                if ($url_check_stmt->fetch()) {
                    $error = "URL already exists for another post. Please choose a different URL.";
                } else {
                    $sql = "UPDATE blog_posts
                            SET title = :title,
                                content = :content,
                                short_description = :short_description,
                                image = :image,
                                category = :category,
                                url = :url,
                                seo_tags = :seo_tags,
                                table_of_contents = :table_of_contents
                            WHERE id = :id";
                    $stmt = $pdo->prepare($sql);
                    $result = $stmt->execute([
                        ':title' => $title,
                        ':content' => $htmlContent,
                        ':short_description' => $description,
                        ':image' => $image_path,
                        ':category' => $category,
                        ':url' => $url,
                        ':seo_tags' => $seo_tags,
                        ':table_of_contents' => $table_of_contents,
                        ':id' => $post_id
                    ]);

                    if ($result) {
                        $_SESSION['success_message'] = "Blog post updated successfully!";
                        header("Location: blog.php");
                        exit;
                    } else {
                        $error = "Failed to update blog post in database.";
                    }
                }
            } catch (PDOException $e) {
                if ($e->getCode() == 23000) { // Duplicate entry error
                    $error = "URL already exists. Please choose a different URL.";
                } else {
                    $error = "Database error: " . $e->getMessage();
                }
                error_log("Blog edit error: " . $e->getMessage());
            }
        }
    }
}

// SQL მოთხოვნა მომხმარებლის სახელისა და სურათის მისაღებად
$user_id = $_SESSION['user_id'];
$user_sql = "SELECT user_name, user_img FROM users WHERE id = :user_id";
$user_stmt = $pdo->prepare($user_sql);
$user_stmt->execute(['user_id' => $user_id]);
$user_data = $user_stmt->fetch(PDO::FETCH_ASSOC);

// მონაცემების გამოყენება
$user_name = $user_data['user_name'] ?? 'Unknown'; 
$user_img = $user_data['user_img'] ?? 'public/img/avatar/avatar.png';

// წაიკითხეთ შეტყობინებები
$notifications_sql = "SELECT * FROM notifications ORDER BY created_at DESC limit 5";
$notifications_stmt = $pdo->prepare($notifications_sql);
$notifications_stmt->execute();
$notifications = $notifications_stmt->fetchAll(PDO::FETCH_ASSOC);

// ნოტიფიკაციის წაკითხვის ლოგიკა
if (isset($_GET['mark_as_read'])) {
  $notification_id = $_GET['mark_as_read'];
  $update_sql = "UPDATE notifications SET `read` = 1 WHERE id = :id";
  $update_stmt = $pdo->prepare($update_sql);
  $update_stmt->execute(['id' => $notification_id]);

  // გადამისამართება orders გვერდზე
  header("Location: orders");
  exit();
}

// წაიკითხეთ ნოტიფიკაციები
$notifications_sql = "SELECT * FROM notifications ORDER BY created_at DESC";
$notifications_stmt = $pdo->prepare($notifications_sql);
$notifications_stmt->execute();
$notifications = $notifications_stmt->fetchAll(PDO::FETCH_ASSOC);

// შეამოწმეთ, არის თუ არა წაუკითხავი ნოტიფიკაციები
$unread_notifications = array_filter($notifications, function($notification) {
  return $notification['read'] == 0;
});
$has_unread = count($unread_notifications) > 0;
?>

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, shrink-to-fit=no" name="viewport">
  <title>Edit Blog Post &mdash; Safe Car Hauler</title>

  <link rel="stylesheet" href="public/modules/bootstrap/css/bootstrap.min.css">
  <link rel="stylesheet" href="public/modules/ionicons/css/ionicons.min.css">
  <link rel="stylesheet" href="public/modules/fontawesome/web-fonts-with-css/css/fontawesome-all.min.css">

  <!-- Summernote CSS -->
  <link href="https://cdn.jsdelivr.net/npm/summernote@0.9.0/dist/summernote-lite.min.css" rel="stylesheet">
  
  <link rel="stylesheet" href="public/modules/flag-icon-css/css/flag-icon.min.css">
  <link rel="stylesheet" href="public/css/demo.css">
  <link rel="stylesheet" href="public/css/style.css">
  <link rel="icon" type="image/x-icon" href="public/favicon.ico">

  <style>
    div[style=""] {
      display: none !important;
      visibility: hidden !important;
      font-size: 0 !important;
      color: transparent !important;
    }
       .note-editable h1 {
      font-size: 24pt;
      font-weight: bold;
         font-family: 'Nohemi';
    }
    .note-editable h2 {
      font-size: 18pt;
      font-weight: bold;
               font-family: 'Nohemi';

    }
    .note-editable h3 {
      font-size: 14pt;
      font-weight: bold;
               font-family: 'Nohemi';

    }
    .note-editable h4 {
      font-size: 12pt;
      font-weight: bold;
               font-family: 'Nohemi';

    }
    .note-editable p {
      font-size: 12pt;
      margin-bottom: 12px;
               font-family: 'Nohemi';

      font-weight: 500!important;
    }
  </style>
</head>
<body>
  <div id="app">
    <div class="main-wrapper">
      <div class="navbar-bg"></div>
      <?php include 'navbar.php'; ?>
      <?php include 'sidebar.php'; ?>
      <div class="main-content">
        <section class="section">
          <h1 class="section-header">
            <div>Edit Blog Post</div>
          </h1>
          <div class="row">
            <div class="col-lg-12 col-md-12 col-12 col-sm-12">
            <?php if (isset($error)): ?>
                <div class="alert alert-danger alert-has-icon">
                <div class="alert-icon"><i class="ion ion-ios-lightbulb-outline"></i></div>
                <div class="alert-body">
                <div class="alert-title">Error</div>
                <?php echo $error; ?>
                </div>
                </div>
              <?php endif; ?>
              <div class="card">
                <div class="card-body">             
                <form method="POST" action="" enctype="multipart/form-data">
                <input type="hidden" name="post_id" value="<?php echo $post['id']; ?>">
                <div class="form-group">
                      <label>Title</label>
                      <input type="text" name="title" id="title"  class="form-control" placeholder="Enter the title" value="<?php echo isset($post['title']) ? htmlspecialchars($post['title']) : ''; ?>" required>
                      <div class="invalid-feedback">
                        Please fill in the title
                      </div>
                    </div>
                <div class="form-group">
                      <label>Custom URL</label>
                      <input type="text" name="url" id="url"  class="form-control" placeholder="Enter the custom URL" value="<?php echo isset($post['url']) ? htmlspecialchars($post['url']) : ''; ?>" required="">
                      <div class="invalid-feedback">
                        Please fill in the title
                      </div>
                    </div>
                <div class="form-group">
                      <label>Category</label>
                      <input type="text" name="category" id="category"  class="form-control" placeholder="Enter the category" value="<?php echo isset($post['category']) ? htmlspecialchars($post['category']) : ''; ?>" required>
                      <div class="invalid-feedback">
                        Please fill in the title
                      </div>
                    </div>
                <div class="form-group">
                      <label>Short Description</label>
                      <textarea name="short_description" id="short_description" class="form-control" placeholder="Enter a short description" required><?php echo isset($post['short_description']) ? htmlspecialchars($post['short_description']) : ''; ?></textarea>
                      <div class="invalid-feedback">
                        Please fill in the title
                      </div>
                    </div>
                <label for="content">Content</label>
                <div id="summernote"><?php echo isset($post['content']) ? htmlspecialchars_decode($post['content']) : ''; ?></div>
                <textarea name="content" id="content" style="display:none;"></textarea>
                <br>
                <div class="form-group">
                      <label>SEO Tags</label>
                      <input type="text" name="seo_tags" class="form-control" 
                             value="<?php echo htmlspecialchars($post['seo_tags']); ?>" required>
                    </div>
                    
                <div class="form-group">
                    <label>Table of Contents</label>
                    <div id="toc-wrapper">
                        <?php
                        // Split the existing table of contents into separate lines (if any)
                        $toc_items = explode("\n\n", $post['table_of_contents'] ?? '');
                        if (empty($toc_items[0])) {
                            $toc_items = ["1. Requirements by state", "2. Steps for changing your car registration", "3. Conclusion"];
                        }
                        foreach ($toc_items as $item) {
                            echo '<input type="text" name="toc_item[]" class="form-control mb-2" value="' . htmlspecialchars(trim($item)) . '">';
                        }
                        ?>
                    </div>
                    <button type="button" class="btn btn-secondary btn-sm mt-2" onclick="addTocItem()">+ Add item</button>
                    <small class="form-text text-muted">
                        Enter each item on a new line, with blank lines between sections
                    </small>
                </div>

                    <label for="image" class="image-label">Choose an image</label>
                        <input type="file" name="image" id="image" accept="image/*" class="image-input">
                      <div id="image-preview" class="image-preview" 
                           style="display: <?php echo !empty($post['image']) ? 'block' : 'none'; ?>;">
                        <img id="preview" src="<?php echo !empty($post['image']) ? '../uploads/'.$post['image'] : '#'; ?>" 
                             alt="Current Image" class="preview-img">
                      </div>
                      <?php if (!empty($post['image'])): ?>
                        <small class="form-text text-muted">
                          Current image: <?php echo htmlspecialchars($post['image']); ?>
                        </small>
                      <?php endif; ?>
                    </div>
                <div class="card-footer d-flex justify-content-center">
                    <button type="submit" class="btn btn-primary" onclick="console.log('Button clicked');">Update Post</button>
                  </div>
            </form>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>

      <!-- jQuery and Summernote JS -->
      <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
      <script src="https://cdn.jsdelivr.net/npm/summernote@0.9.0/dist/summernote-lite.min.js"></script>
      
      <script>
       $(document).ready(function() {
        $('#summernote').summernote({
            height: 300,
             fontSizeUnit: 'pt',  // Set font size unit to 'pt'
            fontNames: ['Arial'],
      addDefaultFonts: false,
            callbacks: {
                onImageUpload: function(files) {
                    uploadImage(files[0]);
                }
            },
            popover: {  // Correctly place the popover option here
                image: [
                    ['image', ['resizeFull', 'resizeHalf', 'resizeQuarter', 'resizeNone']],
                    ['float', ['floatLeft', 'floatRight', 'floatNone']],
                    ['remove', ['removeMedia']]
                ],
                link: [
                    ['link', ['linkDialogShow', 'unlink']]
                ],
                table: [
                    ['add', ['addRowDown', 'addRowUp', 'addColLeft', 'addColRight']],
                    ['delete', ['deleteRow', 'deleteCol', 'deleteTable']],
                ],
                air: [
                    ['color', ['color']],
                    ['font', ['bold', 'underline', 'clear']],
                    ['fontname', ['fontname']],
                    ['para', ['ul', 'paragraph']],
                    ['table', ['table']],
                    ['insert', ['link', 'picture']]
                ]
            }
        });

        // Upload image to server
        function uploadImage(file) {
            var data = new FormData();
            data.append("image_upload", file);

            $.ajax({
                url: window.location.href,  // Post to the same file
                data: data,
                cache: false,
                contentType: false,
                processData: false,
                type: "POST",
                dataType: "json",
                success: function(response) {
                    if (response.url) {
                        // Insert the image into Summernote
                        $('#summernote').summernote('insertImage', response.url);
                    } else if (response.error) {
                        alert(response.error);
                    }
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.error("Upload error:", textStatus, errorThrown);
                    alert("Image upload failed: " + textStatus);
                }
            });
        }

        // Handle form submission
        $('form').on('submit', function(event) {
            console.log('Form submission started');

            // Check required fields
            var title = $('input[name="title"]').val();
            var category = $('input[name="category"]').val();
            var description = $('textarea[name="short_description"]').val();
            var url = $('input[name="url"]').val();
            var seo_tags = $('input[name="seo_tags"]').val();

            console.log('Title:', title);
            console.log('Category:', category);
            console.log('Description:', description);
            console.log('URL:', url);
            console.log('SEO Tags:', seo_tags);

            // Check if any required field is empty
            if (!title || !category || !description || !url || !seo_tags) {
                console.log('Required field validation failed');
                alert('Please fill in all required fields!');
                event.preventDefault();
                return false;
            }

            // Get the HTML content from Summernote
            var summernoteContent = $('#summernote').summernote('code');
            console.log('Summernote content:', summernoteContent);

            if (!summernoteContent || summernoteContent.trim() === '' || summernoteContent === '<p><br></p>') {
                console.log('Content validation failed');
                alert('Please add some content!');
                event.preventDefault();
                return false;
            }

            // Put it in the hidden textarea to be submitted
            $('#content').val(summernoteContent);
            console.log('Content set in hidden field');
            console.log('All validations passed - form will now submit normally');
            // Don't prevent default - let form submit normally
        });
    });

        function addTocItem() {
            const input = document.createElement('input');
            input.type = 'text';
            input.name = 'toc_item[]';
            input.className = 'form-control mb-2';
            document.getElementById('toc-wrapper').appendChild(input);
        }
        
        document.getElementById('image').addEventListener('change', function(event) {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('preview').src = e.target.result;
                    document.getElementById('image-preview').style.display = 'block';
                }
                reader.readAsDataURL(file);
            }
        });
      </script>

      <?php include 'footer.php'; ?>
    </div>
  </div>
</body>
</html>