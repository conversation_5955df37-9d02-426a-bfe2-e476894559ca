<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();
include '../includes/config.php';
include 'connect.php';

echo "<h2>Blog Edit Debug</h2>";

// Check if we have a post ID
if (isset($_GET['id'])) {
    $post_id = $_GET['id'];
    echo "<p>Post ID: " . $post_id . "</p>";
    
    // Try to fetch the post
    try {
        $sql = "SELECT * FROM blog_posts WHERE id = :id";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([':id' => $post_id]);
        $post = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($post) {
            echo "<h3>Post found:</h3>";
            echo "<pre>" . print_r($post, true) . "</pre>";
        } else {
            echo "<p style='color: red;'>Post not found!</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p style='color: red;'>No post ID provided!</p>";
}

// Check if this is a POST request
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    echo "<h3>POST Request Received:</h3>";
    echo "<h4>POST Data:</h4>";
    echo "<pre>" . print_r($_POST, true) . "</pre>";
    echo "<h4>FILES Data:</h4>";
    echo "<pre>" . print_r($_FILES, true) . "</pre>";
    
    // Try to update the post
    if (isset($_POST['post_id'])) {
        $post_id = $_POST['post_id'];
        $title = $_POST['title'];
        $content = $_POST['content'];
        $description = $_POST['short_description'];
        $category = $_POST['category'];
        $url = $_POST['url'];
        $seo_tags = $_POST['seo_tags'];
        
        echo "<h4>Attempting to update post ID: " . $post_id . "</h4>";
        
        try {
            $sql = "UPDATE blog_posts 
                    SET title = :title, 
                        content = :content, 
                        short_description = :short_description, 
                        category = :category, 
                        url = :url, 
                        seo_tags = :seo_tags
                    WHERE id = :id";
            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute([
                ':title' => $title,
                ':content' => $content,
                ':short_description' => $description,
                ':category' => $category,
                ':url' => $url,
                ':seo_tags' => $seo_tags,
                ':id' => $post_id
            ]);
            
            if ($result) {
                echo "<p style='color: green;'>✓ Update successful!</p>";
                echo "<p>Affected rows: " . $stmt->rowCount() . "</p>";
            } else {
                echo "<p style='color: red;'>✗ Update failed!</p>";
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
        }
    }
}

// Show current session data
echo "<h3>Session Data:</h3>";
echo "<pre>" . print_r($_SESSION, true) . "</pre>";

// Show database connection status
echo "<h3>Database Connection:</h3>";
try {
    $test_sql = "SELECT 1";
    $test_stmt = $pdo->prepare($test_sql);
    $test_stmt->execute();
    echo "<p style='color: green;'>✓ Database connection working</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Database connection failed: " . $e->getMessage() . "</p>";
}
?>
