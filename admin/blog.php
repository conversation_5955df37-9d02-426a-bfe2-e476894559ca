<?php
session_start();
include '../includes/config.php';
include 'connect.php';


// SQL მოთხოვნა ბლოგ პოსტების ამოღებისთვის
$blog_sql = "SELECT * FROM blog_posts ORDER BY created_at DESC"; 
$blog_stmt = $pdo->prepare($blog_sql);
$blog_stmt->execute();
$blogs = $blog_stmt->fetchAll(PDO::FETCH_ASSOC);

// SQL მოთხოვნა მომხმარებლის სახელისა და სურათის მისაღებად
$user_id = $_SESSION['user_id'];
$user_sql = "SELECT user_name, user_img FROM users WHERE id = :user_id";
$user_stmt = $pdo->prepare($user_sql);
$user_stmt->execute(['user_id' => $user_id]);
$user_data = $user_stmt->fetch(PDO::FETCH_ASSOC);

// მონაცემების გამოყენება
$user_name = $user_data['user_name'] ?? 'Unknown'; 
$user_img = $user_data['user_img'] ?? 'public/img/avatar/avatar.png';

// გვერდის ნომერი (მიმდინარე გვერდი)
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$itemsPerPage = 5; // ერთ გვერდზე გამოსაჩენი პოსტების რაოდენობა
$offset = ($page - 1) * $itemsPerPage;

// SQL მოთხოვნა ბლოგ პოსტების ამოღებისთვის
$blog_sql = "SELECT * FROM blog_posts ORDER BY created_at DESC LIMIT :limit OFFSET :offset"; 
$blog_stmt = $pdo->prepare($blog_sql);
$blog_stmt->bindValue(':limit', $itemsPerPage, PDO::PARAM_INT);
$blog_stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
$blog_stmt->execute();
$blogs = $blog_stmt->fetchAll(PDO::FETCH_ASSOC);

// გვერდების რაოდენობის გამოთვლა
$totalPosts = $pdo->query("SELECT COUNT(*) FROM blog_posts")->fetchColumn();
$totalPages = ceil($totalPosts / $itemsPerPage);

// წაიკითხეთ შეტყობინებები
$notifications_sql = "SELECT * FROM notifications ORDER BY created_at DESC limit 5";
$notifications_stmt = $pdo->prepare($notifications_sql);
$notifications_stmt->execute();
$notifications = $notifications_stmt->fetchAll(PDO::FETCH_ASSOC);

// ნოტიფიკაციის წაკითხვის ლოგიკა
if (isset($_GET['mark_as_read'])) {
  $notification_id = $_GET['mark_as_read'];
  $update_sql = "UPDATE notifications SET `read` = 1 WHERE id = :id";
  $update_stmt = $pdo->prepare($update_sql);
  $update_stmt->execute(['id' => $notification_id]);

  // გადამისამართება orders გვერდზე
  header("Location: orders");
  exit();
}

// წაიკითხეთ ნოტიფიკაციები
$notifications_sql = "SELECT * FROM notifications ORDER BY created_at DESC";
$notifications_stmt = $pdo->prepare($notifications_sql);
$notifications_stmt->execute();
$notifications = $notifications_stmt->fetchAll(PDO::FETCH_ASSOC);

// შეამოწმეთ, არის თუ არა წაუკითხავი ნოტიფიკაციები
$unread_notifications = array_filter($notifications, function($notification) {
  return $notification['read'] == 0;
});
$has_unread = count($unread_notifications) > 0;
?>

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, shrink-to-fit=no" name="viewport">
  <title>Blog &mdash; Safe Car Hauler</title>
  <base href="/admin/">

  <link rel="stylesheet" href="public/modules/bootstrap/css/bootstrap.min.css">
  <link rel="stylesheet" href="public/modules/ionicons/css/ionicons.min.css">
  <link rel="stylesheet" href="public/modules/fontawesome/web-fonts-with-css/css/fontawesome-all.min.css">

  <link rel="stylesheet" href="public/modules/summernote/summernote-lite.css">
  <link rel="stylesheet" href="public/modules/flag-icon-css/css/flag-icon.min.css">
  <link rel="stylesheet" href="public/css/demo.css">
  <link rel="stylesheet" href="public/css/style.css">
  <link rel="icon" type="image/x-icon" href="public/favicon.ico">
</head>
<body>
  <div id="app">
    <div class="main-wrapper">
      <div class="navbar-bg"></div>
      <?php include 'navbar.php'; ?>
      <?php include 'sidebar.php'; ?>
      <div class="main-content">
        <section class="section">
          <h1 class="section-header">
            <div>Blog</div>
          </h1>
          <div class="row">
            <div class="add_blog">
                <div class="add_blog_button">
                    <a href="blog_add.php" class="btn btn-primary">Add Blog</a>
                </div>
             </div>
            <div class="col-lg-12 col-md-12 col-12 col-sm-12">
              <div class="card">
                <div class="card-body">             
                  <ul class="list-unstyled list-unstyled-border">
                  <?php foreach ($blogs as $blog): ?>
                        <li class="media">
                            <div class="media-container">
                                <img class="blog-image" src="../uploads/<?php echo $blog['image']; ?>" alt="<?php echo $blog['title']; ?>" style="border-radius: 5px;">
                                <div class="media-body">
                                    <div class="float-right">
                                         
                                        <a href="blog_edit.php?id=<?php echo $blog['id']; ?>" class="btn btn-outline-primary btn-sm"> <i class="fas fa-edit"></i> </a>
                                        <a href="includes/blog_delete.php?id=<?php echo $blog['id']; ?>" class="btn btn-outline-danger btn-sm" onclick="return confirm('Are you sure you want to delete this post?');"><i class="fas fa-trash"></i> </a>
                                    </div>
                                    <div class="media-title"><?php echo $blog['title']; ?></div>
                                    <div class="blog-time"><small><?php echo date("F j, Y", strtotime($blog['created_at'])); ?></small></div>
                                    <div class="category-title"><?php echo htmlspecialchars($blog['category']); ?></div>
                                    <small><?php echo $blog['short_description']; ?></small>
                                </div>
                            </div>
                        </li>
                    <?php endforeach; ?>
                  </ul>
                  <div class="card-footer text-right">
                        <nav class="d-inline-block">
                            <ul class="pagination mb-0">
                                <!-- წინა გვერდის ბმული -->
                                <li class="page-item <?php echo ($page <= 1) ? 'disabled' : ''; ?>">
                                    <a class="page-link" href="?page=<?php echo $page - 1; ?>" tabindex="-1"><i class="ion ion-chevron-left"></i></a>
                                </li>

                                <!-- გვერდების ბმულები -->
                                <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                    <li class="page-item <?php echo ($page == $i) ? 'active' : ''; ?>">
                                        <a class="page-link" href="?page=<?php echo $i; ?>"><?php echo $i; ?></a>
                                    </li>
                                <?php endfor; ?>

                                <!-- შემდეგი გვერდის ბმული -->
                                <li class="page-item <?php echo ($page >= $totalPages) ? 'disabled' : ''; ?>">
                                    <a class="page-link" href="?page=<?php echo $page + 1; ?>"><i class="ion ion-chevron-right"></i></a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
<?php include 'footer.php'; ?>
</body>
</html>