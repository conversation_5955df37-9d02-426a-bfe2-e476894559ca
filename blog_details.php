<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', 'blog_details_error.log');

session_start();
require 'includes/config.php';

// Check if custom_url is provided
if (isset($_GET['url']) && !empty($_GET['url'])) {
    $custom_url = $_GET['url'];

    // Fetch the blog post using the custom URL
    $sql = "SELECT * FROM blog_posts WHERE url = :custom_url";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([':custom_url' => $custom_url]);
    $blog = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$blog) {
        // If no blog is found, show an error or a 404 page
        echo "Blog not found!";
    }
} else {
    // If custom_url is not provided, show an error
    echo "Blog URL is not specified.";
}

if (!$blog) {
  header("HTTP/1.0 404 Not Found");
  echo "Blog not found!";
  exit;
}
?>

<!doctype html>
<html lang="en">
  <head>
 <meta charset="utf-8" />
    <title><?php echo htmlspecialchars($blog['title']); ?> - Safe Car Hauler</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/x-icon" href="public/favicon.ico">
    
    <!-- SEO Meta Tags -->
    <?php
    require_once 'includes/functions.php';
    
    $full_url = 'https://safecarhauler.com/blog/' . $blog['url'];
    $image_url = !empty($blog['image']) ? 'https://safecarhauler.com/uploads/' . $blog['image'] : '';
    
    echo generate_seo_meta_tags(
        $blog['title'],
        $blog['short_description'],
        $blog['seo_tags'] ?? '',
        $full_url,
        $image_url
    );
    ?>
    <base href="https://safecarhauler.com/" />
    <link rel="stylesheet" type="text/css" href="public/css/index.css" />
    <link rel="stylesheet" type="text/css" href="public/css/styles.css" />
    <link rel="stylesheet" type="text/css" href="public/css/components.css" />
    <link rel="stylesheet" type="text/css" href="public/css/main.css" />
    <link rel="stylesheet" type="text/css" href="public/css/BlogDeitals.css" />
    <!-- iubenda start -->
  <script type="text/javascript">
        var _iub = _iub || {}; 
        _iub.cons_instructions = _iub.cons_instructions || []; 
        _iub.cons_instructions.push(["init", {api_key: "ri0L77KZSPdst3FYVu0GB0GG0oQ59GS7"}]);
    </script>
    <script type="text/javascript" src="https://cdn.iubenda.com/cons/iubenda_cons.js" async></script>
    <script type="text/javascript">
        var _iub = _iub || [];
        _iub.csConfiguration = {"siteId":3966585,"cookiePolicyId":28502363,"lang":"en","storage":{"useSiteId":true}};
    </script>
    <script type="text/javascript" src="https://cs.iubenda.com/autoblocking/3966585.js"></script>
    <script type="text/javascript" src="//cdn.iubenda.com/cs/gpp/stub.js"></script>
    <script type="text/javascript" src="//cdn.iubenda.com/cs/iubenda_cs.js" charset="UTF-8" async></script>

<!-- iubenda end -->
    <style>
      .blog-fix-full-text img {
    max-width: 100%;
    height: auto;
    display: block;
    margin: 0 auto;
      }
    p[data-f-id="pbf"] {
    display: none !important;
    visibility: hidden !important;
    font-size: 0;
    color: transparent;
  }

  img.emoji {
  display: none;
  visibility: hidden;
  }

.MsoNormal  > a {
    color: var(--deep_orange_300);
    text-decoration: none;
    transition: color 0.3s ease;    display: inline-flex;

}

.MsoNormal  > a:hover {
    color: #222A31; 
  text-decoration: underline;
}
.blog-fix-columntableof-1 {
    position: relative;
    max-height: 500px; 
    overflow: hidden;
    transition: max-height 0.3s ease;
    border: 1px solid #ddd; 
}

.toc-column-container {
    margin-top: 20px;
    font-size: 16px;
}

.toc-column-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.toc-column-item {
    margin: 10px 0;
    font-size: 16px;
}

.resize-handle {
    position: absolute;
    top: 10px;
    right: 10px;
    cursor: pointer;
    font-size: 24px;
    transition: transform 0.3s ease; 
}


.blog-fix-columntableof-1.collapsed {
    max-height: 50px;
}
.blog-fix-columntableof-1.collapsed .resize-handle {
    transform: rotate(180deg); 
    
}


.blog-fix-columntableof-1.expanded {
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}
/*.blog-fix-full-text p {*/
/*    line-height: 8px;*/
/*}*/
.blog-fix-full-text {
    font-size: 1rem !important; /* 16px */
    line-height: 1.75; /* უკეთესი წაკითხვადობისთვის */
}

.blog-fix-full-text p {
    font-size: 1rem !important; /* 16px */
    line-height: 1.75; 
    margin-bottom: 1.5em;
        font-family: 'Nohemi' !important;

}

.blog-fix-full-text h1 {
    font-size: 2.25rem !important; /* 36px */
    line-height: 1.2;
    font-family: 'Nohemi' !important;
    margin-bottom: 1em;
}

.blog-fix-full-text h2 {
    font-size: 1.875rem !important; /* 30px */
    line-height: 1.3;
    font-family: 'Nohemi' !important;
    margin-bottom: 0.8em;
}

.blog-fix-full-text h3 {
    font-size: 1.5rem!important; /* 24px */
    font-family: 'Nohemi' !important;
    line-height: 1.4;
    margin-bottom: 0.8em;
}

.blog-fix-full-text h4 {
    font-size: 1.25rem!important; /* 20px */
    font-family: 'Nohemi' !important;

    line-height: 1.5;
    margin-bottom: 0.6em;
}

.blog-fix-full-text img {
    max-width: 100%;
    height: auto;
    display: block;
    margin: 1.5em auto;
}
 .blog-fix-columnjuly16202 h1 {font-size: 34px !important; font-weight:bold;}
@media only screen and (max-width: 768px) {
  .blog-fix-columnjuly16202 h1 {
    font-size: 23px !important;
  }
}
  @media only screen and (max-width: 1248px) {
    .instantquote {

    border-radius: 10px;
    }

/* Add transitions and hover effects if needed */
.MsoNormal > span > a {
    color: var(--deep_orange_300);
    text-decoration: none;
    transition: color 0.3s ease;
    display: inline-flex;
}

.MsoNormal > span > a:hover {
    color: #222A31;
    text-decoration: underline;
}
.MsoListParagraphCxSpFirst span,
.MsoListParagraphCxSpMiddle span, 
.MsoListParagraphCxSpLast span {
    display: none !important;
    
}


.MsoListParagraphCxSpFirst,
.MsoListParagraphCxSpMiddle,
.MsoListParagraphCxSpLast {
    /*margin: 0 !important;*/
    /*padding: 0 !important;*/
    text-indent: -0.25in !important;
    margin-left: 0.75in !important;
    line-height: 1.15 !important;
    position: relative !important;
    font-size: 12pt !important;
    /*font-family: "Times New Roman", serif !important;*/
}
.MsoListParagraphCxSpFirst span:empty,
.MsoListParagraphCxSpMiddle span:empty,
.MsoListParagraphCxSpLast span:empty,
.MsoListParagraphCxSpFirst span:contains("&nbsp;"),
.MsoListParagraphCxSpMiddle span:contains("&nbsp;"), 
.MsoListParagraphCxSpLast span:contains("&nbsp;") {
    display: none !important;
}

.MsoListParagraphCxSpFirst::before,
.MsoListParagraphCxSpMiddle::before,
.MsoListParagraphCxSpLast::before {
    content: "•";
    font-family: "Symbol", "Apple Symbols", sans-serif !important;
    font-size: 14pt !important;
    /*position: absolute;*/
    /*left: ;*/
    margin-right: 12px;
}


.MsoListParagraphCxSpFirst span[style*="Times New Roman"],
.MsoListParagraphCxSpMiddle span[style*="Times New Roman"],
.MsoListParagraphCxSpLast span[style*="Times New Roman"] {
    display: inline-block !important;
    width: 0.25in !important;
}
.MsoNormal > a{
        display: inline-block;
    color: #CDA565;
    text-decoration: none;
    transition: color 0.3s ease;
}
.MsoNormal > a:hover{
    text-decoration: underline;
    transition: color 0.3s ease;
    color: #222A31;
}
.blog-fix-full-text > h2 > div {
        border-bottom: 0.1px solid #6e6b6b !important;
}

    </style>
  </head>
  <body>
  <div class="home-p">
  <?php include 'includes/header.php' ?>
    <div class="blog-fix-singe-post-page">
      <div class="blog-fix-newscreenbody">
        <div class="blog-fix-columnlineeleve">
          <div class="blog-fix-lineeleven_one"></div>
          <div class="blog-fix-container-xs">
            <div class="blog-fix-rowjuly162024">
              <div class="blog-fix-columnjuly16202">
                <p class="blog-fix-july162024 ui text size-textmd." style="font-size: 14px !important;"><?php echo date('F d, Y', strtotime($blog['created_at'])); ?></p>
                <h1 class="blog-fix-howtochange ui heading size-textlg"><?php echo htmlspecialchars($blog['title']); ?></h1>
              </div>
              <div class="blog-fix-stackview">
                <img src="/uploads/<?php echo htmlspecialchars($blog['image']); ?>" alt="Image" class="blog-fix-image" />
              </div>
            </div>
          </div>
        </div>
<!--column Table-->


    <!---->
<div class="instantquote" style="margin:40px auto 0px auto !important;">
  <div class="columnwantan">
    <h4 class="wantan ui heading size-headingmd">Want an immediate quote for shipping your car?</h4>
    <p class="useour ui text size-texts">
      <span class="useour-span">Use our calculator below or give us a <br class="mobile-break"> call at<a href="tel:+18778788008" class="useour-span-1" data-cmp-ab="2"> (*************</a></span>
    </p>
  </div>
  <button class="flex-row-center-center get_an_instant-1" onclick="window.location.href='quote.php'">Get an instant quote</button>
</div>
        <div class="blog-fix-column">
          <div class="blog-fix-container-xs">
            <div class="blog-fix-full-text">
            <?php echo htmlspecialchars_decode($blog['content']); ?>
            </div>
          </div>
        </div>
      </div>
    </div></div>
    <script>
        function toggleResize(element) {
            var parentDiv = element.closest('.blog-fix-columntableof-1');
            
            parentDiv.classList.toggle('collapsed');
        
            if (parentDiv.classList.contains('collapsed')) {
                parentDiv.classList.remove('expanded');
            } else {
                parentDiv.classList.add('expanded');
            }
        }


    </script>
    <?php include 'includes/footer.php'; ?>
  </body>
</html>